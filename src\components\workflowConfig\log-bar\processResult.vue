<!-- 组件日志 -->
<template>
    <div class="components-logs">
        <el-table v-loading="loading" ref="componentsTableRef" v-if="tableData.length" :data="tableData" border
            size="small" height="85%" style="width: 100%">
            <el-table-column min-width="169px" v-for="(item, index) in colums" :prop="item" :label="item" :key="index"
                show-overflow-tooltip>
            </el-table-column>
        </el-table>
        <el-empty class="empty" v-else :image-size="40" description="暂无流程结果信息">
        </el-empty>
        <el-pagination v-if="pageInfo.total > 0" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pageInfo.curPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageInfo.size"
            layout="total, sizes, prev, pager, next, jumper" :total="pageInfo.total">
        </el-pagination>
    </div>
</template>
<script >
import { getTaskIdApi, getCompnentsResult, getCompnentsTaskResult } from '@/api/log.js';
export default {
    created() {
        this.getList()
    },
    data() {
        return {
            loading: false,
            taskId: '',
            tableData: [],
            colums: [],
            pageInfo: {
                page: 1,
                size: 10,
                curPage: 1,
                total: 0
            },
        };
    },

    components: {
    },
    methods: {
        getResult(val) {
            console.log(val)
        },
        getList() {
            this.loading = true
            getTaskIdApi(window.GetQueryValue('workflowId')).then(res => {
                if (res.code == 200) {
                    this.taskId = res.result
                    for (let item of this.$store.state.componentLogs.moduleExecuteLogDTOList) {
                        if (item.moduleCode == 'storageDataBySink') { // 通用存储
                            let params = {
                                taskId: this.taskId,
                                moduleId: item.moduleId,
                                page: this.pageInfo.curPage,
                                size: this.pageInfo.size
                            }
                            getCompnentsTaskResult(params).then(res => {
                                this.colums = []
                                this.pageInfo.total = res.result.total
                                for (let item of res.result.rowList) {
                                    this.colums.push(item.field)
                                }
                                this.tableData = res.result.list
                                this.loading = false
                            })
                        } else if (item.moduleCode == "outstorage") {
                            let params = {
                                taskId: this.taskId,
                                moduleId: item.moduleId,
                                page: this.pageInfo.curPage,
                                size: this.pageInfo.size
                            }
                            getCompnentsResult(params).then(res => {
                                this.colums = []
                                this.pageInfo.total = res.result.total
                                for (let key in res.result.list[0]) {
                                    this.colums.push(key)
                                }
                                this.tableData = res.result.list
                                this.loading = false
                            })
                        }
                    }
                }
                this.loading = false
            })
        },
        handleSizeChange(val) {
            this.pageInfo.size = val
            console.log(this.pageInfo.size);
            this.getList()
        },
        handleCurrentChange(val) {
            this.pageInfo.curPage = val
            this.getList()
            console.log(this.pageInfo.curPage);
        }
    },
};
</script>
<style lang='scss' scoped>
.components-logs {
    height: 100%;

    .empty {
        height: 100%;
        border: 1px #dcdfe6 solid;
    }
}
</style>