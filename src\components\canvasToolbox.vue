<template>
  <section class="canvas-toolbox">
    <el-tooltip class="box-item" effect="dark" :content="isFullScreen ? '退出全屏' : '全屏'" placement="top"
      :enterable="false">
      <em :class="isFullScreen ? 'exitFull' : 'el-icon-full-screen'" class=" toolbox-btn" @click="fullScreen"></em>
    </el-tooltip>
    <el-tooltip class="box-item" effect="dark" content="居中" placement="top" :enterable="false">
      <em class="el-icon-aim toolbox-btn" @click="centerContent"></em>
    </el-tooltip>
    <el-tooltip class="box-item" effect="dark" content="放大" placement="top" :enterable="false">
      <em class="el-icon-zoom-in toolbox-btn" @click="scale(0.2)">
      </em>
    </el-tooltip>

    <el-tooltip class="box-item" effect="dark" content="缩小" placement="top" :enterable="false">
      <em class="el-icon-zoom-out toolbox-btn" @click="scale(-0.2)">
      </em>
    </el-tooltip>

    <el-tooltip class="box-item" effect="dark" content="自动布局" placement="top" :enterable="false">
      <svg-icon class="toolbox-btn" name="auto_layout" color="#999999" v-throttle="[autoLayout, `click`, 300]" />
    </el-tooltip>

    <el-tooltip class="box-item" effect="dark" content="开/关网格" placement="top" :enterable="false">
      <em class="el-icon-s-grid toolbox-btn" @click="gridToggle">
      </em>
    </el-tooltip>
  </section>
</template>

<script>
import { bus } from '@/libs/bus'
export default {
  name: 'canvasToolbox',
  data() {
    return {
      isFullScreen: false
    }
  },
  mounted() {

    bus.$on("fullScreenChange", this.fullScreenChange);
  },
  methods: {
    fullScreenChange(val) {
      this.isFullScreen = val
    },
    scale(level) {
      bus.$emit('scale', level)
    },
    fullScreen() {
      bus.$emit('fullScreen')
    },
    centerContent() {
      bus.$emit('centerContent')
    },
    gridToggle() {
      bus.$emit('gridToggle')
    },
    autoLayout() {
      bus.$emit('autoLayout')
    }
  },
}
</script>

<style lang="scss">
.canvas-toolbox {
  display: flex;
  align-items: center;
  position: absolute;
  padding: 0 5px;
  top: 12px;
  right: 50px;
  height: 44px;
  z-index: 2;
  background: hsla(0, 0%, 100%, .8);

  .toolbox-btn {
    font-size: 18px;
    color: #999999;
    cursor: pointer;
    margin: 0 5px;
  }

  .exitFull {
    width: 18px;
    height: 18px;
    background: url('../assets/images/exitFull.png') no-repeat;
  }
}
</style>