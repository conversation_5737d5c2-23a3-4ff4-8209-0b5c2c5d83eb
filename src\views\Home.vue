<template>
  <div class="home">
    <!-- <div class="task-tab-header">
      <em class="el-icon-back arrow-left"></em>
      <span class="task-name"> 任务名称</span>
    </div> -->
     <menuBar />
     
    <div id="canvas-wrapper" class="flex-wrapper">
      <nodeSelector />
      <canvasToolbox />
      <graphCanvas />

      <rightConfigBar />
      <rightConfigResource />
      <rightConfigDispatch />
      <logBar/>

    </div>
  </div>
</template>

<script>
 import menuBar from '@/components/menuBar.vue';
 import nodeSelector from '@/components/nodeSelector.vue';
 import canvasToolbox from '@/components/canvasToolbox.vue';
 import graphCanvas from '@/components/graphCanvas.vue';
 import rightConfigBar from '@/components/workflowConfig/right-config-bar.vue';
 import rightConfigResource from '@/components/workflowConfig/right-config-resource.vue';
 import rightConfigDispatch from '@/components/workflowConfig/right-config-dispatch.vue';
 import logBar from '@/components/workflowConfig/log-bar/log-bar.vue'
export default {
  name: 'Home',
  components: {
    menuBar,
    nodeSelector,
    canvasToolbox,
    graphCanvas,
    rightConfigBar,
    rightConfigResource,
    rightConfigDispatch,
    logBar
  },
  
  mounted() {
    document.querySelector('#canvas-wrapper').addEventListener('contextmenu', this.canvasPreventDefault)
  },
  methods: {
    canvasPreventDefault(event) {
      event.preventDefault();
    }
  },
  beforeDestroy() {
    document.querySelector('#canvas-wrapper').removeEventListener('contextmenu', this.canvasPreventDefault)
  }
};
</script>

<style lang="scss">

  .home {
    height: 100%;
  }
  .task-tab-header {
    display: flex;
    align-items: center;
    height: 34px;
    line-height: 34px;
    white-space: nowrap;
    background-color: #f2f5fb;
    border-bottom: 1px solid #dee1e5;
    .arrow-left {
      cursor: pointer;
      margin: 0 5px;
    }
  }

  .flex-wrapper {
    display: flex;
    position: relative;
    height: calc(100vh - 50px);
    overflow: hidden;
  }

  .right-config-panel {
    position: absolute;
    top: 0;
    right: -376px;
    bottom: 0;
    z-index: 2;
    width: 360px;
    background: #fff;
    // box-shadow: -6px 10px 40px 0 rgb(0 0 0 / 10%);
    transition: all .5s cubic-bezier(.4,0,.2,1) 0s;
  }
  .right-config-form {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: auto;
  }

</style>