<!DOCTYPE html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="favicon.ico"><title>graph-engine</title><link href="css/app.a2704afb.css" rel="preload" as="style"><link href="css/chunk-vendors.1d6dba7a.css" rel="preload" as="style"><link href="js/app.bfc4b496.js" rel="preload" as="script"><link href="js/chunk-vendors.43d1d745.js" rel="preload" as="script"><link href="css/chunk-vendors.1d6dba7a.css" rel="stylesheet"><link href="css/app.a2704afb.css" rel="stylesheet"></head><body><noscript><strong>We're sorry but graph-engine doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script>function GetQueryValue(queryName) {
      var reg = new RegExp("(^|&)" + queryName + "=([^&]*)(&|$)", "i");
      var r = window.location.href.split('?')[1]?.match(reg);
      if (r != null) {
        return decodeURI(r[2]);
      } else {
        return null;
      }
    }
    window.dataOsToken = GetQueryValue("accountToken");
    window.accountId = GetQueryValue("accountId");
    window.applicationCode = GetQueryValue("applicationCode");</script><script src="./lib/env.js"></script><script src="js/chunk-vendors.43d1d745.js"></script><script src="js/app.bfc4b496.js"></script></body></html>