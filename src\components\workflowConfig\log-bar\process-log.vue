<!-- 流程日志 -->
<template>
  <ul class="process-container" v-loading="loading" ref="processContainerRef">
    <div v-if="$store.state.processLogs.length !== 0">
      <li v-for="item in $store.state.processLogs" :key="item.uuId">{{item.ouput}}</li>
    </div>
    <el-empty class="empty" v-else :image-size="40" description="暂无流程日志信息"></el-empty>
  </ul>
</template>
<script >
import LogBase from './log-base.vue';
export default {
  data() {
    return {
      logs: [],
    };
  },
  extends: LogBase,
  watch: {
    '$store.state.processLogs'(val) {
      if(!val || val.length === 0) return
      this.$nextTick(() => {
        this.scrollToPosition(this.$refs.processContainerRef);
      });
    }
  },
  methods: {
  },
};
</script>
<style lang='scss' scoped>
.process-container {
  height: 100%;
  overflow: auto;
  border-radius: 1px;
  border: 1px solid #dcdfe6;
  font-size: 12px;
  font-weight: 400;
  color: #1a1b1d;
  padding: 16px;
  li {
    line-height: 17px;
    white-space: pre;
  }
  .empty {
    height: 100%;
  }
}
</style>