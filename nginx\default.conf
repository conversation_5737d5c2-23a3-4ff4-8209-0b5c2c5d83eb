server {
    listen       80;
    server_name  localhost;

    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  error;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # location /mpaasweb {
    #     root   /usr/share/nginx/html;
    #     try_files $uri $uri/ /mpaasweb/index.html;
    #     index  index.html index.htm;
    # }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # 开启gzip压缩
    gzip  on;
    # 表示当请求的资源超过 1k 时，才开启压缩
    gzip_min_length 1k;
    # 设置压缩所需要的缓冲区大小
    gzip_buffers 4 16k;
    # 支持压缩的资源类型，对于前后端分离的项目而言，注意下json的压缩支持
    gzip_types text/plain application/x-javascript application/javascript text/css text/html application/xml application/json text/javascript application/x-httpd-php image/jpeg image/gif image/png image/svg+xml;
}