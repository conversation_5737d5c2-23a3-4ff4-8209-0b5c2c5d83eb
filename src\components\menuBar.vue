<template>
  <section class="menu-bar">
    <div class="node-selector-toggle" @click="nodeSelectorHideToggle(!nodeSelectorHide)">
      <span class="selector-btn-text">组件库</span>
      <em class="el-icon-arrow-up selector-btn-icon" :class="{ 'selector-hide': nodeSelectorHide }"></em>
    </div>
    <div class="menu-buttons">
      <a class="menu-btn" @click="refresh"><em class="menu-icon menu-icon-refresh"></em> <span>刷新</span></a>
      <a class="menu-btn" @click="processValid"><em class="menu-icon menu-icon-valid"></em> <span>流程校验</span></a>
      <a class="menu-btn" @click="handleSave()"><em class="menu-icon menu-icon-draft"></em> <span>保存草稿</span></a>
      <a v-if="category === 'FLINK'" class="menu-btn" @click="flinkStartEnd(executeState === 'EXECUTING' && latestTaskId )">
        <em class="menu-icon menu-icon-run"></em> <span>{{latestTaskId && executeState === 'EXECUTING' ? '关闭' : '开启'}}</span>
      </a>
      <a v-else class="menu-btn" @click="handleExecute"><em class="menu-icon menu-icon-run"></em> <span>立即运行</span></a>
      <!-- <a class="menu-btn" @click="handleExecute"><em class="menu-icon menu-icon-run"></em> <span>立即运行</span></a> -->

      <!-- 
        流程状态：
        NEW("新建"),
        DRAFT("草稿"),
        SCHEDULING_WAIT("等待调度"),
        SCHEDULING("调度中"),
        SCHEDULING_COMPLETE("调度完成"),
        SCHEDULING_STOP("停止调度"),
        DELETED("删除"),
        -->
      <!-- <template>
        <a class="menu-btn" @click="handleSchedule"><em class="menu-icon menu-icon-scheduling"></em> <span>执行调度</span></a>
      </template> -->
      <template v-if="workflowStatus != 'SCHEDULING' && workflowStatus != 'SCHEDULING_WAIT' && category !== 'FLINK'">
        <a class="menu-btn" @click="handleSchedule"><em class="menu-icon menu-icon-scheduling"></em>
          <span>执行调度</span></a>
      </template>

      <template v-if="workflowStatus == 'SCHEDULING' || workflowStatus == 'SCHEDULING_WAIT'">
        <a class="menu-btn" @click="handleFreeze"><em class="menu-icon menu-icon-scheduling"></em> <span>停止调度</span></a>
      </template>

    </div>
    <!-- <div class="workflow-buttons">
      <el-button icon="el-icon-document-checked" size="mini" >保存</el-button>
      <el-button type="primary" icon="el-icon-video-play" size="mini">执行</el-button>
    </div> -->

    <!-- 流程校验弹框 -->
    <el-dialog title="流程校验" custom-class="dialog-form" :visible.sync="validDialog" width="648px"
      :close-on-click-modal="false" :close-on-press-escape="false" @close="validDialogClose">
      <div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">未连线组件</div>
            <div class="headRight">
              <em :class="validOption.unConnectModules.length ? 'el-icon-warning' : 'el-icon-success'" /> <span>{{
                  validOption.unConnectModules.length ? validOption.unConnectModules.length + '项问题' : '通过验证'
              }}</span>
            </div>
          </div>
          <div v-if="validOption.unConnectModules.length">
            <el-table max-height="240" :data="validOption.unConnectModules" style="width: 100%"
              :header-cell-style="{ 'background-color': '#F5F7FA', 'color': '#909399' }">
              <el-table-column label="节点名称" prop="name" />
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handlePosition(scope.row)" type="text" size="small">定位</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">未配置组件</div>
            <div class="headRight">
              <em :class="validOption.unConfigModules.length ? 'el-icon-warning' : 'el-icon-success'" /> <span>{{
                  validOption.unConfigModules.length ? validOption.unConfigModules.length + '项问题' : '通过验证'
              }}</span>
            </div>
          </div>
          <div v-if="validOption.unConfigModules.length">
            <el-table max-height="240" :data="validOption.unConfigModules" style="width: 100%"
              :header-cell-style="{ 'background-color': '#F5F7FA', 'color': '#909399' }">
              <el-table-column label="节点名称" prop="name" />
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handlePosition(scope.row)" type="text" size="small">定位</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">
              <span>唯一流程校验</span>
              <el-tooltip class="item" effect="dark" content="流程中只允许出现一条完整的流程，不允许出现一条以上无交集的子流程" placement="top">
                <em class="el-icon-info" />
              </el-tooltip>
            </div>
            <div class="headRight">
              <em :class="validOption.trackUnique ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{
                  validOption.trackUnique ? '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
        </div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">首节点为开始节点</div>
            <div class="headRight">
              <em :class="validOption.startNodeIsValid ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{
                  validOption.startNodeIsValid ? '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
        </div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">尾节点为结束节点</div>
            <div class="headRight">
              <em :class="validOption.finishNodeIsValid ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{
                  validOption.finishNodeIsValid ? '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="validDialog = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 流程监测结果弹框 -->
    <el-dialog title="流程监测结果" custom-class="dialog-form" :visible.sync="monitorDialog" width="648px"
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">流程配置校验</div>
            <div class="headRight">
              <em :class="processConfigVaild ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{ processConfigVaild ?
                  '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
          <div v-if="!processConfigVaild" style="white-space: normal;">
            检测到当前流程未配置完整，请先将流程配置完整。<br>点击流程控制区【流程校验】可查看详细问题。
          </div>
        </div>
        <div class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">资源配置校验</div>
            <div class="headRight">
              <em :class="validOption.executeParamConfigured ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{
                  validOption.executeParamConfigured ? '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
          <div v-if="!validOption.executeParamConfigured" style="white-space: normal;">
            检测到当前流程未配置运行资源，请先到控制台右侧【资源配置】配置流程运行资源。
          </div>
        </div>
        <div v-if="!isNow" class="itemStyle">
          <div class="headStyle">
            <div class="validTitle">调度配置校验</div>
            <div class="headRight">
              <em :class="validOption.scheduleConfigured ? 'el-icon-success' : 'el-icon-warning'" /> <span>{{
                  validOption.scheduleConfigured ? '通过验证' : '未通过验证'
              }}</span>
            </div>
          </div>
          <div v-if="!validOption.scheduleConfigured" style="white-space: normal;">
            检测到当前流程未配置调度信息，请先到控制台右侧【调度配置】配置流程调度信息
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="monitorDialog = false">关 闭</el-button>
      </span>
    </el-dialog>
  </section>

</template>

<script>
import {
  saveWorkflow,
  executeWorkflow,
  scheduleWorkflow, freezeWorkflow, getStatus, getWorkflowInfo, integrityValidate, flinkStop
} from '@/api/https.js'
import { getTaskIdApi, getProcessLogsApi } from '@/api/log.js';
import { mapState, mapMutations } from 'vuex'
import { bus } from "@/libs/bus";
export default {
  name: 'menuBar',
  data() {
    const checkName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入任务名称'));
      } else {
        if (!/^[A-Za-z0-9_\u4e00-\u9fa5]{1,30}$/.test(value)) {
          callback(new Error('支持1-30个字符以内的中文、英文、数字、下划线'));
        } else {
          callback()
        }
      }
    }
    return {
      isNow: false,
      processConfigVaild: false,
      validDialog: false,
      monitorDialog: false,
      workflowId: '',
      scheduleId: '',
      category: '', // 任务类型
      validOption: {
        unConnectModules: [],
        unConfigModules: []
      },
      workflowForm: {
        name: '',
        desc: ''
      },
      workflowFormRules: {
        name: [
          { validator: checkName, trigger: 'blur' }
        ],
      },
      workflowStatus: '',
      latestTaskId: '',
      executeState: ''
    }
  },
  computed: {
    ...mapState(['nodeSelectorHide'])
  },
  created() {
    // this.workflowId = this.$route.query.workflowId
    this.workflowId = window.GetQueryValue('workflowId')
    this.category = window.GetQueryValue('category')
    this.getWorkflowInfo()
    this.getStatus()
    this.getTaskIdApi()
  },
  mounted() {

    bus.$on("scheduleId", this.getScheduleId);
  },
  methods: {
    ...mapMutations(['nodeSelectorHideToggle', 'setCategory']),
    getTaskIdApi () {
      getTaskIdApi(this.workflowId).then(res => {
          if (res.code == 200) {
            this.latestTaskId = res.result
            if(res.result) this.getlogs()
          }
        })
    },
    getlogs () {
      getProcessLogsApi(this.latestTaskId).then(data => {
        this.executeState = data.result.executeState
      })
    },
    flinkStartEnd (type) {
      if (type) { //关闭
        this.flinkStopBtn()
      } else {
        this.handleExecute()
      }
    },
    async flinkStopBtn () {
      const res = await flinkStop({ workflowId: this.latestTaskId })
      if (res.code == 200) {
        this.$message({
          type: 'success',
          message: '关闭成功'
        })
        this.getlogs()
        bus.$emit('getComponentLogsBus')
      }
    },
    getScheduleId(val) {
      this.scheduleId = val
    },
    // 获取流程信息
    async getWorkflowInfo() {
      const res = await getWorkflowInfo({ workflowId: this.workflowId })
      if (res.code == 200) {
        const data = res.result
        this.category = res.result.category
        this.setCategory(res.result.category)
        this.workflowForm = {
          name: data.name,
          desc: data.desc
        }
      }
    },

    //获取流程运行状态
    async getStatus() {
      const res = await getStatus({ workflowId: this.workflowId })
      if (res.code == 200) {
        this.workflowStatus = res.result.status
      }
    },

    //立即执行
    async handleExecute() {
      integrityValidate(this.workflowId).then(async (res) => {
        if (res.code === 200) {
          // console.log(res)
          this.processConfigVaild = res.result.startNodeIsValid && res.result.finishNodeIsValid && res.result.trackUnique && res.result.unConfigModules.length === 0 && res.result.unConnectModules.length === 0
          // this.
          if (this.processConfigVaild && res.result.executeParamConfigured) {
            const res = await executeWorkflow({ workflowId: this.workflowId })
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '流程开始执行'
              })
              bus.$emit('getComponentLogsBus')
              this.executeState = 'EXECUTING'
            }
          } else {
            this.validOption = res.result
            this.monitorDialog = true
            this.isNow = true
          }
        }
      })


      // if (res.code == 451) {
      //   var arr = res.message.split('[')[1].split(']')[0].split(',')
      //   console.log(arr)
      //   bus.$emit('highlight', arr)
      // }
    },
    // 定位
    handlePosition(row) {
      bus.$emit('highlight', row.id)
      this.validDialog = false
    },
    //执行调度
    async handleSchedule() {
      integrityValidate(this.workflowId).then(async (res) => {
        if (res.code === 200) {
          this.processConfigVaild = res.result.startNodeIsValid && res.result.finishNodeIsValid && res.result.trackUnique && res.result.unConfigModules.length === 0 && res.result.unConnectModules.length === 0
          // this.
          if (this.processConfigVaild && res.result.executeParamConfigured && res.result.scheduleConfigured) {
            const res = await scheduleWorkflow({ workflowId: this.workflowId })
            if (res.code == 200) {
              this.getStatus()
              this.$message({
                type: 'success',
                message: '流程调度成功'
              })
              bus.$emit('getComponentLogsBus')
            }
          } else {
            this.validOption = res.result
            this.monitorDialog = true
            this.isNow = false
          }
        }
      })

    },

    //停止调度
    async handleFreeze() {
      const res = await freezeWorkflow({ workflowId: this.workflowId, scheduleId: this.scheduleId })
      if (res.code == 200) {
        this.getStatus()
        this.$message({
          type: 'success',
          message: '停止调度成功'
        })
      }
    },
    handleSave() {
      this.$confirm(`是否确认将${this.workflowForm.name}流程保存为草稿状态，草稿状态下流程调度配置将失效`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        saveWorkflow(this.workflowId).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '保存成功'
            })
          }
        })
      }).catch(() => { });
    },
    processValid() {
      integrityValidate(this.workflowId).then((res) => {
        if (res.code === 200) {
          this.validOption = res.result
          this.validDialog = true
        }
      })
    },
    centerContent() {
      bus.$emit('centerContent')
    },
    refresh() {
      bus.$emit('refresh')
    },
    fullScreen() {
      bus.$emit('fullScreen')
    },
    toRedo() {
      alert(1)
      bus.$emit('toRedo')
    },
    toUndo() {
      alert(2)
      bus.$emit('toUndo')
    },
    validDialogClose() {

    },
    
  },
}
</script>

<style lang="scss">
.el-icon-warning {
  font-size: 16px;
  color: #FF5656;
  margin-right: 16px;
  display: inline-block;
}

.el-icon-success {
  font-size: 16px;
  color: #16C469;
  margin-right: 16px;
  display: inline-block;
}

.el-icon-info {
  font-size: 16px;
  color: #909399;
  margin-left: 16px;
  display: inline-block;
}

.validTitle {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #303133;
}

.itemStyle {
  padding: 10px;
  border: 1px solid #DCDFE6;
}

.headStyle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
}

.headRight {
  min-width: 120px;
  text-align: left;
}

.itemStyle+.itemStyle {
  margin-top: 16px;
}

.menu-bar {
  display: flex;
  align-items: center;
  height: 48px;
  background: #fff;
  border-bottom: 1px solid #e2e2e2;
  white-space: nowrap;
  user-select: none;
}

.node-selector-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 232px;
  border-right: 1px solid #e2e2e2;
  color: #777;
  padding: 0;
  height: 48px;
  cursor: pointer;

  .selector-btn-text {
    display: inline-block;
    width: 100px;
    text-align: center;
  }

  .selector-btn-icon {
    transition: transform .3s;
  }

  .selector-hide {
    transform: rotate(180deg);
  }

  &:hover {
    color: #6882da;
  }
}

.menu-buttons {
  display: inline-flex;
  flex-grow: 1;
  padding: 5px;
}

.menu-btn {
  display: flex;
  align-items: center;
  float: left;
  height: 30px;
  line-height: 30px;
  padding: 0 8px;
  margin: 0 6px 0 0;
  color: #303133;
  font-size: 14px;
  cursor: pointer;
  border-radius: 2px;

  .menu-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background-size: 100%;
  }

  .menu-icon-refresh {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  .menu-icon-save {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  .menu-icon-draft {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  .menu-icon-valid {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  .menu-icon-run {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  .menu-icon-scheduling {
    background-image: url('~@/assets/images/menu_icons/<EMAIL>');
  }

  span {
    margin-left: 5px;
  }

  &:hover {
    background: #f2f5fc;
    text-decoration: none;
  }
}

.workflow-buttons {
  margin-right: 20px;
}

.el-dialog.dialog-form {
  background-color: #fff !important;
  box-shadow: 0px 8px 24px 0px rgba(26, 72, 120, 0.1) !important;

  .el-dialog__header {
    background: #fff;
    border-bottom: 1px solid #EBEEF5;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 400;
      color: #303133;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #909399;
      ;
    }
  }

  .el-dialog__body {
    padding: 32px 24px;
    margin: 0;

    .el-form-item {
      margin-bottom: 13px;
    }
  }

  .el-dialog__footer {
    height: auto;
    padding: 0 24px 25px 0;
    text-align: right;
  }
}
</style>