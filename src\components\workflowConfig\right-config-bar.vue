<!-- 右侧流程配置导航 -->
<template>
  <section class="right-config-bar" :style="{right: $store.state.rightConfigActiveName === '' ? 0 : '361px'}">
    <a :class="{active: $store.state.rightConfigActiveName === '资源配置'}" @click="rightConfigActiveNameChange('资源配置')">资源配置</a>
    <a :class="{active: $store.state.rightConfigActiveName === '调度配置'}" @click="rightConfigActiveNameChange('调度配置')">调度配置</a>
  </section>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
export default {
  name: 'rightConfigBar',
  computed: {
    ...mapState(['category'])
  },
  methods: {
    ...mapMutations(['rightConfigActiveNameChange'])
  }
}
</script>

<style lang="scss" scoped>
.right-config-bar {
  transition: all .5s cubic-bezier(.4,0,.2,1) 0s;
  position: absolute;
    top: 0;
    right: 0;
    width: 32px;
    bottom: 0;
    z-index: 3;
    // background: #f2f5fc;
    // border-left: 1px solid #e2e2e2;
    border-right: 1px solid #DCDFE6;
    // padding: 8px 0 0;
    user-select: none;
    a {
      display: block;
      margin-top: 8px;
      padding: 15px 0;
      text-indent: 5px;
      letter-spacing: 5px;
      width: 32px;
      line-height: 32px;
      writing-mode: vertical-lr;
      text-align: center;
      font-size: 14px;
      color: #606266;
      background: #FFFFFF;
      border-radius: 4px 0px 0px 4px;
      border: 1px solid #DCDFE6;
      cursor: pointer;
      &.active {
        color: #276EB7;
        text-decoration: none;
        background: #E9F0F7;
        border: 1px solid #A9C5E2;
      }
    }
}
</style>
