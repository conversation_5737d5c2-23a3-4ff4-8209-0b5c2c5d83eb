/* 重定义浏览器默认样式
	Name:	style_reset
	Explain: 重定义浏览器默认样式
*/
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
iframe,
dl,
dt,
dd,
ul,
ol,
pre,
form,
button,
input,
textarea,
th,
td,
fieldset {
  font-family: "PingFang SC","Microsoft Yahei", "Hiragino Sans GB", "Helvetica Neue", Helvetica, tahoma, arial, "WenQuanYi Micro Hei", Verdana, sans-serif, "\5B8B\4F53";
  margin: 0;
  padding: 0
}

ul,
ol,
dl {
  list-style-type: none;
}

a {
  text-decoration: none;
  color: white
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

/* 浏览器默认滚动条美化-start */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  width: 6px;
  // background: #e2e2e2;
  // -webkit-border-radius: 2em;
  // -moz-border-radius: 2em;
  // border-radius: 2em;
}

::-webkit-scrollbar-thumb {
  background-color: #e2e2e2;
  background-clip: padding-box;
  min-height: 28px;
  border-radius: 2em;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #cfcfcf;
}
/* 浏览器默认滚动条美化-end */