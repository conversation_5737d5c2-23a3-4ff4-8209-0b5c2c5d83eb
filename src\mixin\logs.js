// 日志mixin---定时获取taskId、组件日志、流程日志
// 1.开启定时器获取taskId--存在（保存变量）--获取组件日志接口、流程日志接口（流程日志面板打开才调用）
// 2.组件日志接口若返回结束标识--taskId不变---停止调用获取组件日志接口、流程日志接口
//                          --taskId变---继续调用获取组件日志接口、流程日志接口
import { getTaskIdApi, getCompnentsLogsApi, getProcessLogsApi } from '@/api/log.js';
import { setInterval } from 'core-js';
import { bus } from "@/libs/bus";
import { v4 as uuidv4 } from 'uuid';
import { processStatusMapping } from '@/libs/types.js'
export default {
  data() {
    return {
      executeState: false, // 流程状态（false未完成）
      taskIdIsChanged: false, // taskId是否发生变化（false未变化）
      timer: null,
      firstGetProcessLogs: true // 初始化获取流程日志（true未初始化获取）
    }
  },
  created() {
    this.handle()
    this.timer = setInterval(() => {
      this.handle()
    }, 10000);
  },
  mounted() {
    bus.$on('getComponentLogsBus', () => {
      this.executeState = false
      this.handle()
    })
  },
  watch: {
    '$store.state.logBarIsOpen'(val) {
      if (val) this.handle();
    }
  },
  methods: {
    // 开始执行
    async handle() {
      await this.getTaskId();
      const taskId = this.$store.state.taskId
      if (!taskId) return;
      const logBarIsOpen = this.$store.state.logBarIsOpen
      if (!this.executeState || (this.executeState && this.taskIdIsChanged)) {
        this.getComponentsLogs(taskId);
        if (logBarIsOpen || this.firstGetProcessLogs) this.getProcessLogs(taskId);
      }
    },
    // 获取TaskId
    async getTaskId() {
      if (window.GetQueryValue('workflowId')) {
        const { result } = await getTaskIdApi(
          window.GetQueryValue('workflowId')
        )
        this.taskIdIsChanged = this.$store.state.taskId === result ? false : true
        if (result) this.$store.commit('setTaskId', result);
      }
    },
    // 获取组件日志信息
    async getComponentsLogs(taskId) {
      const { result } = await getCompnentsLogsApi(taskId)
      this.$store.commit('setComponentLogs', result)
      this.executeState = [
        processStatusMapping.STOP_EXECUTE.value,
        processStatusMapping.COMPLETED.value,
        processStatusMapping.FAIL.value
      ].indexOf(result.executeState) !== -1
    },
    // 获取流程日志信息
    async getProcessLogs(taskId) {
      this.firstGetProcessLogs = false;
      const { result } = await getProcessLogsApi(taskId)
      result.taskExecuteLogList.forEach((item) => {
        item.uuId = uuidv4();
        item.ouput = `${item.recordDatetime} | ${item.level} | ${item.msg}`;
      });
      // this.setExecuteState(result.result.result)
      this.$store.commit('setProcessLogs', result.taskExecuteLogList)
    },
  },
  beforeDestroy() {
    clearInterval(this.timer)
    bus.$off('getComponentLogsBus')
  }
};