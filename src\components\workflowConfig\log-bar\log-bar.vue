<!-- 日志栏组件 -->
<template>
  <section class="log-bar" :style="{ transform: transformHeight, width: logBarWidth }">
    <!-- <div class="drag-line" v-if="getShowDragLine" draggable="true" @mousemove="drag"
      @mousedown="dragstart" @mouseup="dragend"></div> -->
    <span v-if="getShowDragLine" class="log-header drag-line" @mousemove="drag" @mousedown="dragstart"
      @mouseup="dragend">
      <span class="title" @click="clicklogBar">运行日志<img :src="logIcon" alt="" />
        <span class="err-tag" v-if="getErrComponentNum !== 0">
          <i class="el-icon-error c-error"></i>{{ getErrComponentNum }}</span>
      </span>
    </span>
    <span v-else class="log-header">
      <span class="title" @click="clicklogBar">运行日志<img :src="logIcon" alt="" />
        <span class="err-tag" v-if="getErrComponentNum !== 0">
          <i class="el-icon-error c-error"></i>{{ getErrComponentNum }}</span>
      </span>
    </span>
    <div v-if="showLogContainer" class="logbar-container" ref="logBarContainer" v-loading="loading">
      <el-button class="logbar_export" size="small" @click="exportResult">结果导出</el-button>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane lazy label="流程日志" name="processLog">
          <ProcessLog :taskId="taskId" ref="processLogRef" />
        </el-tab-pane>
        <el-tab-pane lazy label="组件日志" name="componentLog">
          <ComponentLog :taskId="taskId" ref="componentLogRef" />
        </el-tab-pane>
        <el-tab-pane v-if="showProcess" lazy label="流程结果" name="processResult">
          <ProcessResult ref="processResult" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </section>
</template>
<script >
import ProcessLog from './process-log.vue';
import ComponentLog from './component-log.vue';
import ProcessResult from './processResult.vue';
import { componentMapping } from '@/libs/types.js';
import { getResult } from '@/api/https'
import { getTaskIdApi } from '@/api/log'
export default {
  created() {

  },
  data() {
    return {
      move: false,
      logIcon: require('@/assets/images/log.png'),
      transformHeight: 'translateY(0px)',
      activeName: 'processLog',
      taskId: '',
      loading: false,
      // 拖拽前el-tabs__content高度
      oldHeight: 200,
      // 日志容器显隐
      showLogContainer: false,
      showProcess: true,
      workflowId: ''
    };
  },
  watch: {
    showLogContainer: {
      handler(newVal) {
        if (newVal) {
          for (let item of this.$store.state.componentLogs.moduleExecuteLogDTOList) {
            if (item.executeState !== "COMPLETED") {
              this.showProcess = false
            }
          }
        }

      }
    }

  },
  computed: {
    // 计算logBar宽度
    logBarWidth() {
      return this.$store.state.rightConfigActiveName === ''
        ? 'calc(100vw - 231px)'
        : 'calc(100vw - 594px)';
    },
    // DragLine显隐
    getShowDragLine() {
      if (this.showLogContainer && this.transformHeight === 'translateY(0px)') {
        document.body.addEventListener('mouseup', this.mouseupClose)
      } else {
        document.body.removeEventListener('mouseup', this.mouseupClose)
      }
      return (
        this.showLogContainer && this.transformHeight === 'translateY(0px)'
      );
    },
    getErrComponentNum() {
      const componentLogs = this.$store.state.componentLogs.moduleExecuteLogDTOList;
      if (!componentLogs || componentLogs.length === 0) return 0;
      const errComps = componentLogs.filter(item => {
        return item.executeState === componentMapping.FAIL.value
      });
      return errComps.length;
    },

  },
  components: {
    ProcessLog,
    ComponentLog,
    ProcessResult
  },
  mounted () {
    this.workflowId = window.GetQueryValue('workflowId')
    
  },
  methods: {
    getTaskId () {
      getTaskIdApi(this.workflowId).then(res => {
        this.taskId = res.result
      })
    },
    exportResult () {
      getTaskIdApi(this.workflowId).then(res => {
        this.taskId = res.result
        getResult(this.taskId)
      })
      
    },
    mouseupClose() {
      const tabsContent = document.querySelector('.log-bar .el-tabs__content');
      this.oldHeight = tabsContent.clientHeight;
      this.move = false
    },
    async clicklogBar() {
      if (!this.showLogContainer) {
        this.showLogContainer = true;
        this.$store.commit('setLogBarIsOpen', true);
      } else {
        // 关闭
        if (this.transformHeight === 'translateY(0px)') {
          this.$store.commit('setLogBarIsOpen', false);
          this.transformHeight = `translateY(${this.$refs.logBarContainer.clientHeight}px)`;
        } else {
          // 打开
          this.$store.commit('setLogBarIsOpen', true);
          this.transformHeight = 'translateY(0px)';
        }
      }
    },
    drag(e) {
      e.preventDefault();
      if (e.clientY === 0 || !this.move) return;
      // e.dataTransfer.dropEffect = 'move';
      const dragsVal = e.clientY - this.dragstartVal;
      const tabsContent = document.querySelector('.log-bar .el-tabs__content');
      tabsContent.style.height = this.oldHeight - dragsVal + 'px';
    },
    dragstart(e) {
      this.move = true
      this.dragstartVal = e.clientY;
    },
    dragend() {
      this.move = false
      const tabsContent = document.querySelector('.log-bar .el-tabs__content');
      this.oldHeight = tabsContent.clientHeight;
      // this.oldHeight = tabsContent.style.height.slice(0, tabsContent.style.height.length - 2);
    },
    handleClick(tab) {
      console.log(tab);
      console.log(this.$store.state.componentLogs.moduleExecuteLogDTOList)
      if (tab.name == "processResult") {
        this.$nextTick(()=>{
          this.$refs.processResult.getList()
        })
        
      }
    }
  },
};
</script>
<style lang='scss' scoped>
.log-bar {
  position: absolute;
  bottom: 0px;
  z-index: 2;
  left: 232px;
  transition: 0.5s;
  background: #fff;

  .drag-line {

    // width: 100%;
    // height: 5px;
    // transition: 0s 0.3s;
    // background: transparent;
    &:hover {
      // background: #409eff;
      // border-bottom: 3px #409eff solid;
      cursor: row-resize;
    }
  }

  .log-header {
    -webkit-user-select: none;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #303133;
    line-height: 22px;
    display: flex;
    align-items: center;
    background: #f5f7fa;
    padding: 5px 16px;

    // user-select: none;
    .title {
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 16px;
        height: 16px;
        margin-left: 8px;
      }

      .err-tag {
        margin-left: 8px;
        font-size: 12px;
      }
    }
  }

  .logbar-container {
    padding: 12px 16px;
    position: relative;
    .logbar_export{
      position: absolute;
      right: 18px;
      top: 8px;
      z-index: 99;
    }
  }

  ::v-deep .el-tabs__content {
    height: 200px;
    min-height: 100px;
    max-height: 60vh;
  }

  ::v-deep .el-tabs__item {
    height: 36px;
    line-height: 36px;
    padding: 0px 16px;
  }

  .el-tab-pane {
    height: 100%;
  }
}
</style>