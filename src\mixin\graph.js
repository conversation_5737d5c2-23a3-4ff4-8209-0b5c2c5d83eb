import { DagreLayout } from '@antv/layout'
import { updatePosition } from "@/api/https.js";

export default {
  methods: {
    // layout方式渲染画布
    renderLayoutGraph(nodes, edges) {
      this.isDagreLayout = false
      const dagreLayout = new DagreLayout({
        type: 'dagre',
        rankdir: 'LR',
        ranksep: 50,
        nodesep: 25,
      })
      const model = dagreLayout.layout({
        nodes,
        edges
      })
      this.graph.fromJSON(model);

      // 保存自动布局后位置信息
      const layoutModelPositions = model.nodes.map(item => {
        return {
          id: item.id,
          x: item.x,
          y: item.y
        }
      })
      this.updateModelPosition(layoutModelPositions)
    },

    // 更新组件位置信息
    async updateModelPosition(modelPositions) {
      if(modelPositions.length > 0) {
        const res = await updatePosition(modelPositions)
        if (res.code != 200) {
          this.getWorkflow()
        }
      }
      
    },

    // 根据组件日志返回的组件状态绑定到画布
    bindComponentStatus() {
      const cells = this.graph.getCells()
      if (!cells || cells.length === 0) return
      const { moduleExecuteLogDTOList, executeState } = this.$store.state.componentLogs
      cells.forEach((item) => {
        if (!item.data) return
        const logData = moduleExecuteLogDTOList.filter(logItem => logItem.moduleId === item.id)[0]
        let moduleStatus = item.data.executeStatus
        if (logData) {
          moduleStatus = moduleStatus === 'UNSETING' ? 'UNSETING' : logData.executeState
        } else {
          // 任务状态为”提交中“、”待执行“、”执行中“时，组件状态默认”未运行“
          if (['SUBMITTING', 'UN_EXECUTE', 'EXECUTING'].includes(executeState)) moduleStatus = 'NOTRUNNING'
        }
        item.prop('data', { ...item.data, executeStatus: moduleStatus })
      })
    },

    // 入参父组件id矫正（修复：更改父组件后，入参id还是旧父组件id问题）
    // 逻辑：入参[index].id = parentJson[index].id
    correctParentId(data) {
      if (!data || data.length <= 0) return
      const parentJson = this.nodeData.parentJson
      data.forEach((item, index) => {
        if (parentJson[index]) {
          item.id = JSON.parse(parentJson[index])[0].id
        }
      })
    }
  },
};