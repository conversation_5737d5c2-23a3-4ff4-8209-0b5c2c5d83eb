<template>
  <section class="node-selector" :class="{ 'node-selector-hide': nodeSelectorHide }">
    <div class="node-search-wrap">
      <el-input prefix-icon="el-icon-search" size="mini" placeholder="请输入组件关键字" v-model="keyword" clearable></el-input>
    </div>

    <div class="node-wrap">
      <div class="node-group" v-for="(items, index) in componentsData" :key="index">
        <h4 @click="shrink(index)">
          {{ items.category }}
          <em :class="'icon' + index" class="el-icon-caret-top"></em>
        </h4>
        <ul>
          <template v-for="item in items.modules">
            <el-tooltip popper-class="tool-tip-box" effect="dark" :open-delay="500" placement="right" :enterable="false"
              :key="item.identifier">
              <div slot="content" class="tool-tip-content">
                {{ `组件名称:${item.displayName}` }}<br>
                {{ item.moduleDescription ? `组件描述:${item.moduleDescription}` : '组件描述:暂无描述' }}
              </div>
              <li :key="item.identifier" @mousedown="dragNode($event, item)">
                <img class="node-icon" :src="item.img" alt="">
                <!-- <span
                  class="node-icon"
                  :style="{backgroundImage: `url(./icon/${item.startFlag || item.endFlag ? 1 : 2}.svg)`}"
                ></span> -->
                <span class="node-label">{{ item.displayName }}</span>
              </li>
            </el-tooltip>
          </template>
        </ul>
      </div>
    </div>
  </section>
</template>

<script>
import { mapState } from "vuex";
import { bus } from "@/libs/bus";
import { getApplication } from "@/api/https.js";

export default {
  name: "nodeSelector",
  data() {
    return {
      componentsList: [],
      keyword: "",
    };
  },
  computed: {
    componentsData() {
      const data = JSON.parse(JSON.stringify(this.componentsList));
      if (this.keyword.trim() === "") {
        return data;
      } else {
        return data.filter(items => {
          const modules = items.modules;
          if (modules.length) {
            const itemList = modules.filter(item => {
              return item.name.includes(this.keyword);
            });
            items.modules = itemList;
          }
          return items.modules.length;
        });
      }
    },
    ...mapState(["nodeSelectorHide"])
  },
  watch: {
    componentsData() {
      this.$nextTick(() => {
        this.componentsData.forEach((item, index) => {
          document.querySelectorAll('.node-group')[index].style.height = 'auto'
          document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-top`
        })
      })
    }
  },
  async created() {
    console.log(window.GetQueryValue('category'))
    const res = await getApplication({ workflowCategory: window.GetQueryValue('category') });
    if (res.code == 200) {
      res.result.forEach(x => {
        x.modules.forEach(y => {
          y.name = y.displayName
          // 非开发环境去掉地址域名
          if (process.env.NODE_ENV === 'development') {
            y.img = this.formatUrl(y.img)
            y.webUrl = this.formatUrl(y.webUrl)
          }
        })
      })
      this.componentsList = res.result;
    }
    bus.$emit('componentsList', this.componentsList);
  },
  methods: {
    dragNode(event, item) {
      bus.$emit("dragNode", event, item);
      // console.log(item, "item");
    },
    shrink(index) {
      if (document.querySelectorAll('.node-group')[index].offsetHeight > 24) {
        document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-bottom`
        document.querySelectorAll('.node-group')[index].style.height = '24px'
      } else {
        document.querySelectorAll('.node-group')[index].style.height = 'auto'
        document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-top`
      }
    },
    // 去掉地址域名
    formatUrl(url) {
      const reg = /https?:\/\/[^/]*\//
      const formatedUrl = url.split(reg)[1]
      if (!formatedUrl) return url
      else return '/' + formatedUrl
    },
  }
};
</script>

<style lang="scss" scoped>
.node-selector {
  position: absolute;
  z-index: 4;
  top: 0;
  left: 0;
  width: 232px;
  height: 100%;
  opacity: 1;
  background: #fff;
  transition: all 0.4s;
  border-right: 1px solid #e2e2e2;
  user-select: none;
  overflow: hidden;

  .node-wrap {
    height: calc(100% - 43px);
    overflow: auto;
    // border-top: 1px solid #e2e2e2;
    // box-sizing: border-box;
  }

  .node-search-wrap {
    display: flex;
    align-items: center;
    background-color: #f2f5fc;
    border-bottom: 1px solid #e2e2e2;
    height: 43px;
    overflow: hidden;
    padding: 0 7px;
  }

  .node-search {
    width: auto;
    margin: 7px;
  }
}

.node-selector-hide {
  opacity: 0;
  height: 0;
}

.node-group {
  overflow: hidden;
  height: auto;
  transition: all 0.3s;
  margin-bottom: 8px;

  h4 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    background: #f2f5fc;
    padding: 0 16px;
    color: #333;
    cursor: pointer;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
  }

  li {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    width: 96px;
    height: 80px;
    margin: 8px 8px 0px 8px;
    background: #fff;
    cursor: pointer;
    position: relative;

    &:hover {
      background: #f7f7f7;
    }

    .node-icon {
      display: block;
      width: 32px;
      height: 32px;
      margin: 0 auto;
      background-size: 32px;
      background-repeat: no-repeat;
      background-position: 50%;
      // background-image: url("/icon/2.svg");
    }

    .node-label {
      display: block;
      height: 32px;
      font-size: 12px;
      line-height: 32px;
      color: #333;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis
    }
  }
}

.tool-tip-box {
  background-color: #464c59 !important;
}

.tool-tip-box[x-placement^="right"] .popper__arrow {
  border-color: transparent !important;
  border-right-color: #464c59 !important;
}

.tool-tip-box[x-placement^="right"] .popper__arrow::after {
  border-color: transparent !important;
  border-right-color: #464c59 !important;
}

.tool-tip-box[x-placement^="left"] .popper__arrow {
  border-color: transparent !important;
  border-left-color: #464c59 !important;
}

.tool-tip-box[x-placement^="left"] .popper__arrow::after {
  border-color: transparent !important;
  border-left-color: #464c59 !important;
}

.tool-tip-content {
  max-width: 200px;
  line-height: 25px;
}
</style>