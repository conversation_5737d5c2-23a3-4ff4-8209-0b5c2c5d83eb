import feach from '../libs/feach'

const baseUrl = window.applicationServerPath
// const dataOsUrl = window.dataos_urlDaasMeta
// const jobUrl = window.executorgover


// dataos可运行资源查询
export function dataosResource(params) {
  return feach({
    url: baseUrl + `/api/v2/common/resources/${params.resourceType}`,
    method: 'get',
  })
}

// 可运行资源查询
export function getResourceList(params) {
  return feach({
    url: baseUrl + '/api/v2/resource/dataos',
    method: 'get',
    params
  })
}

//清空资源配置
export function clearConfig(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/resource/config/delete`,
    method: 'post',
    // params
  })
}

// 当前流程资源配置查询
export function getConfig(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/runtime`,
    method: 'get',
    // params
  })
}

// 保存资源配置
export function updateConfig(data, workflowId) {
  return feach({
    // url: baseUrl + '/api/v2/workflow/execute/addConfig',
    url: baseUrl + `/api/v2/workflows/${workflowId}/runtime`,
    method: 'post',
    data
  })
}

// 资源配置项查询
export function resourceConfig(params) {
  return feach({
    url: baseUrl + `/api/v2/common/workflow/execute/config/item/${params.workflowCategory}`,
    method: 'get',
    // params
  })
}

// 调度配置
// 新增调度
export function scheduleAdd(data, workflowId) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${workflowId}/schedules`,
    method: 'post',
    data
  })
}

// 更新调度
export function scheduleUpdate(data, workflowId, scheduleId) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${workflowId}/schedules/${scheduleId}/update`,
    method: 'post',
    data
  })
}

// 查询调度信息
export function scheduleInfo(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/schedules`,
    method: 'get',
    // params
  })
}

// 清空调度信息
export function scheduleClear(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/schedules/${params.scheduleId}/delete`,
    method: 'post',
    // params
  })
}

// 校验cron表达式
export function cronValid(params) {
  return feach({
    url: baseUrl + '/api/v2/common/cron/valid',
    method: 'post',
    params
  })
}

// 解析cron表达式
export function commonCron(params) {
  return feach({
    url: baseUrl + '/api/v2/common/cron',
    method: 'get',
    params
  })
}
