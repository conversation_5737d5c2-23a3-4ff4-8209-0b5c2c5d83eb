<template>
  <div>
    <section id="container" class="theCanvas" :style="nodeSelectorHide ? 'margin-left: 0;' : 'margin-left: 232px;'">
    </section>
    <contextMenu :position="contextMenuPosition" :visible="contextMenuVisible" :currentNode="currentNode"
      @contextMenuEvent="contextMenuEvent" ref="contextMenuRef" />
    <!--弹窗组件-->
    <el-dialog :append-to-body="true" :close-on-click-modal="false" :modal-append-to-body="false"
      :visible.sync="dialogVisible" :width="dialogWidth" custom-class="task-config" v-if="dialogVisible">
      <div class="header-title" slot="title">
        <span class="tit">{{ templateTitle }}</span>
        <span style="font-size:14px" v-if="templateTitle == '选取数据'">
          <img height="20px" src="../assets/images/warning.png"
            style="position:relative;top:4px;margin:0 12px 0 60px" />目前仅支持选取已落地式接入的结构化数据
        </span>
      </div>
      <iframe :src="componentUrl" @load="loadandpostmessage" class="frame-layout" frameborder="0" height="100%"
        marginheight="0" marginwidth="0" ref="myApplication" width="100%"></iframe>
      <span class="dialog-footer" slot="footer" v-if="dialogWidth != '30%'">
        <el-button @click="dialogVisible = false" type="info">取消</el-button>
        <el-button :disabled="loading" @click="onConfirmation" type="primary" v-popover:popover>确认</el-button>
        <el-tooltip class="item" popper-class="toolItemBig" effect="light" :enterable="true" placement="right-end"
          :key="100">
          <div slot="content">
            <div v-if="desc.function">
              <h3 class="popper-title">查看功能说明</h3>
              <div class="poper-content">
                <div class="function">
                  <h3 class="popper-title">功能：</h3>
                  <p class="popper-body">{{ desc.function }}</p>
                </div>

                <div class="popper-item input" v-if="desc.input.length > 0">
                  <h3 class="popper-title">输入：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.input">{{ item }}</p>
                </div>

                <div class="popper-item output" v-if="desc.output.length > 0">
                  <h3 class="popper-title">输出：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.output">{{ item }}</p>
                </div>

                <div class="popper-item ability">
                  <h3 class="popper-title">组件能力：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.ability">{{ item }}</p>
                </div>
              </div>

            </div>
            <p v-else class="popper-body">暂无内容，待更新</p>
          </div>
          <div class="checkButton">

            <span class="text" ref="showDesc"><em class="el-icon-info"> 查看功能说明</em></span>
          </div>
        </el-tooltip>
      </span>
      <span class="dialog-footer" slot="footer" v-if="dialogWidth == '30%'">
        <el-button :disabled="loading" @click="onConfirmation" type="primary">确认</el-button>
        <el-tooltip class="item" popper-class="toolItemBig" effect="light" :enterable="true" placement="right-end"
          :key="100">
          <div slot="content">
            <div v-if="desc.function">

              <h3 class="popper-title">查看功能说明</h3>
              <div class="poper-content">
                <div class="function">
                  <h3 class="popper-title">功能：</h3>
                  <p class="popper-body">{{ desc.function }}</p>
                </div>

                <div class="popper-item input" v-if="desc.input.length > 0">
                  <h3 class="popper-title">输入：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.input">{{ item }}</p>
                </div>

                <div class="popper-item output" v-if="desc.output.length > 0">
                  <h3 class="popper-title">输出：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.output">{{ item }}</p>
                </div>

                <div class="popper-item ability">
                  <h3 class="popper-title">组件能力：</h3>
                  <p :key="index" class="popper-body" v-for="(item, index) in desc.ability">{{ item }}</p>
                </div>
              </div>

            </div>
            <p v-else class="popper-body">暂无内容，待更新</p>
          </div>
          <div class="checkButton">

            <span class="text" ref="showDesc"><em class="el-icon-info"> 查看功能说明</em></span>
          </div>
        </el-tooltip>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { mapMutations, mapState } from 'vuex'
import { Graph, Addon, Shape, Path } from "@antv/x6";
const { Dnd } = Addon;
import "@antv/x6-vue-shape";
import { bus } from "@/libs/bus";
import { getWorkflow, deleteConnInfo, nodeAdd, edgeAdd, queryPreviousAndSelfParam, updateFunctionParam } from "@/api/https.js";
import myShape from "./shape.vue";
import contextMenu from "./contextMenu.vue";
import logs from '@/mixin/logs.js'
import graphMixin from '@/mixin/graph.js'
export default {
  name: "graphCanvas",
  mixins: [logs, graphMixin],
  components: {
    contextMenu
  },
  data() {
    Graph.registerConnector(
      'algo-connector',
      (s, e) => {
        const offset = 4
        const deltaX = Math.abs(e.x - s.x)
        const control = Math.floor((deltaX / 3) * 2)

        const v1 = { x: s.x + offset + control, y: s.y }
        const v2 = { x: e.x - offset - control, y: e.y }

        return Path.normalize(
          `M ${s.x} ${s.y}
          L ${s.x + offset} ${s.y}
          C ${v1.x} ${v1.y} ${v2.x} ${v2.y} ${e.x - offset} ${e.y}
          L ${e.x} ${e.y}
          `,
        )
      },
      true,
    )
    return {
      lastTargetCell: '',
      isFullScreen: false,
      navList: [],
      codeList: [],
      dialogVisible: false,
      dialogWidth: '85%',
      templateTitle: '',
      componentUrl: '',
      loading: true,
      desc: {
        function: '',
        input: [],
        output: [],
        ability: []
      },
      showGrid: false,
      knob: null,
      container: null,
      headHeight: 74, // 页面顶部任务名称和菜单栏高度
      graph: null, // 画布实例
      dnd: null, // 拖拽插件实例
      resizeTimer: null,
      contextMenuPosition: { // 右键鼠标位置
        x: 0,
        y: 0
      },
      contextMenuVisible: false, // 是否显示右键菜单
      nodeData: {}, // 节点传递数据
      updateStatus: false, // 更新节点时 是否给出提示
      modList: [], // 组件信息
      connList: [], // 连线信息
      notInitModList: [], // 未配置的组件id列表
      workflowId: "09c3831852194974a72db1b81ba04154", // 流程id

      currentNode: {}, // 当前右键选中的元素数据
      // Graph是否自动布局
      isDagreLayout: false,
    };
  },
  computed: {
    ...mapState(["nodeSelectorHide"])
  },
  watch: {
    'nodeSelectorHide'(val) {
      if (val) {
        this.graph.resize(window.innerWidth)
      } else {
        this.graph.resize(window.innerWidth - 232)
      }
    },
    '$store.state.componentLogs'() {
      this.bindComponentStatus()
    }
  },
  created() {
    this.workflowId = window.GetQueryValue('workflowId')
    // this.workflowId = this.$route.query.workflowId
  },
  mounted() {
    window.addEventListener('resize', () => {
      if (!this.checkFull()) {
        this.isFullScreen = false
      } else {
        this.isFullScreen = true
      }
      bus.$emit('fullScreenChange', this.isFullScreen)
    })
    window.onmessage = e => {
      // debugger
      // console.log(e,"接收的数据")
      // 提交配置
      if (e.data && e.data.name == 'confirmConfig') {
        if (e.data.type === 'hongfu') {
          this.isHongfu = true
        } else {
          this.isHongfu = false
        }
        // this.data.nodeList.find(n => n.id === e.data.id).runStatus = 3
        this.configSubmit(e.data)
        // const resData = JSON.parse(e.data.data)
        // if (resData[0].dataType) {
        //   // 接入选组组件
        //   const allConnections = this.jsPlumb.getConnections()
        //   const sourceNodeData = this.data.nodeList.find(
        //     n => n.code === 'chooseShareData' || n.code === 'chooseShareDataSpark'
        //   )
        //   this.dataType = resData[0].dataType
        //   sourceNodeData.dataType = resData[0].dataType
        //   if (sourceNodeData.connList.length > 0) {
        //     const targetNodeData = this.data.nodeList.find(n => n.id === sourceNodeData.connList[0].targetNodeId)
        //     if ((resData[0].dataType === 'file' && targetNodeData.code !== 'resourceAnalysis') || (resData[0].dataType === 'dataBase' && targetNodeData.code === 'resourceAnalysis')) {
        //       this.$message.error(resData[0].dataType === 'file' ? '数据服务接入组件选取excel文件后只能连接Excel解析组件！' : '数据服务接入组件选取表数据后不能连接Excel解析组件！')
        //       allConnections.forEach(item => {
        //         if (
        //           item.sourceId === sourceNodeData.connList[0].sourceNodeId &&
        //           item.targetId === sourceNodeData.connList[0].targetNodeId
        //         ) {
        //           this.jsPlumb.deleteConnection(item)
        //         }
        //       })
        //     }
        //   }
        // }
        if (e.data.component == 'storeupdata') {
          this.storeData = e.data.data // 这里保存下data，俊杰说确认配置成功后，需要调一个存储的接口，接口中需要这些数据
          // console.log(this.storeData)
        }
      }
      // 接入新数据->跳转
      if (e.data.name == 'openData') {
        window.parent.postMessage(
          {
            name: 'toData',
            data: '',
            component: ''
          },
          '*'
        )
      }
      // 获取组件帮助配置文案
      if (e.data.name === 'desc') {

        this.desc = e.data.data
      }
    }


    bus.$on('componentsList', this.componentsList);
    bus.$on("dragNode", this.dragNode);
    bus.$on("centerContent", this.centerContent);
    bus.$on("scale", this.scale);
    bus.$on("gridToggle", this.gridToggle);
    bus.$on("autoLayout", this.autoLayout);
    bus.$on("refresh", this.refresh);
    bus.$on("fullScreen", this.fullScreen);

    bus.$on("toRedo", this.toRedo);
    bus.$on("toUndo", this.toUndo);

    bus.$on("highlight", this.highlight);
    window.addEventListener("resize", this.onResize, false);
    this.$nextTick(() => {
      this.initCanvas()
      this.initAddon()
      // this.getWorkflow()
    })
  },

  methods: {
    componentsList(data) {
      window.componentsUrl = []
      // const that = this
      this.navList = data
      data.forEach(function (item) {
        // that.codeList.push({
        //   img: item.img,
        //   code: item.code,
        //   name: item.name,
        //   color: item.color,
        //   status: 0
        // })
        item.modules.forEach((v) => {
          window.componentsUrl.push(v)
        })
      })
      this.getWorkflow()
    },
    initCanvas() {
      const self = this
      const container = document.getElementById("container");
      this.container = container;
      this.graph = new Graph({
        // selecting: true,
        container: container,
        width: window.innerWidth - 232,
        height: window.innerHeight - 74,
        background: {
          color: '#fff', // 设置画布背景颜色
        },
        panning: true,
        // scroller: true,
        history: {
          enabled: true, //历史记录
          ignoreChange: true //ignoreChange 是否忽略属性变化
        },
        snapline: true,
        selecting: {
          enabled: true,
          // enabled: true,
          // multiple: true,
          // // rubberband: true,
          // movable: true,
          // showNodeSelectionBox: true,
        },
        highlighting: {
          magnetAvailable: {
            name: "stroke",
            args: {
              attrs: {
                fill: "#fff",
                stroke: "#47C769"
              }
            }
          },
          magnetAdsorbed: {
            name: "stroke",
            args: {
              attrs: {
                fill: "#fff",
                stroke: "#31d0c6"
              }
            }
          }
        },
        connecting: {
          allowPort: true, //是否允许边链接到链接桩
          allowEdge: false, //是否允许边链接到另一个边
          allowNode: false, //是否允许边链接到节点（非节点上的链接桩)
          allowLoop: false, //是否允许创建循环连线，即边的起始节点和终止节点为同一节点
          allowMulti: false, //是否允许在相同的起始节点和终止之间创建多条边
          allowBlank: false, //是否允许连接到画布空白位置的点
          highlight: true, //拖动边时，是否高亮显示所有可用的连接桩或节点
          connector: 'algo-connector',
          sourceAnchor: 'right',
          targetAnchor: 'left',
          connectionPoint: 'anchor',
          // 点击链接桩时，根据此函数返回值来判断是否新增边, 函数返回 true 可以新增，返回false不可以新增
          // validateMagnet({ cell }) {
          //   // "maxConnections" => 多输入标识: 1.单输入；2.多输入
          //   // "maxOutput" => 多输出标识: 1.单输出；2.多输出

          //   const { id, maxOutput, name } = cell.getData();
          //   if (parseInt(maxOutput) === 2) {
          //     return true
          //   }

          //   const edges = self.graph.getEdges();
          //   for(let edge of edges) {
          //     const { cell: sourceId } = edge.source
          //     if (sourceId === id) {
          //        self.$message({
          //           message: `组件${name} 不支持多输出！`,
          //           type: 'warning'
          //         });
          //        return false
          //     }
          //   }

          //   return true
          // },
          validateConnection({ targetMagnet }) {
            if (targetMagnet.getAttribute("port-group") !== "in") {
              return false;
            }
            return true;
          },

          // 新增连线
          validateEdge({ edge }) {
            let validateEdgeStatus = true;
            const nodeId = edge.target.cell;
            const targetCell = self.graph.getCellById(nodeId);
            // "maxConnections" => 多输入标识: 1.单输入；2.多输入
            // "maxOutput" => 多输出标识: 1.单输出；2.多输出
            const { id, maxConnections, name } = targetCell.getData();

            if (maxConnections === 1) {
              const edges = self.graph.getEdges();
              let num = 0;
              for (let i = 0; i < edges.length; i++) {
                const { cell: targetId } = edges[i].target
                if (targetId === id) {
                  num++
                }
                if (num > 1) {
                  self.$message({
                    message: `组件${name} 不支持多输入！`,
                    type: 'warning'
                  });
                  validateEdgeStatus = false
                  return validateEdgeStatus
                }
              }
            }
            edge.attr("line/strokeDasharray", 0);
            const params = {
              from: edge.source.cell,
              to: edge.target.cell,
            }

            edgeAdd(params).then((res) => {
              if (res.code == 200) {
                var sourceCell = self.graph.findViewByCell(edge.source.cell)
                sourceCell.removeClass('tag-highlight')
                // validateEdgeStatus = true
                // return validateEdgeStatus;
              } else {
                self.getWorkflow()
              }
            })

            return validateEdgeStatus
          },

          // 自动吸附
          snap: {
            radius: 50
          },
          createEdge() {
            return new Shape.Edge({
              router: {
                name: 'manhattan',
                args: {
                  step: 30,
                },
              },
              anchor: 'center',
              connectionPoint: 'anchor',
              allowBlank: false,
              snap: {
                radius: 20,
              },
              attrs: {
                line: {
                  stroke: "#33aa98",
                  strokeWidth: 1.5,
                  strokeDasharray: 5,
                  targetMarker: {
                    name: "classic",
                    width: 7,
                    height: 7
                  }
                }
              }
            });
          }
        },
        grid: {
          size: 10, // 网格大小 10px
          visible: false, // 渲染网格背景
          type: 'mesh'
        },
        mousewheel: {
          enabled: true,
          // modifiers: "ctrl",
          factor: 1.1,
          maxScale: 1.8,
          minScale: 0.5
        }
      });
      // 设置默认缩放级别
      this.graph.zoom('0.8')

      Graph.registerNode("shape", {
        inherit: "vue-shape",
        x: 200,
        y: 150,
        width: 50,
        height: 50,
        label: "aaa",
        component: {
          template: `<my-shape />`,
          components: {
            myShape
          }
        },
      });

      this.graph.on("blank:mousedown", () => {
        this.rightConfigActiveNameChange('')
      })
      this.graph.on('blank:click', () => {
        this.contextMenuVisible = false;
      })
      this.graph.on('cell:click', () => {
        this.contextMenuVisible = false;
      })
      this.graph.on('cell:change:*', () => {
        this.contextMenuVisible = false;
      })
      this.graph.on('scale', () => {
        this.contextMenuVisible = false;
      })
      this.graph.on('resize', () => {
        this.contextMenuVisible = false;
      })
      this.graph.on('translate', () => {
        this.contextMenuVisible = false;
      })

      this.graph.on("node:contextmenu", ({ e, node }) => {
        if (this.lastTargetCell) {
          this.lastTargetCell.removeClass('tag-highlight')
        }
        // console.log(node.getPosition(), 'node')
        // const nodeData = node.getData()
        // console.log(node.getData(), "contextmenu");
        // const { num } = node.getData();
        // node.setData({
        //   num: num + 1
        // });
        this.currentNode = node
        const p = this.graph.clientToGraph(e.clientX, e.clientY)
        this.contextMenuPosition = p;
        this.contextMenuVisible = true;
      });

      this.graph.on('node:dblclick', async ({ cell }) => {
        this.dbOrrightConfig(cell)
      });
      this.graph.on("edge:dblclick", ({ cell }) => {
        this.graph.removeEdge(cell)
        this.removeEdge(cell)
      });

      // 组件拖动改变位置时更新位置信息
      this.graph.on("node:moved", async ({ node }) => {
        const { x, y } = node.position()
        const params = [{
          id: node.id,
          x,
          y
        }]
        this.updateModelPosition(params)
      });



      // 控制连接桩显示/隐藏
      this.graph.on('node:mouseenter', () => {
        // debugger
        // const container = document.getElementById("container")
        const ports = container.querySelectorAll('.x6-port-body')
        this.showPorts(ports, true)
      })
      this.graph.on('node:mouseleave', () => {
        // const container = document.getElementById("container")
        const ports = container.querySelectorAll('.x6-port-body')
        this.showPorts(ports, false)
      })
    },
    showPorts(ports, show) {
      for (let i = 0, len = ports.length; i < len; i = i + 1) {
        ports[i].style.visibility = show ? 'visible' : 'hidden'
      }
    },
    highlight(id) {
      if (this.lastTargetCell) {
        this.lastTargetCell.removeClass('tag-highlight')
      }
      var targetCell = this.graph.findViewByCell(id)
      this.lastTargetCell = targetCell

      targetCell.addClass('tag-highlight')
      // targetCell.highlight(targetCell.container, {
      //   highlighter: {
      //     name: 'className'
      //   },
      // })
      this.graph.matrix(null)
      this.graph.centerCell(targetCell)
    },
    initAddon() {
      const self = this;
      this.dnd = new Dnd({
        target: this.graph,
        scaled: false,
        animation: true,

        // 新增组件
        validateNode(node) {
          const { x, y } = node.position()
          // if (x < 232) return false; // 拖动组件未离开左侧菜单区域时，不允许添加
          const {
            // applicationCode, // 组件分类
            code, // 组件code
            identifier, // 组件id
            instanceCode = '', // 实例code
            name: moduleName, // 组件名称
            maxOutputs, // 是否支持多输出， 1：单输出，2：多输出
            maxConnections, // 是否支持多输入， 1：单输入，2：多输入
            startFlag, // 是否是输入组件，1：是，0：否
            endFlag, // 是否是输出组件， 1：是，0：否
            webUrl, // 后端地址（只到后端项目地址）
          } = node.getData();
          const data = {
            // applicationCode,
            code,
            identifier,
            instanceCode,
            moduleName,
            maxOutputs,
            maxConnections,
            startFlag,
            endFlag,
            webUrl,
            workflowId: self.workflowId, // 流程id
            x,
            y,
          }
          const params = {
            code,
            identifier,
            moduleName,
            workflowId: self.workflowId, // 流程id
            x,
            y,
          }
          node.data = data
          return new Promise((resolve, reject) => {
            nodeAdd(params).then((res) => {
              if (res.code == 200) {
                node.id = res.result
                node.data.id = res.result
                node.data.executeStatus = 'UNSETING'
                resolve(true)
              } else {
                self.getWorkflow()
                reject(false)
              }
            })
          })
        },
        getDragNode: node => {
          const data = node.data;
          return this.graph.createNode({
            width: 85,
            height: 70,
            shape: "html",
            data,
            html: () => {
              const wrap = document.createElement("div");
              wrap.className = "drag-node-item";
              wrap.innerHTML = `
                <img class="node-icon" src="${data.img}" />
                <span class="node-label">${data.name}</span>
                `;
              return wrap;
            }
          });
          // return node.clone({ keepId: true })
        },
        getDropNode: node => {
          // const { width, height } = node.size()
          // // 返回一个新的节点作为实际放置到画布上的节点
          // return node.clone().size(width * 3, height * 3)
          //return node.clone({ keepId: true })
          const { x, y } = node.getPosition()
          return this.graph.createNode({
            width: 60,
            height: 67,
            // label: "接入组件接入组件",
            // attrs: {
            //   label: {
            //     text: node.data.name,
            //     fontSize: 14,
            //     refY: 70 // x 轴偏移，类似 css 中的 margin-left
            //     //textAnchor: 'center', // 左对齐
            //   }
            // },
            shape: "shape",
            x,
            y,
            data: node.data,
            ports: {
              groups: {
                // 输入链接桩群组定义
                in: {
                  // position: {
                  //   name: 'absolute',//核心
                  //   args: { x: 30, y: 60 }
                  // },
                  position: "left",
                  attrs: {
                    circle: {
                      r: 7,
                      //magnet: true,
                      magnet: "passive",
                      stroke: "#5e96fe",
                      strokeWidth: 1,
                      fill: "#fff"
                      // style: {
                      //   visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示
                      // },
                    },
                    line: {
                      stroke: "#722ed1"
                    }
                  }
                },
                // 输出链接桩群组定义
                out: {
                  position: "right",
                  label: {
                    position: "bottom" // 标签位置
                  },
                  attrs: {
                    // text: { text: 'out1' },
                    circle: {
                      r: 7,
                      magnet: true,
                      stroke: "#47C769",
                      strokeWidth: 1,
                      fill: "#fff"
                    },
                    line: {
                      stroke: "#722ed1"
                    }
                  },
                  zIndex: 1
                }
              },
              items: this.createPorts(node.data)
            }
          });
        }
      });
    },
    autoLayout() {
      this.isDagreLayout = true;
      this.getWorkflow()
    },

    //右键菜单事件监听
    contextMenuEvent(key) {
      this.contextMenuVisible = false
      // if (key === 'deleteNode' || key === 'updateName' || key === 'nodeCopy') {
      //   this.getWorkflow()
      // }
      if (key === 'deleteNode') {
        const nodeId = this.currentNode?.id
        if (nodeId) {
          this.graph.removeNode(nodeId)
        }
      }
      if (key === 'getWorkflow') {
        this.getWorkflow()
      }
      if (key === 'config') {
        this.dbOrrightConfig(this.currentNode)
      }

      if (key === 'clearFunctionParam') {
        this.getWorkflow()
      }
    },

    //查询流程配置
    async getWorkflow() {
      const res = await getWorkflow({
        id: this.workflowId
      });
      if (res.code == 200) {
        const { modList, connList, notInitModList } = res.result;
        this.modList = modList;
        // identifier
        var navList = []
        this.navList.forEach(k => {
          if (k.modules && k.modules.length > 0) {
            navList.push(...k.modules)
          }
        })
        this.modList.forEach(x => {
          for (var i = 0; i < navList.length; i++) {
            if (x.identifier == navList[i].identifier) {
              // x.applicationCode = navList[i].applicationCode
              x.maxOutputs = navList[i].maxOutputs
              x.maxConnections = navList[i].maxConnections
              x.startFlag = navList[i].startFlag
              x.endFlag = navList[i].endFlag
              x.webUrl = navList[i].webUrl
              x.img = navList[i].img
              x.executeStatus = notInitModList.includes(x.id) ? 'UNSETING' : ''
              break;
            }
          }
        })
        this.connList = connList;
        this.notInitModList = notInitModList;
        bus.$emit('getComponentLogsBus')

        this.renderNode();
      }
    },

    //初始化画布数据
    renderNode() {
      let nodes = [];
      this.modList.forEach(item => {
        const node = {
          shape: "shape",
          id: item.id, // String，可选，节点的唯一标识
          x: item.x, // Number，必选，节点位置的 x 值
          y: item.y, // Number，必选，节点位置的 y 值
          width: 60, // Number，可选，节点大小的 width 值
          height: 67, // Number，可选，节点大小的 height 值
          // label: item.name, // String，节点标签
          // attrs: {
          //   label: {
          //     text: item.name,
          //     fontSize: 14,
          //     refY: 75 // x 轴偏移，类似 css 中的 margin-left
          //     //textAnchor: 'center', // 左对齐
          //   }
          // },
          data: item,
          ports: {
            groups: {
              // 输入链接桩群组定义
              in: {
                // position: {
                //   name: 'absolute',//核心
                //   args: { x: 30, y: 60 }
                // },
                position: "left",
                attrs: {
                  circle: {
                    r: 7,
                    //magnet: true,
                    magnet: "passive",
                    stroke: "#5e96fe",
                    strokeWidth: 1,
                    fill: "#fff",
                    style: {
                      visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示
                    },
                  },
                  line: {
                    stroke: "#722ed1"
                  }
                }
              },
              // 输出链接桩群组定义
              out: {
                position: "right",
                label: {
                  position: "bottom" // 标签位置
                },
                attrs: {
                  // text: { text: 'out1' },
                  circle: {
                    r: 7,
                    magnet: true,
                    stroke: "#47C769",
                    strokeWidth: 1,
                    fill: "#fff",
                    style: {
                      visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示
                    },
                  },
                  line: {
                    stroke: "#722ed1"
                  }
                },
                zIndex: 1
              }
            },
            items: this.createPorts(item)
          }
        };
        nodes.push(node);
      });

      let edges = [];
      this.connList.forEach(item => {
        const edge = {
          source: {
            cell: item.from,
            port: "portOut"
          },
          target: {
            cell: item.to,
            port: "portIn"
          },
          // vertices: [{ x: 500, y: 200 }],
          router: {
            name: 'manhattan',
            args: {
              step: 30,
            },
          },
          anchor: 'center',
          connectionPoint: 'anchor',
          allowBlank: false,
          snap: {
            radius: 20,
          },
          attrs: {
            line: {
              stroke: "#33aa98",
              strokeWidth: 1.5,
              strokeDasharray: 0,
              targetMarker: {
                name: "classic",
                width: 7,
                height: 7,
              }
            }
          },
          zIndex: 0,
        };
        edges.push(edge);
      });

      if (this.isDagreLayout) return this.renderLayoutGraph(nodes, edges)

      this.graph.fromJSON({
        nodes,
        edges
      });
    },

    // 删除连线
    async removeEdge({ source, target }) {
      const params = {
        to: target.cell,
        from: source.cell
      }
      const res = await deleteConnInfo(params)
      if (res.code != 200) {
        this.getWorkflow()
      }
    },

    dragNode(MouseEvent, data) {
      //const target = MouseEvent.currentTarget
      const node = this.graph.createNode({
        data
      });
      this.dnd.start(node, MouseEvent);
    },

    // 根据组件类型生成链接桩
    createPorts(componentInfo) {
      const portsItems = [];

      if (componentInfo.startFlag) {
        // 接入组件
        portsItems.push({
          id: "portOut",
          group: "out",
          zIndex: 10
        });
      } else if (componentInfo.endFlag) {
        // 结束组件
        portsItems.push({
          id: "portIn",
          group: "in",
          zIndex: 10
        });
      } else {
        // 其他组件
        portsItems.push(
          {
            id: "portIn",
            group: "in",
            zIndex: 10
          },
          {
            id: "portOut",
            group: "out",
            zIndex: 10
          }
        );
      }
      return portsItems;
    },

    centerContent() { // 画布居中对齐
      this.graph.centerContent();
    },

    // 画布缩放
    scale(level) {
      const zoom = this.graph.zoom(); // 获取缩放级别
      if (Math.sign(level) === 1 && zoom > 1.8) return;
      if (Math.sign(level) === -1 && zoom < 0.5) return;
      this.graph.zoom(level);
    },

    // 开/关网格
    gridToggle() {
      this.showGrid = !this.showGrid
      this.showGrid ? this.graph.showGrid() : this.graph.hideGrid()
    },

    // 刷新画布
    refresh() {
      this.getWorkflow()
    },
    // 判断是否为退出全屏
    checkFull() {
      var isFull = document.fullscreen || document.webkitIsFullScreen || document.msFullscreenEnabled;
      //to fix : false || undefined == undefined
      if (isFull === undefined) { isFull = false; }
      return isFull;
    },
    // 全屏显示
    fullScreen() {
      const docElm = document.documentElement
      if (this.isFullScreen) {
        document.exitFullscreen()
      } else {
        docElm.requestFullscreen()
      }
      // this.isFullScreen = !this.isFullScreen
    },

    //撤销操作
    toUndo() {
      this.graph.undo();
    },

    //重做
    toRedo() {
      this.graph.redo();
    },

    onResize() {
      if (this.resizeTimer) return;
      this.resizeTimer = setTimeout(() => {
        this.graph.resize(
          window.innerWidth,
          window.innerHeight - this.headHeight
        );
        clearTimeout(this.resizeTimer);
        this.resizeTimer = null;
      }, 200);
    },
    // 加载iframe
    loadandpostmessage(event) {
      this.loading = false
      var iframeUrl = ''
      if (this.componentUrl.indexOf('http') > -1 || this.componentUrl.indexOf('https') > -1) {
        iframeUrl = this.componentUrl
      } else {
        iframeUrl = window.location.origin + this.componentUrl
      }
      event.target.contentWindow.postMessage(
        {
          name: 'setIframeData',
          data: {
            id: this.componentId,
            taskId: this.taskId,
            token: window.dataOsToken,
            hongfu: this.isHongfu, // 鸿富组件特殊传值
            nodeData: this.nodeData, // 节点传递数据 
            transformList: this.transformList // 字段类型转换规则
          },
          replay: false
        },
        iframeUrl
      )
    },
    // 提交配置
    configSubmit(msg) {
      // let data = {}
      console.log(JSON.parse(msg.data), '传递的data')
      // data.functionParam = msg.data
      // data.id = msg.id
      this.dialogVisible = false // 关闭弹窗
      this.loading = true // 初始化loading
      this.updateStatus = true
      this.updateTask(JSON.parse(msg.data), msg.id)
    },
    // 节点更新
    async updateTask(data, id) {
      // 入参父组件id矫正（修复更改父组件后，入参id还是旧父组件id问题）
      this.correctParentId(data)

      const res = await updateFunctionParam(data, id)
      if (res.code === 200) {
        this.$message({
          showClose: true,
          message: '保存成功',
          type: 'success'
        })
        this.getWorkflow()
      }


      // console.log(res,"节点更新")
      // if (this.updateStatus) {
      //   // 需要提示状态的时候
      //   if (res.code == 200) {
      //     res.data.forEach(item => {
      //       this.data.nodeList.find(n => n.id === item.id).runStatus = 4
      //     })
      //     this.$message({
      //       showClose: true,
      //       message: res.message,
      //       type: 'success'
      //     })
      //   } else {
      //     this.$message({
      //       showClose: true,
      //       message: res.message,
      //       type: 'error'
      //     });
      //   }
      //   this.updateStatus = false
      // }
    },
    // 确认配置
    onConfirmation() {
      // debugger
      // console.log(this.$store.state.tableData)
      var iframeUrl = ''
      if (this.componentUrl.indexOf('http') > -1 || this.componentUrl.indexOf('https') > -1) {
        iframeUrl = this.componentUrl
      } else {
        iframeUrl = window.location.origin + this.componentUrl
      }
      this.$refs.myApplication.contentWindow.postMessage(
        {
          name: 'saveConfig',
          data: '',
          replay: false
        },
        iframeUrl
      )
    },
    isEqual(a, b) {
      //如果a和b本来就全等
      if (a === b) {
        //判断是否为0和-0
        return a !== 0 || 1 / a === 1 / b;
      }
      //判断是否为null和undefined
      if (a == null || b == null) {
        return a === b;
      }
      //接下来判断a和b的数据类型
      var classNameA = toString.call(a),
        classNameB = toString.call(b);
      //如果数据类型不相等，则返回false
      if (classNameA !== classNameB) {
        return false;
      }
      //如果数据类型相等，再根据不同数据类型分别判断
      switch (classNameA) {
        case '[object RegExp]':
        case '[object String]':
          //进行字符串转换比较
          return '' + a === '' + b;
        case '[object Number]':
          //进行数字转换比较,判断是否为NaN
          if (+a !== +a) {
            return +b !== +b;
          }
          //判断是否为0或-0
          return +a === 0 ? 1 / +a === 1 / b : +a === +b;
        case '[object Date]':
        case '[object Boolean]':
          return +a === +b;
      }
      //如果是对象类型
      if (classNameA == '[object Object]') {
        //获取a和b的属性长度
        var propsA = Object.getOwnPropertyNames(a)
        // propsB = Object.getOwnPropertyNames(b);
        // if (propsA.length != propsB.length) {
        //   return false;
        // }
        // 白名单字段
        const whiteFields = ['primary', 'storageType', 'isAdd', 'splitType']
        for (var i = 0; i < propsA.length; i++) {
          var propName = propsA[i];
          //如果对应属性对应值不相等，则返回false
          if (propName === 'type') {
            if (a[propName].toUpperCase() !== b[propName].toUpperCase()) {
              return false;
            }
          } else if (a[propName] !== b[propName] && !whiteFields.includes(propName)) {
            return false;
          }
        }
        return true;
      }
      //如果是数组类型
      if (classNameA == '[object Array]') {
        if (a.toString() == b.toString()) {
          return true;
        }
        return false;
      }
    },
    // 双击或者右键点击配置
    async dbOrrightConfig(node) {
      var targetCell = this.graph.findViewByCell(node.data.id)
      targetCell.removeClass('tag-highlight')
      const item = node.data
      // 点击配置后,请把组件id赋值给this.componentId
      // 然后调用this.openUrl(item.name, this.dataCleanformatclean),第二个参数为组件对应的irameUrl
      console.log(item, "点击了配置按钮");
      let res = {}
      let params = {
        selfJson: null,
        parentJson: []
      }
      let dialogStatus = false
      // 判断是否为接入组件
      if (!item.startFlag) {
        // 非选取数据点击“配置” 1.先判断是否有连线 2.再判断是否有配置传递
        // 判断连接状态
        const edgesArr = this.graph.getIncomingEdges(node)
        if (edgesArr) {
          // 判断是否有配置传递
          let parentIds = []
          let selfId = []
          let parentJsonArr = []
          edgesArr.forEach(item => {
            parentIds.push(item.source.cell)
          })
          selfId = edgesArr[0].target.cell
          res = await queryPreviousAndSelfParam({ ids: parentIds.toString() + ',' + selfId })
          res.result.forEach(item => {
            if (item.moduleId === selfId) {
              params.selfJson = item.functionParam
            }
            parentIds.forEach(x => {
              if (x === item.moduleId && item.functionParam) {
                var arr = JSON.parse(item.functionParam)
                arr.forEach(i => {
                  i.id = item.moduleId
                  // 将functionParam里sourceFields = targetFields
                  i.sourceFields = i.targetFields
                })
                params.parentJson.push(JSON.stringify(arr))
                parentJsonArr.push(...arr)
              }
            })
          })
          debugger
          if (params.selfJson && JSON.parse(params.selfJson).length !== parentJsonArr.length) { // 如果数量不一致则清空
            if (!parentJsonArr.some(i => i.governanceJson.action === '多表拼接')) {
              // params.selfJson = null
              console.log('上游组件变更或上游组件数据变更,数据重置')
            }
          } else if (params.selfJson && JSON.parse(params.selfJson).length === parentJsonArr.length) { // 数量一致先判断组件是否被替换过
            debugger
            const parseSelfJson = JSON.parse(params.selfJson)
            let isReplace = parseSelfJson.filter(x => parentJsonArr.some(y => y.id === x.id)).length === parseSelfJson.length ? false : true // 取交集判断是否替换过
            if (isReplace) {  // 替换过就判断是否多输入
              if (parseSelfJson.length === 1) { // 不是多输入判断字段是否一致，一致就不清空。
                if (!(parentJsonArr[0].targetFields.every(_b => parseSelfJson[0].sourceFields.some(_a => this.isEqual(_a, _b))))
                ) {
                  // params.selfJson = null
                }
              } else {
                // params.selfJson = null
              }
            } else { // 没有替换过
              parentJsonArr.sort((a, b) => { // 先排序
                return parseSelfJson.findIndex(x => a.id === x.id) - parseSelfJson.findIndex(y => b.id === y.id)
              })
              var isFieldsChange = false // 判断组件中参数是否有改变
              for (var i = 0; i < parseSelfJson.length; i++) {
                for (var k = 0; k < parentJsonArr.length; k++) {
                  // 如果父组件组件是文件则不进行比较
                  if (parentJsonArr[k].dataType === 'file') break;
                  if (parseSelfJson[i].id === parentJsonArr[k].id) {
                    if (!(parentJsonArr[k].targetFields.every(_b => parseSelfJson[i].sourceFields.some(_a => this.isEqual(_a, _b))))
                    ) {
                      // 调试
                      // parseSelfJson[i].sourceFields.every(a => {
                      //   const isSome = parentJsonArr[k].targetFields.some(b => this.isEqual(a, b))
                      //   if (!isSome) {
                      //     console.log(a);
                      //     console.log(k);
                      //   }
                      //   return isSome
                      // })
                      isFieldsChange = true
                      break;
                    }
                  }
                }
              }
              if (isFieldsChange) { // 有改变，清空
                // params.selfJson = null
              } else {
                params.parentJson = parentJsonArr.map(x => JSON.stringify([x]))
              }
            }
          }
          if (params.parentJson.length == 0) {
            this.$message.error('请先完成前置流程配置！')
            return false
          } else {
            // 有连线且有配置-->可以弹窗
            dialogStatus = true
          }
        } else {
          // 提示 未连线
          this.$message.error('请先进行流程连接！')
          return
        }
      } else {
        dialogStatus = true
        res = await queryPreviousAndSelfParam({ ids: item.id })
        params.parentJson = ''
        params.selfJson = res.result[0].functionParam
        // console.log(res,"点击了选取的配置按钮")
      }
      if (!dialogStatus) {
        return false
      }
      this.loading = true
      this.dialogWidth = '800'
      if (
        item.code === 'storageData' ||
        item.code === 'storageClibData' ||
        item.code === 'storageDataBySink'
      ) {
        this.dialogWidth = '30%'
      }
      if (res.code === 200) {
        this.nodeData = params
      }
      // const url = window.componentsUrl.find(n => n.code === item.code).url
      const url = item.webUrl
      // const url = 'http://************:12025'
      this.openUrl(item.name, item.id, url)
      // this.openUrl(item.name, item.id, 'http://localhost:8081')
      // switch (item.startFlag) {
      //   case true:
      //   // 接入组件
      //   this.openUrl(item.name, item.id, 'http://************:12029')
      //   break
      //   case 'stringProcess':
      //     // 字符串处理
      //     this.openUrl(item.name, item.id, 'http://************:8610')
      //     break
      //   case 'renameFields':
      //     // 字段重命名
      //     this.openUrl(item.name, item.id, 'http://************:8613')
      //     break
      //   case 'chooseData':
      //     // 选取组件
      //     this.openUrl(item.name, item.id, 'http://**********:12010/#')
      //     break
      //   case 'chooseShareData':
      //     // 选取组件
      //     this.openUrl(item.name, item.id, /* url */ 'http://************:12029' /* 'http://service.testbuild.youedata.cc/shujufuwujieru-1212/web-access-data-services' */)
      //     break
      //   case 'chooseShareDataSpark':
      //     // 选取组件
      //     this.openUrl(item.name, item.id, 'http://************:12029' /* 'http://service.testbuild.youedata.cc/shujufuwujieru-1212/web-access-data-services' */)
      //     break
      //   case 'nullProcess':
      //     // 空值预处理组件
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12023' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanNullProcess' */
      //     )
      //     break
      //   case 'dataRepeat':
      //     // 数据去重组件
      //     this.openUrl(item.name, item.id, 'http://**********:12024')
      //     break
      //   case 'formatClean':
      //     // 格式清洗组件
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12030' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFormatClean' */
      //     )
      //     break
      //   case 'logicError':
      //     // 逻辑错误清洗组件
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12026' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanLogicError' */
      //     )
      //     break
      //   case 'fieldMerge':
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12022' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFieldMerge' */
      //     )
      //     break
      //   case 'fieldSplit':
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12021' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFieldSplit' */
      //     )
      //     break
      //   case 'dataOrder':
      //     this.openUrl(item.name, item.id, 'http://**********:8604')
      //     break
      //   case 'appStorage':
      //     // 储存组件
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12060' /* 'http://fanso.testbuild.y   ouedata.cc/subassembly/appStoreupdata' */
      //     )
      //     break
      //   case 'idcardFormat':
      //     // 身份证组件
      //     this.openUrl(item.name, item.id, 'http://**********:12022')
      //     break
      //   case 'storageClibData':
      //     // 储存组件
      //     // this.dialogWidth = '30%'
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://**********:12060' /* 'http://fanso.testbuild.youedata.cc/subassembly/appStoreupdata' */
      //     )
      //     break
      //   case 'storageDataBySink':
      //     // 储存组件
      //     // this.dialogWidth = '30%'
      //     this.openUrl(
      //       item.name,
      //       item.id,
      //       'http://************:12060' /* 'http://fanso.testbuild.youedata.cc/subassembly/appStoreupdata' */
      //     )
      //     break //
      //   case 'outStorage':
      //     // 外部存储
      //     this.openUrl(item.name, item.id, 'http://************:12060')
      //     break
      //   case 'formatCleanClibArrow':
      //     this.openUrl(item.name, item.id, 'http://**********:12027')
      //     break
      //   case 'formatCleanClibFindchips':
      //     this.openUrl(item.name, item.id, 'http://**********:12028')
      //     break
      //   case 'resourceAnalysis':
      //     // 文件解析
      //     this.openUrl(item.name, item.id, 'http://**********:12030')
      //     break
      //   case 'multiTableJoin':
      //     // 多表拼接
      //     this.openUrl(item.nanme, item.id, 'http://************:8606')
      //     break
      //   case 'dataCalculate':
      //     // 数据计算
      //     this.openUrl(item.name, item.id, 'http://**********:8603')
      //     break
      //   case 'newFields':
      //     // 新增字段
      //     this.openUrl(item.name, item.id, 'http://************:8608')
      //     break
      //   case 'newFieldsSpark':
      //     // 新增字段spark
      //     this.openUrl(item.name, item.id, 'http://************:8608')
      //     break
      //   case 'dataUnion':
      //     // 数据拼接
      //     this.openUrl(item.name, item.id, 'http://************:8609')
      //     break
      //   case 'dataTypeConvert':
      //     // 格式转换
      //     this.openUrl(item.name, item.id, 'http://************:8607')
      //     break
      //   case '':
      //     // 行列转换
      //     this.openUrl(item.name, item.id, 'http://localhost:12399')
      //     break
      //   default:
      //     this.$message.error('code没写入switch语句')
      //     this.openUrl(item.name, item.id, url)
      //     break
      // }
    },
    // 关闭desc查看页面
    close() {
      this.$refs.showDesc.click()
    },
    // 打开页面
    openUrl(title, id, url) {
      // console.log(this.data.nodeList)
      this.componentUrl =
        url +
        '/?accountToken=' +
        window.dataOsToken +
        '&accountId=' +
        window.accountId +
        '&categoryService=' +
        '2'
      this.templateTitle = title
      this.componentId = id
      this.dialogVisible = true
    },
    ...mapMutations(['rightConfigActiveNameChange']),
  },
};
</script>

<style lang="scss">
@import "../assets/style/elenentReset_dialog.scss";

.theCanvas {
  transition: all 0.4s;
}

.node-shape-box {
  width: 100%;
  height: 100%;
  position: relative;
}

.node-shape-name {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  text-align: center;
  font-weight: normal;
  font-size: 12px;
}

.node-shape {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: all .3s;

  &:hover {
    background-color: #b7e5d7;
    box-shadow: 0 0 5px #dadada;
  }
}

.sharp {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #DCDFE6;
  width: calc(100% - 10px);
  height: calc(100% - 10px);
  clip-path: polygon(50% 0, 100% 25%, 100% 75%, 50% 100%, 0 75%, 0 25%);
}

.tag-highlight {
  .sharp {
    //  background-color: #276EB7;
    // background: url('../assets/images/pic_line_shadow.png') no-repeat;
    // clip-path: none;
    // background-color: ;
    // background: linear-gradient(90deg, #223D7C 0%, #276EB7 100%);
    // box-shadow: 0px 0px 6px 0px #276EB7;
    // border: 1px solid #276EB7;
  }

  .sharpb {
    background: url('../assets/images/pic_line_shadow.png') no-repeat;
    width: calc(100% - 0px);
    background-size: cover;
    clip-path: none;
  }
}

.sharpb {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  clip-path: polygon(50% 0, 100% 25%, 100% 75%, 50% 100%, 0 75%, 0 25%);

  .node-shape-icon {
    width: 60%;
    height: auto;
  }
}

.drag-node-item {
  width: 85px;
  height: 70px;
  background: #fff;
  position: relative;
  box-shadow: 0 0 10px 1px #dadada;
  padding: 6px 5px 0;

  .node-icon {
    display: block;
    width: 28px;
    height: 28px;
    margin: 0 auto;
    background-size: 22px;
    background-repeat: no-repeat;
    background-position: 50%;
  }

  .node-label {
    display: block;
    height: 32px;
    font-size: 12px;
    line-height: 16px;
    color: #333;
    text-align: center;
    overflow: hidden;
  }
}
</style>