import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    nodeSelectorHide: false,
    rightConfigActiveName: '',
    taskId: '',
    // 组件日志信息
    componentLogs: [],
    // 流程日志信息
    processLogs: [],
    // 日志栏是否打开
    logBarIsOpen: false,
    category: ''
  },
  mutations: {
    nodeSelectorHideToggle(state, val) {
      state.nodeSelectorHide = val
    },
    rightConfigActiveNameChange(state, val) {
      if (state.rightConfigActiveName === val) {
        state.rightConfigActiveName = ''
      }
      else {
        state.rightConfigActiveName = val
      }
    },
    setTaskId(state, val) {
      state.taskId = val
    },
    setComponentLogs(state, val) {
      state.componentLogs = val
    },
    setProcessLogs(state, val) {
      state.processLogs = val
    },
    setLogBarIsOpen(state, val) {
      state.logBarIsOpen = val
    },
    setCategory (state, val) {
      state.category = val
    }
  },
  actions: {
  },
  modules: {
  }
})
