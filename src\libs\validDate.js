var timeValidator = (rule, value, callback) => {
  // debugger
    if (!value) {
        return callback(new Error('请选择执行时间'))
    }
    if (new Date(value).getTime() - 300 * 1000 < Date.now()) {
        callback(new Error("请选择正确的执行时间(调度任务五分钟后才可执行)"));
    } else {
        callback();
    }
}
var specificTimeCheck = (rule, value, callback) => {
    if (!value) {
      return callback(new Error('请选择具体时间'))
    } else {
      callback()
    }
  }
export default {
  // 通用
  executeTime: [
    { required: true, validator: timeValidator, trigger: 'blur' }
  ],
  specificTime: [
    { required: true, validator: specificTimeCheck, trigger: 'blur' }
  ],
  weekTime: [
    { required: true, message: '请选择时间', trigger: 'change' }
  ],
  dispatchCycle: [
    { required: true, message: '请选择调度周期', trigger: 'change' }
  ]
//   codeName: [
//     { required: true, validator: codeName<PERSON>heck, trigger: 'blur' },
//     { min: 2, max: 60, message: '长度在2~60之间', trigger: 'blur' }
//   ]
}
  