import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import workflowAdd from '../views/workflowAdd.vue'
Vue.use(VueRouter)
const routes = [
  {
    path: '/',
    redirect: self == top ? '/workflowAdd' : '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/workflowAdd',
    name: 'workflowAdd',
    component: workflowAdd
  }
]

const router = new VueRouter({
  basePath: '/workflowAdd',
  routes
})

export default router
