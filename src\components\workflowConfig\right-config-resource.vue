<!-- 资源配置 -->
<template>
  <section class="right-config-panel" :style="{right: $store.state.rightConfigActiveName === '资源配置' ? 0 : ''}">
    <div class="right-config-form">
      <div class="workflow-resource-config">
        <h2>资源配置</h2>
        <div class="form-group" v-if="resourceList.length">
          <el-form :model="resourceForm" ref="resourceForm" label-width="100px" label-position="top" size="small">
            <el-form-item label="运行资源" prop="resourceCode" style="margin-bottom: 25px" :rules="[
              { required: true, message: '请选择运行资源', trigger: 'blur,change' },
            ]">
              <template slot="label">
                运行资源
                <el-tooltip class="item" effect="dark" content="任务运行所依赖的资源环境" placement="right">
                  <em class="el-icon-question"></em>
                </el-tooltip>
              </template>
              <el-select v-model="resourceForm.resourceCode" @change="handleResourceChange" placeholder="请选择">
                <el-option v-for="item in resourceList" :label="item.name" :value="item.id" :key="item.resourceCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <div style="text-align: center; color: #999; padding-bottom: 50px;" v-if="!configData.length">暂无配置项</div>
          <el-form :model="configForm" ref="configForm" label-width="100px" label-position="top" size="small">

            <el-form-item v-for="(item) in configData" :label="item.key" :prop="item.key" :rules="[
              { validator: validateNumber, trigger: 'blur' }
            ]" :key="item.key">
              <template slot="label">
                {{item.key}}
                <el-tooltip class="item" effect="dark" :content="item.desc" placement="right">
                  <em class="el-icon-question"></em>
                </el-tooltip>
              </template>
              <el-input v-model="configForm[item.key]">
                <template slot="append">{{item.unit}}</template>
              </el-input>
            </el-form-item>

            <el-form-item style="text-align: center; padding-top: 15px">
              <!-- <el-button @click="resetForm">清空</el-button> -->
              <el-button type="primary" :disabled="!configData.length" @click="submitForm">保存</el-button>
            </el-form-item>

          </el-form>

        </div>
        <div v-else>请先到AI引擎管理平台的资源列表注册任务调度需要的资源</div>
      </div>
    </div>
  </section>
</template>

<script>

import { dataosResource, getConfig, resourceConfig, updateConfig, clearConfig } from '@/api/workflowConfig'
export default {
  name: 'rightConfigResource',
  data() {
    return {
      workflowId: '',
      savedConfig: {},
      resourceForm: {
        resourceCode: '', // 资源code
      },
      configForm: {
        //
      },
      resourceList: [ // 运行资源列表
        {
          "createTime": "cupidatat tempor deserunt do pariatur",
          "resourceCode": "1",
          "resourceType": "mysql",
          "resourceName": "mysql"
        },
        {
          "createTime": "laboris",
          "resourceCode": "2",
          "resourceType": "spark",
          "resourceName": "spark"
        }
      ],
      allConfigData: [], // 所有配置项数据
      configData: [
        // {
        //   code: 'mysql',   
        //   key: 'aaaaaaa', // 配置项名称
        //   value: 1, // 配置项值
        //   desc: '描述1', // 配置项描述
        //   unit: 'G', // 配置项单位
        // },
        // {
        //   code: 'mysql', 
        //   key: 'bbbbbbb', // 配置项名称
        //   value: 2, // 配置项值
        //   desc: '描述2', // 配置项描述
        //   unit: '个', // 配置项单位
        // }
      ], // 配置项数据
      //configList: [], // 当前选中资源对应的配置项列表
    }
  },
  watch: {
    configData(newValue) {
      let configForm = {}
      newValue.forEach((item) => {
        configForm[item.key] = item['value']
      })
      this.configForm = configForm
    },
    '$store.state.rightConfigActiveName'(val) {
      if (val === '资源配置') {
        // this.getScheduleInfo()
        this.getDataosResource()
      }
      // this.currentTime = new Date().getTime()
    }
  },
  created() {
    // this.workflowId = this.$route.query.workflowId
    // this.category =  this.$route.query.category
    this.workflowId = window.GetQueryValue('workflowId')
    this.category = window.GetQueryValue('category')
    this.getDataosResource()
    this.getSavedConfig()
  },
  methods: {
    async getDataosResource() {
      const data = {
        resourceType: this.category
      }
      const res = await dataosResource(data);
      if (res.code == 200) {
        this.resourceList = res.result
      }
      this.getSavedConfig()
    },
    async getSavedConfig() {
      const res = await getConfig({ // 获取上次保存的配置项
        workflowId: this.workflowId
      })
      if (res.code == 200 && res.result.resourceId) { // 如果有保存的值
        // this.savedConfig = res.result
        this.resourceForm.resourceCode = res.result.resourceId
        const configData = res.result.workflowExecuteConfigBaseVOs
        this.configData = configData
      }
    },
    async handleResourceChange() { // 获取运行资源下的配置项
      // if(this.savedConfig.resourceCode === val) {
      //   this.configData = JSON.parse(this.savedConfig.resourceConfig)
      //   return
      // }
      const res = await resourceConfig({ workflowCategory: this.category });
      if (res.code == 200) {
        this.configData = res.result
        this.$refs['configForm'].resetFields();
        this.configForm = {}
      }
    },
    async resetForm() {
      this.$refs['configForm'].resetFields();
      const res = await clearConfig({ workflowId: this.workflowId });
      if (res.code == 200) {
        // this.savedConfig = {}
        this.configForm = {}
        this.$message({
          message: '清空成功',
          type: 'success'
        });
      }
    },
    submitForm() {

      this.$refs['resourceForm'].validate((valid) => {
        if (valid) {
          this.$refs['configForm'].validate((valid2) => {
            if (valid2) {
              const configData = JSON.parse(JSON.stringify(this.configData))
              configData.forEach((item) => {
                item.value = this.configForm[item.key]
                // delete item.unit
                // delete item.desc
                // delete item.value
              })

              const data = {
                // workflowId: this.workflowId, // 流程id
                resourceId: this.resourceForm.resourceCode, // dataos的资源code
                // type: this.category, // 资源类型：mysql，spark
                executeConfigParams: configData // 资源配置
              }
              updateConfig(data, this.workflowId).then((res) => {
                if (res.code == 200) {
                  this.getSavedConfig()
                  this.$message({
                    message: '保存成功',
                    type: 'success'
                  });
                }
              })
            } else {
              console.log('error submit!!');
              return false;
            }
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },

    validateNumber(rule, value, callback) {
      if (!(/(^[1-9]\d*$)/.test(value))) {
        callback(new Error('配置项必须为正整数'));
      } else {
        callback();
      }
    }
  }
}
</script>

<style lang="scss">
.workflow-resource-config {
  width: 100%;
  padding: 0 24px 12px;

  h2 {
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    // border-bottom: 1px solid #e2e2e2;
  }

  .form-group {
    .el-form-item--small .el-form-item__label {
      line-height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: #606266;
    }

    .el-form--label-top .el-form-item__label {
      padding: 0 0 0;
    }

    .el-form-item--mini.el-form-item,
    .el-form-item--small.el-form-item {
      margin-bottom: 9px;
    }

    .el-select {
      display: initial;
    }

    .el-input-group__append {
      padding: 0 10px;
      font-size: 14px;
      font-weight: 400;
      background-color: transparent;
      border: none;
      color: #303133;
    }

    .el-input-group--append .el-input__inner,
    .el-input-group__prepend {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    .el-button--primary {
      border-color: #276EB7;
      background-color: #276EB7;
      color: #fff;
    }
  }
}
</style>