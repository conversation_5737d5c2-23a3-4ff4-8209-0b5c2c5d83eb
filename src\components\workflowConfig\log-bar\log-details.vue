<!-- 日志详情dialog -->
<template>
  <span>
    <span class="opt-text" @click='dialogVisible = true'>详情</span>
    <el-dialog title='日志详情' :visible.sync='dialogVisible' width='53%' destroy-on-close :close-on-click-modal='false'
      append-to-body>
      <el-descriptions :column="1" :labelStyle="{ minWidth: '66px' }">
        <el-descriptions-item label="节点名称">{{ row.moduleName }}</el-descriptions-item>
        <el-descriptions-item label="运行状态">{{ componentMapping[row.executeState].label }}
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ row.exeStartTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ row.exeEndTime }}</el-descriptions-item>
        <el-descriptions-item label="任务耗时"> {{ getTaskConsumeTime(row.timeConsuming) }}</el-descriptions-item>
        <el-descriptions-item label="日志详情">
          <div class="log-details">{{ row.exeResult }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </span>
</template>
<script>
import { componentMapping } from '@/libs/types.js'
import { getConsumeTime } from '@/libs/utils.js'
export default {
  props: {
    row: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: false,
      componentMapping
    };
  },
  computed: {
    getTaskConsumeTime() {
      return (ms) => {
        return getConsumeTime(ms)
      }
    }
  },
  components: {},
  methods: {
    cancel() {
      this.dialogVisible = false;
    },
    submit() {
      this.dialogVisible = false;
    },
    openDialog() {
      this.dialogVisible = true;
      this.nextTick(() => { });
    },
  },
};
</script>
<style lang='scss' scoped>
::v-deep .el-descriptions-item__label {
  min-width: 60px;
}

.log-details {
  max-height: 350px;
  overflow: auto;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e9e9eb;
  white-space: pre;
  width: calc(53vw - 138px);
}

::v-deep .el-descriptions__body {
  padding: 12px;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding-bottom: 24px !important;
}
</style>