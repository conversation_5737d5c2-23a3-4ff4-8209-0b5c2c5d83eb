.console_container {
  position: relative;
  width: 100%;
  background: #fff;
  .console_switch {
    padding: 0 30px;
    display: flex;
    align-items: center;
    height: 60px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 1);
    border-top: 1px solid #e3e3e3;
    .console_title {
      margin-right: 40px;
      font-size: 14px;
      font-weight: 400;
      color: #2F3949;
    }
  }

  .console_content {
    position: relative;
    width: 100%;
    height: 320px;
    overflow: hidden;
    .console_content_con{
      display: flex;
      align-items: center;
      width: 100%;
      height: 320px;
    }
    .console_content_table {
      flex: 1;
      height: 320px;
      background: rgba(255, 255, 255, 1);
      box-sizing: border-box;
      padding: 20px;
      box-sizing: border-box;
      position: relative;
      .console_content_table_title{
        width: 100%;
        font-weight: 400;
        color: #303133;
        line-height: 24px;
        height: 24px;
      }
      .log_content{
        position: absolute;
        left:20px;
        right:20px;
        top: 48px;  
        margin-top:10px;
      }
    }
    .console_log {
      .log_content {
        p {
          margin-bottom: 10px;
          font-size: 12px;
          font-weight: 400;
          color: rgba(137, 146, 160, 1);
          line-height: 200%;
        }
      }
    }

    .console_content_table:last-child {
      margin-left: 10px;
    }
  }
}

// 取消每行移入效果
/deep/ .el-table--enable-row-hover .el-table__body tr:hover > td {
  background: none !important;
}

// /deep/ .el-table td, .el-table th.is-leaf {
//   border-bottom: 1px solid #e3e3e3;
// }

// /deep/ .el-table th.is-leaf {
//   border-bottom: 1px solid #e3e3e3;
// }

/deep/ .el-table td {
  padding: 4px 0;
}

/deep/ .el-table::before {
  height: 0;
}

/deep/ .el-table th > .cell {
  font-size: 14px;
}

/deep/ .el-table .cell {
  font-size: 12px;
}

// 滚动条
.console_content_table::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #CFD3DB;
}

// 滚动条里面轨道
.console_content_table::-webkit-scrollbar-track {
  border-radius: 3px;
  background: #EBEDF3;
}

.console_content_table::-webkit-scrollbar {
  width: 8px;
  height: 1px;
}

/deep/ .is-scrolling-none{
  height:244px; overflow-y: auto;
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: #CFD3DB;
  }
  
  // 滚动条里面轨道
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: #EBEDF3;
  }
  
  &::-webkit-scrollbar {
    width: 8px;
    height: 1px;
  }
}
