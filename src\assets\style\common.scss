* {
  box-sizing: border-box;
}
body {
  margin: 0;
  padding: 0;
  color: #333;
  font-size: 14px;
}
#content {
  
  padding: 0px 16px 9px 31px;

  .content-tit {
    font-size: 14px;
    color: #2F3949;
  }

  .name-box {
    overflow: hidden;
    height: 56px;
    line-height: 56px;
    margin-bottom: 24px;
    box-sizing: border-box;
    border-bottom: 1px solid #F2F2F2;
    .exit-btn {
      float: left;
      font-size: 16px;
      // height: 18px;
      // margin: 4px 5px 0 0;
      color: #276EB7;
      cursor: pointer;
    }
    .icon-back {
      float: left;
      width: 16px;
      height: 16px;
      margin: 19px 8px 0 0;
      background: url(../images/icon_back.png) no-repeat center;
    }

    .name-txt {
      float: left;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 400;
      margin-left: 40px;
      color: #303133;
    }

    .name-text {
      float: left;
      width: 120px;
      height: 34px;
      padding: 0 10px;
      font-size: 14px;
      border-radius: 3px;
      border: 1px solid #e3e3e3;
      box-sizing: border-box;
    }

    .name-edit {
      float: left;
      width: 18px;
      height: 18px;
      margin: 6px 0 0 5px;
      cursor: pointer;
      background: url(../images/name-edit.png) no-repeat center;
    }
  }

  #head-nav {
    width: 100%;
    background: #ffffff;
    border-radius: 4px;
    box-sizing: border-box;

    #head-ul {
      float: left;
      width: 100%;
      margin: 0 0 0px; 
      position: relative;
      z-index: 3;
      border-right: 1px solid #fff;
      box-sizing: border-box;
      border-top-right-radius: 4px;
      

      li {
        float: left;
        margin: 0 3px 0px 0;
        padding: 0px 12px;
        min-width: 80px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        box-sizing: border-box;
        font-size: 14px;
        color: #606266;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #DCDFE6;
        user-select: none;
        cursor: pointer;
        background: #fff;
        z-index: 1; 
      }
      li.active {
        color: #303133;
        font-weight: 600;
        border-bottom: none;
        // box-shadow: 0 5px 5px #E1E8E5;
      }
    }

    .drag-ul {
      position: relative;
      margin-top:-1px;
      z-index: 2;
      width: 100%;
      min-height: 62px;
      float: left;
      background: #ffffff;
      padding: 17px 15px;
      box-sizing: border-box;
      border-top: 1px solid #DCDFE6;
      border-left: 1px solid #DCDFE6;
      border-right: 1px solid #DCDFE6;
      box-shadow: 0px 3px 2px -1px rgba(26, 72, 120, 0.1);

      li {
        float: left;
        position: relative;
        margin: 7px 10px;
        padding: 0 12px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        color: #4F8DFF;
        border: 1px solid #4F8DFF;
        border-radius: 4px;
        box-sizing: border-box;
        text-align: center;
        user-select: none;
        cursor: pointer;

        span {
          position: absolute;
          right: -10px;
          top: -10px;
          display: inline-block;
          min-width: 18px;
          height: 18px;
          line-height: 17px;
          border-radius: 50%;
          font-size: 12px;
          background: #8F8BEA;
          color: #ffffff;
          text-align: center;
        }
      }

      li.only {
        color: #B5B8BE !important;
        border: 1px solid #B5B8BE !important;

        span {
          background: #B5B8BE !important;
        }
      }
    }
  }

  .actice-box {
    overflow: hidden;
    width: 100%;
    margin-top: 20px;
    padding: 20px 30px 10px;
    background: #fff;
    display: table;
    box-sizing: border-box;

    .content-tit2 {
      display: table-cell;
      vertical-align: middle;
      width: 80px;

      span {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        padding-bottom: 10px;
        font-size: 14px;
        color: #2F3949;
      }
    }

    .head-active-ul {
      display: table-cell;

      li {
        float: left;
        margin: 0 20px 10px 0;
        padding: 0 12px;
        min-width: 80px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        box-sizing: border-box;
        font-size: 14px;
        color: #ffffff;
        border-radius: 4px;
        user-select: none;
        cursor: pointer;
      }
    }
  }

  .config-btn {
    float: right;
    width: 88px;
    height: 34px;
    line-height: 32px;
    background: #E9F0F7;
    border-radius: 4px;
    border: 1px solid #A9C5E2;
    
    text-align: center;
    margin-left: 20px;
    box-sizing: border-box;
    font-size: 14px;
    color: #1570C4;
    cursor: pointer;
    user-select: none;
  }

  .config-btn-primary {
    float: right;
    width: 88px;
    height: 36px;
    line-height: 36px;
    background: #276EB7;
    border-radius: 4px;
    border: 1px solid #276EB7;
    text-align: center;
    margin: 10px 0 0 20px;
    box-sizing: border-box;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    user-select: none;
  }

  
  .drag-content {
    position: relative;
    padding-top: 20px;
    padding: 0;
    background: #ffffff;
    border-radius: 4px;
    border-left:  1px solid #DCDFE6;
    border-right:  1px solid #DCDFE6;
    border-bottom:  1px solid #DCDFE6;

    #drag-box {
      position: relative;
      overflow: auto;
      z-index: 1;
      width: 100%;
      height: 620px;
      border-radius: 4px;
      background-image: url(../images/drag-bg.png);

      .node {
        position: absolute;
        min-width: 68px;
        height: 64px;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
        display: flex;
        justify-content: center;

        .node-txt {
          display: inline-block;
          margin-right: 36px;
          padding: 0 7px 0 12px;
          min-width: 104px;
          height: 36px;
          line-height: 36px;
          text-align: center;
          user-select: none;
          border-radius: 4px 0 0 4px;
          box-sizing: border-box;
          font-size: 12px;
        }

        .node-edit {
          position: absolute;
          right: 0;
          top: 0;
          width: 36px;
          height: 36px;
          border-radius: 0 2px 2px 0;
          background-image: url(../images/config.png);
          background-repeat: no-repeat;
          background-position: center;
          cursor: pointer;
        }

        .node-close {
          display: none;
          position: absolute;
          left: -8px;
          top: -8px;
          z-index: 999;
          width: 18px;
          height: 18px;
          background: url(../images/close.png) no-repeat center;
          background-size: 100%;
        }

        .el-icon-error {
          display: none;
          position: absolute;
          right: -8px;
          top: -8px;
          z-index: 999;
        }

        &:hover {

          .node-close,
          .el-icon-error {
            display: inline-block;
          }
        }
      }

      .title {
        position: absolute;
        bottom: -30px;
        z-index: 999;
        white-space: nowrap;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: rgba(47, 57, 73, 1);
        line-height: 25px;
         
      }
       

      .icon {
        width: 36px;
        height: 36px;
        margin-top: 6px;
      }

      .statu {
        width: 13px;
        height: 14px;
        position: absolute;
        bottom: 6px;
        left: 5px;
      }

      .loading {
        transition: color .15s linear;
        animation: rotating 2s linear infinite
      }
    }
  }
}

.choose-config {
  width: 100%;
  height: 580px;
  background: #F7F6F9;

  .config-left {
    float: left;
    width: 350px;
    height: 530px;
    padding: 20px 10px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 4px;

    p {
      background: #F5F4F7;
      height: 32px;
      line-height: 32px;
      color: #2F3949;
      font-size: 14px;
      margin-bottom: 5px;
      padding-left: 13px;
    }

    .tree-box {
      overflow: auto;
      height: 460px;

      .is-disabled {
        display: none;
      }

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      // 滚动条
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #999;
      }

      // 滚动条里面轨道
      &::-webkit-scrollbar-track {
        border-radius: 10px;
        background: #EDEDED;
      }
    }
  }

  .config-right {
    margin-left: 370px;
    height: 530px;
    background: #fff;
    padding: 20px 30px;
    border-radius: 4px;
    box-sizing: border-box;
    overflow: auto;

    .el-table {
      border: 1px solid #E3E8F0;
      border-bottom: 0;
      border-radius: 4px;
    }

    .el-table th {
      background: #F3F5F9;
      color: #2F3949;
      font-size: 14px;
      white-space: nowrap;
    }

    .el-table td {
      color: #2F3949;
      font-size: 12px;
      white-space: nowrap;
    }

    .el-table td,
    .el-table th {
      border-bottom: 1px solid #E3E8F0;
    }

    .el-loading-mask {
      background-color: rgba(255, 255, 255, 0.7) !important;
    }

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    // 滚动条
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #999;
    }

    // 滚动条里面轨道
    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #EDEDED;
    }
  }
}

.store-up-config {
  height: 520px;

  .store-up-tit {
    font-size: 14px;
    color: #2F3949;
    padding: 0 0 20px 0;
  }

  .store-up-config-tree {
    height: 420px;
    border-bottom: 1px solid #E5E5E7;
    padding-bottom: 10px;

    p {
      background: #F5F4F7;
      height: 32px;
      line-height: 32px;
      color: #2F3949;
      font-size: 14px;
      margin-bottom: 5px;
      padding-left: 13px;
    }
  }

  .tree-add {
    padding: 15px 0 0;

    button {
      width: 72px;
      text-align: center;
      padding: 8px 0;
    }
  }
}

.el-tooltip__popper.is-dark {
  max-width: 700px;
  line-height: 1.2;
}

.popperDesc {
  background: #333744;
  height: 689px;
  overflow-y: auto;
  border: none;
  top: 3vh !important;

  .close {
    float: right;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    cursor: pointer;
  }

  .label {
    height: 48px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    line-height: 48px;
  }

  .contain {
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: rgba(221, 224, 232, 1);
    line-height: 20px;
    text-indent: 2em
  }

  .ability {
    li {
      list-style-type: disc
    }
  }

  .defaultInfo {
    width: 100%;
    height: calc(100% - 20px);
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    line-height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.el-picker-panel .el-picker-panel__footer .el-button--text {
  display: none;
}

// 滚动条
.console_content_table::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #CFD3DB;
}



// 滚动条里面轨道
.console_content_table::-webkit-scrollbar-track {
  border-radius: 3px;
  background: #EBEDF3;
}

.console_content_table::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

// toolTip
.toolItem,.toolItemAuto, .toolItemBig {
  width: 300px;
  max-height: 380px;
  border: none !important;
  box-shadow: 0px 8px 24px 0px rgba(26, 72, 120, 0.1);
  .popper__arrow {
    border: none;
  }
  .popper-item {
    margin-top: 10px;
  }
  .popper-title {
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 10px;
  }
  .popper-body {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: #606266;
  }
}
.toolItemBig {
  width: 405px;
  
  .poper-content {
    max-height: 320px;
    overflow-y: auto;
  }
}
.toolItemAuto {
  width: auto;
}
.ss{
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:first-child{
    display: none;
  }
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(2) {
    display: none;
  }
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(3) {
    width: 100%;
  }
}
.el-time-panel{
  width: 150px !important;
}
.mmss{
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:first-child{
    display: none;
  }
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(2) {
    width: 50%;
  }
  .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(3) {
    width: 50%;
  }
}
// .hhmmss{
//   .el-time-spinner.has-seconds .el-time-spinner__wrapper:first-child{
//     display: none;
//   }
//   .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(2) {
//     width: 50%;
//   }
//   .el-time-spinner.has-seconds .el-time-spinner__wrapper:nth-child(3) {
//     width: 50%;
//   }
// }

.opt-text {
  cursor: pointer;
  color: #276eb7;
}
.c-success {
  color: rgb(22, 196, 105);
}

.c-error {
  color: #ff5656;
}
.c-loading {
  color: #276eb7;
}
.c-warning {
  color: rgb(255, 154, 0);
}

.b-success {
  background: rgb(22, 196, 105);
}

.b-error {
  background: #ff5656;
}
.b-loading {
  background: #276eb7;
}
.b-warning {
  background: rgb(255, 154, 0);
}
@keyframes running-line {
  to {
    stroke-dashoffset: -1000;
  }
}