# 删除目录
rm -rf devops_out
rm -rf dist

echo $1 

node_path=devops/$1/node_ext

if [ ! -d "$node_path" ]; then
mkdir -p $node_path
	if [ $1 = 'win' ];then
		unzip -d $node_path devops/$1/*.zip
	elif [ $1 = 'linux' ];then
		tar -zxvf devops/$1/*.tar.gz -C $node_path
	fi
fi
if [ $1 = 'win' ];then
		node_path=$node_path/$(ls $node_path)
	elif [ $1 = 'linux' ];then
		node_path=$node_path/$(ls $node_path)/bin
fi

$node_path/node -v

# 编译
$node_path/npm install --unsafe-perm=true --allow-root
$node_path/npm run build

#打包
mkdir -p devops_out
cp devops/Dockerfile  devops_out/
cp devops/default.conf devops_out/
cp -r dist devops_out/
