import feach from '../libs/feach'

const baseUrl = window.applicationServerPath

// 获取taskId
export function getTaskIdApi(workflowId) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${workflowId}/latest_task_id`,
    method: 'get',
  })
}

// 获取流程日志
export function getProcessLogsApi(taskId) {
  return feach({
    url: baseUrl + `/api/v2/tasks/${taskId}/execute_logs`,
    method: 'get',
  })
}

// 获取组件日志
export function getCompnentsLogsApi(taskId) {
  return feach({
    url: baseUrl + `/api/v2/tasks/${taskId}/module_logs`,
    method: 'get',
  })
}

// 获取组件结果
export function getCompnentsResult(data) {
  return feach({
    url: baseUrl + `/api/v2/tasks/${data.taskId}/${data.moduleId}/result?page=${data.page}&size=${data.size}`,
    method: 'get',
  })
}

// 获取流程结果(通用存储)
export function getCompnentsTaskResult(data) {
  return feach({
    url: baseUrl + `/api/v2/tasks/${data.taskId}/${data.moduleId}/task_result?pageNum=${data.page}&pageSize=${data.size}`,
    method: 'get',
  })
}