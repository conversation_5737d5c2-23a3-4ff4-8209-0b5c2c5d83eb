<template>
  <div class="node-shape-box">
    <div class="node-shape">
      <div class="sharp">
        <div class="sharpb">
          <img class="node-shape-icon" :src="shapeData.img" />
          <img :class="{ loading: shapeData.runStatus === 0 }"
            :src="require(`../assets/images/components/icon-${shapeData.runStatus === 0 ? 'inOperation' : shapeData.runStatus === 1 ? 'runSuccessfully' : shapeData.runStatus === 2 ? 'runFailed' : shapeData.runStatus === 4 ? 'noConfigure' : 'error'}.png`)"
            alt class="statu" v-if="shapeData.runStatus !== undefined && shapeData.runStatus !== 3" />
        </div>
      </div>
    </div>
    <div class="node-label">
      <el-tooltip v-if="shapeData.executeStatus === 'UNSETING'" content="组件未配置，请双击配置" placement="top" effect="light">
        <i :class="getIcon"></i>
      </el-tooltip>
      <svg-icon v-else-if="shapeData.executeStatus === 'NOTRUNNING'" name="funnel" color="#666"
        style="font-size: 16px" />
      <i v-else :class="getIcon"></i>
      {{ shapeData.name }}
    </div>
  </div>
</template>

<script>
import { componentMapping } from '@/libs/types.js'
export default {
  name: 'myShape',
  inject: ['getGraph', 'getNode'],
  data() {
    return {
      shapeData: {},
    };
  },
  computed: {
    getIcon() {
      if (!this.shapeData.executeStatus) return ''
      const icon = componentMapping[this.shapeData.executeStatus].icon
      return `el-icon-${icon} c-${icon}`
    }
  },
  mounted() {
    const self = this;
    const node = this.getNode();
    const graph = this.getGraph()
    self.shapeData = node.getData();
    // 监听数据改变事件
    node.on('change:data', ({ current }) => {
      const edges = graph.getIncomingEdges(node)
      const { executeStatus } = node.getData()
      self.shapeData = current;
      edges?.forEach((edge) => {
        if (executeStatus === 'EXECUTING') {
          edge.attr('line/strokeDasharray', 5)
          edge.attr('line/style/animation', 'running-line 30s infinite linear')
        } else {
          edge.attr('line/strokeDasharray', '')
          edge.attr('line/style/animation', '')
        }
      })
    });
  },
  methods: {
    // add() {
    //   const node = this.getNode();
    //   const { num } = node.getData();
    //   node.setData({
    //     num: num + 1,
    //   });
    // },
  },
};
</script>
<style lang="scss" scoped>
.statu {
  width: 13px;
  height: 14px;
  position: absolute;
  bottom: 17px;
  left: 41px;
}

.node-label {
  min-width: 120px;
  position: absolute;
  text-align: center;
  left: -32px;
  display: flex;
  justify-content: center;
  align-items: center;

  .c-loading {
    color: #fff;
    background: #276eb7;
    border-radius: 50%;
    box-sizing: border-box;
    padding: 1px;
    font-size: 10px;
  }

  i {
    margin-right: 4px !important;
    font-size: 16px;
  }
}
</style>