<template>
  <div style="text-align: center; padding: 100px 0;">
    <el-button type="primary" icon="el-icon-circle-plus" @click="dialogVisible = true">新建流程</el-button>
    <el-dialog :append-to-body="true" :close-on-click-modal="false" :modal-append-to-body="false"
      :visible.sync="dialogVisible" width="200px">
      <div class="header-title" slot="title">
        <span class="tit">新建流程</span>
      </div>
      <div>

        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="流程名称" prop="name">
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="流程标签" prop="category">
            <el-select v-model="ruleForm.category" placeholder="请选择">
              <el-option label="mysql" value="MYSQL"></el-option>
              <el-option label="spark" value="SPARK"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

      </div>
      <span class="dialog-footer" slot="footer">
        <el-button @click="resetForm" type="info">取消</el-button>
        <el-button @click="submitForm" type="primary" v-popover:popover>确认</el-button>

      </span>

    </el-dialog>
  </div>
</template>

<script>
import { workflowAdd } from '@/api/https.js'
export default {
  name: 'workflowAdd',
  data() {
    return {
      // workflowId: window.dataOsToken,
      dialogVisible: false,
      ruleForm: {
        name: '',
        category: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入流程名称', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择流程标签', trigger: 'change' }
        ],
      }
    }
  },
  methods: {
    submitForm() {
      this.$refs['ruleForm'].validate(async (valid) => {
        if (valid) {
          const params = this.ruleForm
          const res = await workflowAdd(params)
          if (res.code == 200) {
            const { category, name, workflowId } = res.result
            this.$router.push({
              path: '/home',
              query: {
                accountToken: window.dataOsToken || '1ea958ae55a94c1787f0f67e9248b83e000000',
                workflowId,
                category,
                name
              }
            })
          }
        }
      });
    },
    resetForm() {
      this.dialogVisible = false
      this.$refs['ruleForm'].resetFields();
    }
  }
}
</script>