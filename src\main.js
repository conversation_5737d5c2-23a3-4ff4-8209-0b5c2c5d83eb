import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import '@/assets/style/base.scss'
import '@/assets/style/common.scss'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import moment from 'moment'//导入文件
import '@/libs/development.js'
import '@/libs/directive.js'
import SvgIcon from '@/components/SvgIcon'// svg组件

// 全局注册SvgIcon组件
Vue.component('svg-icon', SvgIcon)
// 载入所有svg icon
const requireContext = require.context('./assets/icons', false, /\.svg$/)
requireContext.keys().forEach(requireContext)

Vue.config.productionTip = false
Vue.use(ElementUI);
Vue.prototype.$moment = moment;//赋值使用
moment.locale('zh-cn');//需要汉化
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
