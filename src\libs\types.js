// 常量js
// 组件状态
export const componentMapping = {
  EXECUTING: {
    label: '执行中',
    icon: 'loading',
    value: 'EXECUTING'
  },
  FAIL: {
    label: '失败',
    icon: 'error',
    value: 'FAIL'
  },
  COMPLETED: {
    label: '成功',
    icon: 'success',
    value: 'COMPLETED'
  },
  UNSETING: {
    label: '未配置',
    icon: 'warning',
    value: 'UNSETING'
  },
  NOTRUNNING: {
    label: '未运行',
    icon: 'funnel',
    value: 'NOTRUNNING'
  }
}
// 流程状态
export const processStatusMapping = {
  SUBMITTING: {
    label: '提交中',
    value: 'SUBMITTING'
  },
  UN_EXECUTE: {
    label: '待执行',
    value: 'UN_EXECUTE'
  },
  EXECUTING: {
    label: '执行中',
    value: 'EXECUTING'
  },
  STOP_EXECUTE: {
    label: '停止执行',
    value: 'STOP_EXECUTE'
  },
  COMPLETED: {
    label: '执行完成',
    value: 'COMPLETED'
  },
  IGNORE: {
    label: '忽略',
    value: 'IGNORE'
  },
  FAIL: {
    label: '执行失败',
    value: 'FAIL'
  },
}