<template>
  <div id="context-menu"
    :style="{ left: position.x + (nodeSelectorHide ? 0 : 232) + 'px', top: position.y + 'px', display: visible ? 'block' : 'none' }">
    <ul>
      <li class="menu-item" @click="config">
        <em class="el-icon-setting"></em> 配置
      </li>
      <li class="menu-item" @click="deleteNode">
        <em class="el-icon-delete"></em> 删除
      </li>
      <li class="menu-item" @click="copy">
        <em class="el-icon-document-copy"></em> 复制
      </li>
      <li class="menu-item" @click="handleRename">
        <em class="el-icon-edit-outline"></em> 重命名
      </li>
      <li class="menu-item" @click="clearFunctionParam('')">
        <em class="el-icon-takeaway-box"></em> 清空配置
      </li>
    </ul>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { bus } from "@/libs/bus";
import { deleteNode, updateName, nodeCopy, updateFunctionParamClear } from "@/api/https.js";

export default {
  name: "contextMenu",
  props: {
    position: {
      //右键菜单的坐标
      required: true,
      type: null,
      default: () => ({ x: 0, y: 0 })
    },
    visible: {
      // 右键菜单是否显示
      required: true,
      type: Boolean,
      default: false
    },
    currentNode: {
      // 当前右键选中的组件
      required: true,
      type: null
    }
  },
  computed: {
    ...mapState(["nodeSelectorHide"])
  },
  methods: {
    // 配置
    config() {
      this.$emit("contextMenuEvent", "config");
    },
    // 删除组件
    async deleteNode() {
      this.$emit("contextMenuEvent");
      await this.$confirm('删除此节点会同时删除相关连接线并清空配置，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const params = {
          moduleId: this.currentNode.id
        };
        const res = await deleteNode(params);
        if (res.code == 200) {
          this.$emit("contextMenuEvent", "deleteNode");
        } else {
          this.$emit("contextMenuEvent", "getWorkflow");
        }
      }).catch(() => {
      })
    },

    // 重命名
    handleRename() {
      this.$emit("contextMenuEvent");
      this.$prompt("请输入名称", "名称修改", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        closeOnPressEscape: false,
        inputPattern: /^[\u4e00-\u9fa5_a-zA-Z0-9_]{2,16}$/,
        inputErrorMessage: "2-16个中英文、数字、下划线",
        inputValue: this.currentNode.attrs.label.text
      })
        .then(async ({ value }) => {
          const params = {
            // id: this.currentNode.id,
            name: value
          }
          const res = await updateName(params, this.currentNode.id)
          if (res.code == 200) {
            // this.currentNode.attr("label/text", value);
            bus.$emit('refresh')
          } else {
            this.$emit("contextMenuEvent", "getWorkflow");
          }
        })
        .catch(() => {

        });
    },

    // 复制组件
    async copy() {
      const nodeHeight = 85;
      const params = {
        id: this.currentNode.id
      }
      const { x, y } = this.currentNode.position()
      const data = {
        x: x,
        y: y + nodeHeight
      }
      await nodeCopy(params, data)
      this.$emit("contextMenuEvent", "getWorkflow");
    },

    // 清空组件参数配置
    async clearFunctionParam(id) {
      // const params = {
      //   // id: this.currentNode.id,
      //   functionParam: null
      // }
      const res = await updateFunctionParamClear(id || this.currentNode.id)
      if (res.code == 200) {
        this.$emit("contextMenuEvent", 'clearFunctionParam')
        if (id) return
        this.$message({
          showClose: true,
          message: '清空成功',
          type: 'success'
        })
      } else {
        this.$emit("contextMenuEvent", "getWorkflow");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
#context-menu {
  position: absolute;
  z-index: 3;
  left: 0;
  top: 0;
  width: 140px;
  background-color: #fff;
  box-sizing: border-box;
  border: 1px solid #dadada;
  box-shadow: 0 0 10px 0px #dadada;
  user-select: none;

  .menu-item {
    height: 35px;
    line-height: 35px;
    cursor: pointer;
    padding-left: 15px;

    &:hover {
      background-color: #efefef;
      color: #329ffb;
    }
  }
}
</style>