import feach from '@/libs/feach.js'
// import { workflowAdd } from '@/api/https.js'
// import Vue from 'vue'
// import App from '../App.vue'
// import router from '../router'
// import store from '../store'

if (process.env.NODE_ENV === 'development') {
  // 开发环境默认token
  if (!window.dataOsToken) {
    window.dataOsToken = '24bc6ceb49bf4f558f99b33fb6fa12d5000000'
    feach.defaults.headers.accessToken = window.dataOsToken
  }

  if (!window.applicationCode) {
    feach.defaults.headers.applicationCode = 'ai20230403001'
  }

  // if (!window.GetQueryValue('workflowId')) {
  //   workflowAdd({
  //     category: "SPARK",
  //     name: Math.random().toString(36).slice(-6)
  //   }).then(res => {
  //     const { category, name, workflowId } = res.result
  //     location.href = `/#/home?workflowId=${workflowId}&category=${category}&name=${name}`
  //     new Vue({
  //       router,
  //       store,
  //       render: h => h(App)
  //     }).$mount('#app')

  //   })

  // } else {
  //   new Vue({
  //     router,
  //     store,
  //     render: h => h(App)
  //   }).$mount('#app')
  // }

}