<!-- 调度配置 -->
<template>
  <section class="right-config-panel" :style="{right: $store.state.rightConfigActiveName === '调度配置' ? 0 : ''}">
    <div class="right-config-form">
      <p class="configTitle">调度配置</p>
      <p class="configWay">调度方式</p>
      <el-radio-group v-model="dispatchWay" @change="dispatchWayChange">
        <el-radio label="SINGLE">单次调度</el-radio>
        <el-radio label="ROUND">周期调度</el-radio>
        <el-radio label="CRON">cron调度</el-radio>
        <!-- <el-radio label="DEPENDENT">依赖调度</el-radio> -->
      </el-radio-group>
      <p class="configWay" style="margin: 14px 0 10px 0">调度属性</p>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top" label-width="100px" class="demo-ruleForm">
        <div v-if="dispatchWay === 'SINGLE'">
          <el-form-item label="执行时间" prop="executeTime">
            <el-date-picker style="width:100%" :default-value="new Date().getTime() + 360 * 1000" @change="currentTime = new Date().getTime()" v-model="form.executeTime" :picker-options="pickerBeginDateBefore" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择开始时间"></el-date-picker>
          </el-form-item>
        </div>
        <div v-if="dispatchWay === 'ROUND'">
          <el-form-item label="生效时间" prop="effectStartTime" :rules="[{ required: true, validator: effectStartTimeCheck, trigger: 'blur' }]">
            <span style="display: inline-block; width:43px">开始</span>
            <el-date-picker class="dateClass" style="width:260px" @change="currentTime = new Date().getTime()" v-model="form.effectStartTime" @focus="dateFocus" @blur="dateBlur" :picker-options="pickerBeginDateBefore1" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择开始时间"></el-date-picker>
            <span style="display: inline-block; width:43px">截止</span>
            <el-date-picker class="dateClass" style="width:260px;margin-top: 10px" @change="currentTime = new Date().getTime()" v-model="form.effectEndTime" @focus="dateFocus" @blur="dateBlur" :picker-options="pickerBeginDateBefore1" type="datetime" :disabled="continuous" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择截止时间"></el-date-picker>
          </el-form-item>
          <el-checkbox v-model="continuous" @change="continuousChange">持续生效</el-checkbox>
          <el-form-item label="调度周期" prop="dispatchCycle">
            <el-select v-model="form.dispatchCycle" style="width:100%" @change="dispatchCycleChange" placeholder="请选择调度周期">
              <el-option
                v-for="item in cycleOption"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <div v-if="form.dispatchCycle === 'minute'">
            <el-form-item label="范围" prop="scopeStartTime" class="itemAline" :rules="[{ required: true, validator: scopeStartTimeCheck, trigger: 'blur' }]">
              <!-- <span style="display: inline-block; width:35px"></span> -->
              <el-select v-model="form.scopeStartTime" style="width:121px" placeholder="起始时间">
                <el-option
                  v-for="item in hoursOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              ~
              <el-select v-model="form.scopeEndTime" style="width:121px" placeholder="结束时间">
                <el-option
                  v-for="item in hoursOption"
                  :disabled="parseInt(item.value) < parseInt(form.scopeStartTime)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="间隔时间" prop="intervalStartTime" :rules="[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]">
              <span style="display: inline-block; width:43px">从</span>
              <el-select v-model="form.intervalStartTime" style="width:121px" placeholder="起始时间">
                <el-option
                  v-for="item in minOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              ~
              <el-select v-model="form.intervalEndTime" style="width:121px" placeholder="结束时间">
                <el-option
                  v-for="item in minOption"
                  :disabled="parseInt(item.value) <= parseInt(form.intervalStartTime)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <div style="margin-top: 15px">
                <span style="display: inline-block; width:43px">间隔</span>
                <el-select v-model="form.spacer" style="width:260px" placeholder="结束时间">
                  <el-option
                    v-for="item in spacerOptionMin"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="具体时间(单位秒)" prop="specificTime">
              <el-time-picker
                v-model="form.specificTime"
                style="width:100%"
                class="ss"
                value-format="ss"
                format="ss"
                placeholder="请选择具体时间">
              </el-time-picker>
            </el-form-item>
          </div>
          <div v-if="form.dispatchCycle === 'hour'">
            <el-form-item label="间隔时间" prop="intervalStartTime" :rules="[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]">
              <span style="display: inline-block; width:43px">从</span>
              <el-select v-model="form.intervalStartTime" style="width:121px" placeholder="起始时间">
                <el-option
                  v-for="item in hoursOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              ~
              <el-select v-model="form.intervalEndTime" style="width:121px" placeholder="结束时间">
                <el-option
                  v-for="item in hoursOption"
                  :disabled="parseInt(item.value) <= parseInt(form.intervalStartTime)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <div style="margin-top: 15px">
                <span style="display: inline-block; width:43px">间隔</span>
                <el-select v-model="form.spacer" style="width:260px" placeholder="结束时间">
                  <el-option
                    v-for="item in spacerOptionHour"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item label="具体时间(单位分秒)" prop="specificTime">
              <el-time-picker
                v-model="form.specificTime"
                style="width:100%"
                value-format="mm:ss"
                format="mm:ss"
                placeholder="请选择具体时间">
              </el-time-picker>
            </el-form-item>
          </div>
          <div v-if="form.dispatchCycle === 'day'">
            <el-form-item label="间隔时间" prop="intervalStartTime" :rules="[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]">
              <span style="display: inline-block; width:43px">从</span>
              <el-select v-model="form.intervalStartTime" style="width:121px" placeholder="起始时间">
                <el-option
                  v-for="item in spacerOptionDay"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              ~
              <el-select v-model="form.intervalEndTime" style="width:121px" placeholder="结束时间">
                <el-option
                  v-for="item in spacerOptionDay"
                  :disabled="parseInt(item.value) <= parseInt(form.intervalStartTime)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <div style="margin-top: 15px">
                <span style="display: inline-block; width:43px">间隔</span>
                <el-select v-model="form.spacer" style="width:260px" placeholder="结束时间">
                  <el-option
                    v-for="item in intervalOptionDay"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
            <!-- 这里开始 -->
            <el-form-item label="具体时间(单位时分秒)" prop="specificTime">
              <el-time-picker
                style="width:100%"
                value-format="HH:mm:ss"
                format="HH:mm:ss"
                v-model="form.specificTime"
                placeholder="请选择具体时间">
              </el-time-picker>
            </el-form-item>
          </div>
          <div v-if="form.dispatchCycle === 'week'">
            <el-form-item label="选择周期" prop="weekTime">
              <el-dropdown trigger="click" style="width: 100%">
                <span class="el-dropdown-link">
                  <el-input 
                    placeholder="请选择周期、支持多选" 
                    v-model="form.weekTime"
                    readonly
                    suffix-icon="el-icon-arrow-down"
                  />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <div class="dropdownBox">
                    <div style="height: 150px;overflow: auto;">
                      <div class="dropdownTitle">每周</div>
                      <div style="display: flex;flex-wrap: wrap;">
                        <div v-for="item in spacerOptionWeek" class="chooseStyle" :class="currentClass(item)" :key="item.value">
                          <div class="labelStyle" @click="weekClick(item)">{{ item.label }}</div>
                        </div>
                      </div>
                      
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </el-form-item>
            <el-form-item label="具体时间(单位时分秒)" prop="specificTime">
              <el-time-picker
                style="width:100%"
                value-format="HH:mm:ss"
                format="HH:mm:ss"
                v-model="form.specificTime"
                placeholder="请选择具体时间">
              </el-time-picker>
            </el-form-item>
          </div>
          <div v-if="form.dispatchCycle === 'month'">
            <el-form-item label="选择周期" prop="dayTime">
              <el-dropdown trigger="click" style="width: 100%">
                <span class="el-dropdown-link">
                  <el-input 
                    placeholder="请选择周期、支持多选" 
                    v-model="form.dayTime"
                    readonly
                    suffix-icon="el-icon-arrow-down"
                  />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <div class="dropdownBox">
                    <div style="height: 260px;overflow: auto;">
                      <div class="dropdownTitle">每月</div>
                      <div style="display: flex;flex-wrap: wrap;">
                        <div v-for="item in optionDay" class="daychooseStyle" :class="currentClassDay(item)" :key="item.value">
                          <div class="daylabelStyle" @click="dayClick(item)">{{ item.value }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </el-form-item>
            <el-form-item label="具体时间(单位时分秒)" prop="specificTime">
              <el-time-picker
                style="width:100%"
                value-format="HH:mm:ss"
                format="HH:mm:ss"
                v-model="form.specificTime"
                placeholder="请选择具体时间">
              </el-time-picker>
            </el-form-item>
          </div>
        </div>
        <div v-if="dispatchWay === 'CRON'">
          <el-form-item label="生效时间" prop="effectStartTime" :rules="[{ required: true, validator: effectStartTimeCheck, trigger: 'blur' }]">
            <span style="display: inline-block; width:43px">开始</span>
            <el-date-picker class="dateClass" style="width:260px" @change="currentTime = new Date().getTime()" v-model="form.effectStartTime" :picker-options="pickerBeginDateBefore1" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择开始时间"></el-date-picker>
            <span style="display: inline-block; width:43px">截止</span>
            <el-date-picker class="dateClass" style="width:260px;margin-top: 10px" @change="currentTime = new Date().getTime()" v-model="form.effectEndTime" :picker-options="pickerBeginDateBefore1" type="datetime" :disabled="continuous" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择结束时间"></el-date-picker>
          </el-form-item>
          <el-checkbox v-model="continuous" @change="continuousChange">持续生效</el-checkbox>
          <el-form-item label="cron表达式" :rules="[{ required: true, validator: cronCheck, trigger: 'blur' }]" prop="cron">
            <el-input v-model="form.cron" placeholder="请输入cron表达式"></el-input>
          </el-form-item>
          <el-form-item label="测试">
            <div style="display:flex; justify-content: space-between;">
              <el-input-number v-model="testNum" controls-position="right" :min="1" :precision="0" :max="200"></el-input-number>
              <el-button type="primary" plain @click="executeCron">执行</el-button>
            </div>
          </el-form-item>
          <el-form-item label="最近运行时间">
            <el-table
              height="240"
              :data="elapsedTableData"
              style="width: 100%"
              :header-cell-style="{'background-color': '#F5F7FA','color': '#909399'}"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100">
              </el-table-column>
              <el-table-column
                prop="executeTime"
                label="执行时间">
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
        <el-form-item style="text-align:center;padding-top: 5px;">
          <el-button plain @click="empty('ruleForm')">清空</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </section>
</template>

<script>
import { scheduleAdd, scheduleInfo, cronValid, scheduleClear, commonCron, scheduleUpdate } from '@/api/workflowConfig'
import {hoursOption, cycleOption, minOption, spacerOptionMin, spacerOptionHour, spacerOptionDay, spacerOptionWeek, optionDay, secOption, intervalOptionDay} from "../../libs/common.js";
import rules from '../../libs/validDate.js'
import { bus } from '@/libs/bus'
export default {
  name: 'rightConfigDispatch',
  data() {
    return {
      rules,
      isAdd: false,
      scheduleId: '',
      lastSaveData: '',
      workflowId: '',
      dayOfWeeks: [],
      showWeeks: [],
      dayOfMonths: [],
      showMonths: [],
      testNum: 10,
      elapsedTableData: [],
      dispatchWay: 'SINGLE',
      form: {
        executeTime: '', // 执行时间
        effectStartTime: new Date(), // 生效开始时间
        effectEndTime: '', // 生效结束时间
        dispatchCycle: '', // 调度周期
        scopeStartTime: '0', // 范围开始时间
        scopeEndTime: '23', // 范围结束时间
        intervalStartTime: '0', // 间隔开始时间
        intervalEndTime: '59', // 间隔结束时间
        spacer: '30', // 间隔区间
        specificTime: '0', // 具体时间
        weekTime: '', // 周选择时间
        dayTime: '', // 月选择时间
        cron: ''
      },
      hoursKey: 1,
      hoursOption,
      cycleOption,
      minOption,
      spacerOptionMin,
      spacerOptionHour,
      secOption,
      spacerOptionDay,
      intervalOptionDay,
      spacerOptionWeek,
      optionDay,
      continuous: true,
      currentTime: new Date().getTime()
    }
  },
  watch: {
    '$store.state.rightConfigActiveName'(val) {
      if (val === '调度配置') {
        this.getScheduleInfo()
      }
      // this.currentTime = new Date().getTime()
    }
  },
  computed:{
    pickerBeginDateBefore1() {
      //选择的日期
      let curDate
      if (this.form.effectStartTime) {
        curDate = this.$moment(this.form.effectStartTime).format("YYYY-MM-DD");
      } else {
        curDate = this.$moment(this.currentTime).format("YYYY-MM-DD");
      }
      //最小日期
      let minDate = this.$moment(Date.now()).format("YYYY-MM-DD");
      // 如果选择的日期为最小日期，则设置为最小日期的时分秒
      let str = "";
      if(curDate == minDate){
        str = this.$moment(Date.now()).format("HH:mm:ss");
      }else{
        str = "00:00:00"
      }
      return {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7;
        },
        selectableRange: str + " - 23:59:59"
      }
    },
    pickerBeginDateBefore(){
      //选择的日期
      let curDate
      if (this.form.executeTime) {
        curDate = this.$moment(this.form.executeTime).format("YYYY-MM-DD");
      } else {
        curDate = this.$moment(this.currentTime).format("YYYY-MM-DD");
      }
      //最小日期
      let minDate = this.$moment(Date.now()).format("YYYY-MM-DD");
      // 如果选择的日期为最小日期，则设置为最小日期的时分秒
      let str = "";
      if(curDate == minDate){
        str = this.$moment(Date.now() + 360 * 1000).format("HH:mm:ss");
      }else{
        str = "00:00:00"
      }
      return {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7;
        },
        selectableRange: str + " - 23:59:59"
      }
    },
  },
  created() {
    // this.workflowId = this.$route.query.workflowId
    this.workflowId = window.GetQueryValue('workflowId')
    // this.category = window.GetQueryValue('category')
    this.getScheduleInfo()
  },
  mounted() {
    // this.getScheduleInfo()
  },
  methods: {
    getScheduleInfo() {
      scheduleInfo({workflowId: this.workflowId}).then((res) => {
        if (res.code === 200) {
          if (res.result[0]) {
            this.scheduleId = res.result[0].scheduleId
            bus.$emit('scheduleId', this.scheduleId)
            this.isAdd = false
            this.lastSaveData = res.result[0]
            this.dispatchWay = res.result[0].strategyType
            if (this.dispatchWay === 'ROUND') {
              this.form.dispatchCycle = res.result[0].period.toLowerCase()
              // this.continuous = res.result.continuedFlag === 'CONTINUED' ? true : false
              this.continuous = res.result[0].endTime ? false : true
              this.form.effectStartTime = res.result[0].startTime
              this.form.effectEndTime = res.result[0].endTime ? res.result[0].endTime : ''
              switch(this.form.dispatchCycle) {
                case 'minute':
                  this.form.scopeStartTime = res.result[0].hourRange.split(',')[0]
                  this.form.scopeEndTime = res.result[0].hourRange.split(',')[1]
                  this.form.intervalStartTime = res.result[0].minuteRange.split(',')[0]
                  this.form.intervalEndTime = res.result[0].minuteRange.split(',')[1]
                  this.form.spacer = res.result[0].timeInterval + ''
                  this.form.specificTime = res.result[0].second + ''
                break
                case 'hour':
                  this.form.intervalStartTime = res.result[0].minuteRange.split(',')[0]
                  this.form.intervalEndTime = res.result[0].minuteRange.split(',')[1]
                  this.form.spacer = res.result[0].timeInterval + ''
                  this.form.specificTime = res.result[0].minute + ':' + res.result[0].second
                break
                case 'day':
                  this.form.intervalStartTime = res.result[0].dayRange.split(',')[0]
                  this.form.intervalEndTime = res.result[0].dayRange.split(',')[1]
                  this.form.spacer = res.result[0].timeInterval + ''
                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second
                break
                case 'week':
                  this.dayOfWeeks = res.result[0].dayOfWeeks
                  this.dayOfWeeks.forEach(x => {
                    if (x == '1') {
                      this.showWeeks.push('星期一')
                    } else if (x == '2') {
                      this.showWeeks.push('星期二')
                    } else if (x == '3') {
                      this.showWeeks.push('星期三')
                    } else if (x == '4') {
                      this.showWeeks.push('星期四')
                    } else if (x == '5') {
                      this.showWeeks.push('星期五')
                    } else if (x == '6') {
                      this.showWeeks.push('星期六')
                    } else if (x == '7') {
                      this.showWeeks.push('星期日')
                    }
                  })
                  this.form.weekTime = this.showWeeks.toString()
                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second
                break
                case 'month':
                  this.dayOfMonths = res.result[0].dayOfMonths
                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second
                  this.dayOfMonths.forEach(x => {
                    this.showMonths.push(x + '号')
                  })
                  this.form.dayTime = this.showMonths.toString()
                break
              }
            } else if (this.dispatchWay === 'SINGLE') {
              this.form.executeTime = res.result[0].startTime
            } else if (this.dispatchWay === 'CRON') {
              this.continuous = res.result[0].endTime ? false : true
              this.form.effectStartTime = res.result[0].startTime
              this.form.effectEndTime = res.result[0].endTime ? res.result[0].endTime : ''
              this.form.cron = res.result[0].cronExp
            }
          } else {
            this.isAdd = true
          }
        }
      })
    },
    executeCron() {
      // this.$refs.ruleForm.
      this.$refs.ruleForm.validateField("cron", errMsg => {
        if (!errMsg) {
          commonCron({cronExp: this.form.cron, testSize: this.testNum}).then((res) => {
            if (res.code === 200) {
              this.elapsedTableData = []
              res.result.forEach(x => {
                this.elapsedTableData.push({executeTime: x})
              })
            }
          })
        }
      });
    },
    async cronCheck(rule, value, callback) {
      if (!value) {
        return callback(new Error('请输入cron表达式'))
      }
      if (value.trim() === '') {
        callback(new Error('请输入cron表达式'))
      }
      const res = await cronValid({cronExp: value})
      if (res.result === true) {
        callback()
      } else {
        callback(new Error('请输入正确的cron表达式'))
      }
    },
    effectStartTimeCheck(rule, value, callback){
      if (!value) {
        return callback(new Error('请选择开始时间'))
      }
      if (!this.continuous) {
        if (!this.form.effectEndTime) {
          return callback(new Error('请选择截止时间'))
        } else {
          if (new Date(this.form.effectEndTime).getTime() <= new Date(value).getTime()) {
            return callback(new Error('截止时间不能小于或等于开始时间'))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    },
    dispatchWayChange(val) {
      this.$refs.ruleForm.clearValidate()
      if (this.lastSaveData) {
        if (val === this.lastSaveData.strategyType) {
          if (val === 'ROUND') {
            this.form.dispatchCycle = this.lastSaveData.scheduleConfig.period.toLowerCase()
            this.continuous = this.lastSaveData.scheduleConfig.endTime ? false : true
            this.form.effectStartTime = this.lastSaveData.scheduleConfig.startTime
            this.form.effectEndTime = this.lastSaveData.scheduleConfig.endTime ? this.lastSaveData.scheduleConfig.endTime : ''
            switch(this.form.dispatchCycle) {
              case 'minute':
                this.form.scopeStartTime = this.lastSaveData.scheduleConfig.hourRange.split(',')[0]
                this.form.scopeEndTime = this.lastSaveData.scheduleConfig.hourRange.split(',')[1]
                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[0]
                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[1]
                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval
                this.form.specificTime = this.lastSaveData.scheduleConfig.second + ''
              break
              case 'hour':
                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[0]
                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[1]
                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval
                this.form.specificTime = this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second
              break
              case 'day':
                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.dayRange.split(',')[0]
                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.dayRange.split(',')[1]
                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval
                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second
              break
              case 'week':
                this.dayOfWeeks = this.lastSaveData.scheduleConfig.dayOfWeeks
                this.dayOfWeeks.forEach(x => {
                  if (x == '1') {
                    this.showWeeks.push('星期一')
                  } else if (x == '2') {
                    this.showWeeks.push('星期二')
                  } else if (x == '3') {
                    this.showWeeks.push('星期三')
                  } else if (x == '4') {
                    this.showWeeks.push('星期四')
                  } else if (x == '5') {
                    this.showWeeks.push('星期五')
                  } else if (x == '6') {
                    this.showWeeks.push('星期六')
                  } else if (x == '7') {
                    this.showWeeks.push('星期日')
                  }
                })
                this.form.weekTime = this.showWeeks.toString()
                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second
              break
              case 'month':
                this.dayOfMonths = this.lastSaveData.scheduleConfig.dayOfMonths
                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second
                this.dayOfMonths.forEach(x => {
                  this.showMonths.push(x + '号')
                })
                this.form.dayTime = this.showMonths.toString()
              break
            }
          } else if (val === 'SINGLE') {
            document.getElementsByTagName('body')[0].className = ''
            this.form.executeTime = new Date(this.lastSaveData.scheduleConfig.startTime)
          } else if (val === 'CRON') {
            document.getElementsByTagName('body')[0].className = ''
            // this.continuous = this.lastSaveData.continuedFlag === 'CONTINUED' ? true : false
            this.continuous = this.lastSaveData.scheduleConfig.endTime ? false : true
            this.form.effectStartTime = this.lastSaveData.scheduleConfig.startTime
            this.form.effectEndTime = this.lastSaveData.scheduleConfig.endTime ? this.lastSaveData.scheduleConfig.endTime : ''
            this.form.cron = this.lastSaveData.scheduleConfig.cronExp
          }
        } else {
          document.getElementsByTagName('body')[0].className = ''
          if (val === 'SINGLE') {
            this.form.executeTime = ''
          } else if (val === 'ROUND') {
            this.form.dispatchCycle = ''
            this.continuous = true
            this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
            this.form.effectEndTime = ''
          } else if (val === 'CRON') {
            this.continuous = true
            this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
            this.form.effectEndTime = ''
            this.form.cron = ''
            this.testNum = 10
            this.elapsedTableData = []
          }
        }
      } else {
        document.getElementsByTagName('body')[0].className = ''
        if (val === 'SINGLE') {
          this.form.executeTime = ''
        } else if (val === 'ROUND') {
          this.form.dispatchCycle = ''
          this.continuous = true
          this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
          this.form.effectEndTime = ''
        } else if (val === 'CRON') {
          this.continuous = true
          this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
          this.form.effectEndTime = ''
          this.form.cron = ''
          this.testNum = 10
          this.elapsedTableData = []
        }
      }
    },
    dateFocus() {
      this.bodyClass = JSON.parse(JSON.stringify(document.getElementsByTagName('body')[0].className))
      document.getElementsByTagName('body')[0].className = ''
    },
    dateFocus1() {
      document.getElementsByTagName('body')[0].className = ''
    },
    dateBlur() {
      document.getElementsByTagName('body')[0].className = this.bodyClass
    },
    scopeStartTimeCheck(rule, value, callback){
      if (!value) {
        return callback(new Error('请选择范围起始时间'))
      }
      if (!this.form.scopeEndTime) {
        return callback(new Error('请选择范围结束时间'))
      }
      if (parseInt(this.form.scopeEndTime) < parseInt(value)) {
        return callback(new Error('范围结束时间必须大于等于起始时间'))
      } else {
        callback()
      }
    },
    intervalStartTimeCheck(rule, value, callback){
      if (!value) {
        return callback(new Error('请选择间隔起始时间'))
      }
      if (!this.form.intervalEndTime) {
        return callback(new Error('请选择间隔结束时间'))
      }
      if (!this.form.spacer) {
        return callback(new Error('请选择间隔时间段'))
      }
      if (parseInt(this.form.intervalEndTime) <= parseInt(value)) {
        return callback(new Error('间隔结束时间必须大于起始时间'))
      } else {
        callback()
      }
    },
    dispatchCycleChange(val) {
      this.$refs.ruleForm.clearValidate()
      if (val === 'minute') {
        this.form.spacer = '30'
      } else if (val === 'hour' || val === 'day') {
        this.form.spacer = '1'
      } else {
        this.form.spacer = ''
      }
      if (val === 'day' || val === 'week' || val === 'month') {
        document.getElementsByTagName('body')[0].className = ''
        this.form.specificTime = '00:00:00'
      } else if (val === 'hour') {
        document.getElementsByTagName('body')[0].className = 'mmss'
        this.form.specificTime = '00:00'
      } else {
        document.getElementsByTagName('body')[0].className = 'ss'
        this.form.specificTime = '00'
      }
      if (val === 'minute') {
        this.form.intervalStartTime = '0'
        this.form.intervalEndTime = '59'
      } else if (val === 'hour') {
        this.form.intervalStartTime = '0'
        this.form.intervalEndTime = '23'
      } else if (val === 'day') {
        this.form.intervalStartTime = '1'
        this.form.intervalEndTime = '31'
      }
    },
    continuousChange(val) {
      if (val) {
        this.form.effectEndTime = ''
      }
    },
    currentClass(item) {
      return [this.dayOfWeeks.indexOf(item.value) > -1 ? 'ccc' : '']
    },
    currentClassDay(item) {
      return [this.dayOfMonths.indexOf(item.value) > -1 ? 'ccc' : '']
    },
    weekClick(item) {
      var index = this.dayOfWeeks.indexOf(item.value)
      if (index < 0) {
        this.showWeeks.push(item.label)
        this.dayOfWeeks.push(item.value)
      } else {
        this.showWeeks.splice(index, 1)
        this.dayOfWeeks.splice(index, 1)
      }
      this.form.weekTime = this.showWeeks.toString()
    },
    dayClick(item) {
      var index = this.dayOfMonths.indexOf(item.value)
      if (index < 0) {
        this.showMonths.push(item.label)
        this.dayOfMonths.push(item.value)
      } else {
        this.showMonths.splice(index, 1)
        this.dayOfMonths.splice(index, 1)
      }
      this.form.dayTime = this.showMonths.toString()
    },
    empty(formName) {
      if (!this.scheduleId) {
        this.$refs[formName].resetFields()
        this.$message({
          type: 'success',
          message: '清空成功'
        })
        return 
      }
      scheduleClear({workflowId: this.workflowId, scheduleId: this.scheduleId}).then((res) => {
        if (res.code === 200) {
          this.isAdd = true
          this.$refs[formName].clearValidate()
          if (this.dispatchWay === 'SINGLE') {
            this.form.executeTime = ''
          } else if (this.dispatchWay === 'ROUND') {
            this.form.dispatchCycle = ''
            this.continuous = true
            this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss")
            this.form.effectEndTime = ''
            setTimeout(() => {
              this.$refs[formName].clearValidate()
            }, 0);
          } else if (this.dispatchWay === 'CRON') {
            this.continuous = true
            this.form.effectStartTime = this.$moment(Date.now()).format("YYYY-MM-DD HH:mm:ss");
            this.form.effectEndTime = ''
            this.form.cron = ''
            this.testNum = 10
            this.elapsedTableData = []
          }
          this.lastSaveData = ''
          this.$message({
            type: 'success',
            message: '清空成功'
          })
        }
      })
     
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = this.getParam()
          if (this.isAdd) {
            scheduleAdd(param, this.workflowId).then((res) => {
              if (res.code === 200) {
                this.$message({
                  type: 'success',
                  message: '保存成功'
                })
                this.scheduleId = res.result
                bus.$emit('scheduleId', this.scheduleId)
                this.isAdd = false
                this.lastSaveData = param
              }
              
            })
          } else {
            scheduleUpdate(param, this.workflowId, this.scheduleId).then((res) => {
              if (res.code === 200) {
                this.$message({
                  type: 'success',
                  message: '保存成功'
                })
                this.isAdd = false
                this.lastSaveData = param
              }
            })
          }
          
        } else {
          return false;
        }
      });
    },
    getParam() {
      var obj = {}
      // obj.executeWay = this.dispatchWay
      // obj.workflowId = this.workflowId
      // obj.scheduleConfig = {}
      obj.strategyType = this.dispatchWay
      // if (this.dispatchWay !== 'ROUND') {
      //   obj.continuedFlag = 'INIT'
      // } else {
      //   if (this.continuous) {
      //     obj.continuedFlag = 'CONTINUED'
      //   } else {
      //     obj.continuedFlag = 'INIT'
      //   }
      // }
      if (this.dispatchWay === 'SINGLE') {
        obj.startTime = this.form.executeTime
      } else if (this.dispatchWay === 'ROUND') {
        obj.startTime = this.form.effectStartTime
        obj.endTime = this.form.effectEndTime
        obj.period = this.form.dispatchCycle.toUpperCase()
        switch(this.form.dispatchCycle) {
          case 'minute':
            obj.hourRange = this.form.scopeStartTime + ',' + this.form.scopeEndTime
            obj.minuteRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime
            obj.timeInterval = this.form.spacer
            obj.second = this.form.specificTime
          break
          case 'hour':
            obj.minuteRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime
            obj.timeInterval = this.form.spacer
            obj.minute = this.form.specificTime.split(':')[0]
            obj.second = this.form.specificTime.split(':')[1]
          break
          case 'day':
            obj.dayRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime
            obj.timeInterval = this.form.spacer
            obj.hour = this.form.specificTime.split(':')[0]
            obj.minute = this.form.specificTime.split(':')[1]
            obj.second = this.form.specificTime.split(':')[2]
          break
          case 'week':
            obj.dayOfWeeks = this.dayOfWeeks
            obj.hour = this.form.specificTime.split(':')[0]
            obj.minute = this.form.specificTime.split(':')[1]
            obj.second = this.form.specificTime.split(':')[2]
          break
          case 'month':
            obj.dayOfMonths = this.dayOfMonths
            obj.hour = this.form.specificTime.split(':')[0]
            obj.minute = this.form.specificTime.split(':')[1]
            obj.second = this.form.specificTime.split(':')[2]
          break
        }
      } else if (this.dispatchWay === 'CRON') {
        obj.startTime = this.form.effectStartTime
        obj.endTime = this.form.effectEndTime
        // obj.period = this.form.dispatchCycle.toUpperCase()
        obj.cronExp = this.form.cron
      }
      return obj
    }
  }
  
}
</script>

<style lang="scss" scoped>
// ::v-deep .ss{
//   .el-time-spinner.has-seconds .el-time-spinner__wrapper{
//     background: aquamarine;
//   }
//   .el-time-spinner:first-child{
//     display: none;
//   }
//   .el-time-spinner:nth-child(2) {
//     width: 50%;
//   }
// }
::v-deep .right-config-form{
  padding:16px 24px;
  .el-radio{
    // display: block;
    margin-bottom: 16px;
  }
  .el-radio__input.is-checked .el-radio__inner{
    border-color: #276EB7;
    background: #276EB7;
  }
  .el-radio__input.is-checked+.el-radio__label{
    color: #276EB7;
  }
  .el-button--primary{
    background-color: #276EB7;
    border-color: #276EB7;
  }
  .el-button--primary.is-plain{
    color: #409EFF;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
  .el-form--label-top .el-form-item__label{
    padding: 0;
  }
  .el-form-item{
    margin-bottom: 15px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: #276EB7;
    border-color: #276EB7;
  }
  .el-checkbox__input.is-checked+.el-checkbox__label{
    color: #276EB7;
  }
  .dateClass{
    // .el-input__inner{
    //   padding: 0 0 0 20px !important
    // }
    // .el-input__prefix{
    //   left: 0;
    // }
    // .el-input__suffix{
    //   right: -4px
    // }
  }
  .itemAline{
    display: flex;
    .el-form-item__label{
      width: 43px;
    }
  }
}
.configTitle{
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 800;
  color: #303133;
  margin-bottom: 24px;
}
.configWay{
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #303133;
  margin-bottom: 20px;
}
.dropdownBox{
    width: 300px;
    padding: 10px;
  }
.dropdownTitle{
  text-align: center;
  font-size: 16px;
  padding-bottom: 20px;
  border-bottom: 1px solid #DCDFE6;
}
.chooseStyle{
  margin: 8px 25px;
}
.labelStyle{
  cursor: pointer;
}
.daychooseStyle{
  margin: 8px 12px;
}
.daylabelStyle{
  width: 20px;
  cursor: pointer;
}
.ccc{
  color:#1570C4;
}
</style>