// Token ->来源（数据治理传递）
window.dataOsToken = dataOsToken
window.accountId = accountId

// 前端各组件地址
window.componentsUrl = []

// 测试环境地址
window.applicationServerPath = '/api'  /* 'http://192.168.130.63:3000/mock/38' */
//window.applicationServerPath='/aiengine/ai-engine-server'

//window.taskgover = 'https://service.training.dataos.top/shujuzhili118/fanso-governance-task'

window.dataos_urlDaasMeta = 'http://service.testbuild.youedata.cc/daas-meta'
window.executorgover = 'http://service.testbuild.youedata.cc/datagover-57/executorgove'