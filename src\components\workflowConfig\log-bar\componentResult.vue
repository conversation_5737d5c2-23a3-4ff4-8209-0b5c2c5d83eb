<!-- 组件结果dialog -->
<template>
    <span>
        <span class="opt-text" @click='getResult'>组件结果</span>
        <el-dialog :title='`${row.moduleName}组件结果`' :visible.sync='dialogVisible' width='80%' destroy-on-close
            :close-on-click-modal='false' append-to-body>
            <el-table v-loading="loading" ref="componentsTableRef" :data="tableData" border size="small" height="90%"
                style="width: 100%">
                <el-table-column min-width="169px" v-for="(item, index) in colums" :prop="item" :label="item"
                    :key="index" show-overflow-tooltip>
                </el-table-column>
            </el-table>
            <el-pagination v-if="pageInfo.total > 0" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" :current-page="pageInfo.curPage" :page-sizes="[10, 20, 50, 100]"
                :page-size="pageInfo.size" layout="total, sizes, prev, pager, next, jumper" :total="pageInfo.total">
            </el-pagination>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="close">关 闭</el-button>
            </span>
        </el-dialog>
    </span>
</template>
<script>
import { getTaskIdApi, getCompnentsResult, getCompnentsTaskResult } from '@/api/log.js';
export default {
    props: {
        row: {
            required: true,
            type: Object,
        },
    },
    created() {
        getTaskIdApi(window.GetQueryValue('workflowId')).then(res => {
            if (res.code == 200) {
                this.taskId = res.result
            }
        })
    },
    data() {
        return {
            dialogVisible: false,
            taskId: '',
            tableData: [],
            colums: [],
            pageInfo: {
                page: 1,
                size: 10,
                curPage: 1,
                total: 0
            },
            loading: false
        };
    },
    computed: {

    },
    components: {},
    methods: {
        close() {
            this.pageInfo = {
                page: 1,
                size: 10,
                curPage: 1,
                total: 0
            }
            this.dialogVisible = false;
        },
        getResult() {
            this.loading = true
            let params = {
                taskId: this.taskId,
                moduleId: this.row.moduleId,
                page: this.pageInfo.curPage,
                size: this.pageInfo.size
            }
            console.log(this.row)
            if (this.row.moduleCode == "storageDataBySink") {
             
                getCompnentsTaskResult(params).then(res => {
                    if (res.code == 200) {
                        this.colums = []
                        if (res.result && res.result.rowList && res.result.rowList.length) {
                            for (let item of res.result.rowList) {
                                this.colums.push(item.field)
                            }
                        }
                        if (res.result && res.result.list && res.result.list.length) {
                            this.pageInfo.total = res.result.total
                            this.tableData = res.result.list

                        }
                        this.loading = false
                        this.dialogVisible = true
                    } else {
                        this.loading = false
                    }
                })
            } else {
                getCompnentsResult(params).then(res => {
                    if (res.code == 200) {
                        this.colums = []
                        if (res.result && res.result.list && res.result.list.length) {
                            this.pageInfo.total = res.result.total
                            for (let key in res.result.list[0]) {
                                this.colums.push(key)
                            }
                            this.tableData = res.result.list

                        }
                        this.loading = false
                        this.dialogVisible = true
                    } else {
                        this.loading = false
                    }
                })
            }


        },
        handleSizeChange(val) {
            this.pageInfo.size = val
            console.log(this.pageInfo.size);
            this.getResult()
        },
        handleCurrentChange(val) {
            this.pageInfo.curPage = val
            this.getResult()
            console.log(this.pageInfo.curPage);
        }
    },
};
</script>
<style lang='scss' scoped>
::v-deep .el-descriptions-item__label {
    min-width: 60px;
}

.log-details {
    max-height: 350px;
    overflow: auto;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e9e9eb;
    white-space: pre;
    width: calc(53vw - 138px);
}

::v-deep .el-descriptions__body {
    padding: 12px;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    padding-bottom: 24px !important;
}
</style>