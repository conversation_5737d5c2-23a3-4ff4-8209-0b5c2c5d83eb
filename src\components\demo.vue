<template>
  <div>
    <section id="container"></section>
  </div>
</template>

<script>
// const data = {
//   // 节点
//   nodes: [
//     {
//       shape: 'polygon',
//       id: 'node1', // String，可选，节点的唯一标识
//       x: 440,       // Number，必选，节点位置的 x 值
//       y: 40,       // Number，必选，节点位置的 y 值
//       width: 60,   // Number，可选，节点大小的 width 值
//       height: 60,  // Number，可选，节点大小的 height 值
//       label: '字段拆分', // String，节点标签
//       attrs: {
//         // group1: {
//         //   fill: '#2ECC71',
//         // },
//         body: {
//           width: 60,
//             height: 60,
//            stroke: '#dadada',
//            //fill: '#efdbff',
//            refPoints: "24.8,22 37.3,29.2 37.3,43.7 24.8,50.9 12.3,43.7 12.3,29.2",
//           // strokeWidth: 1,
//         },
//         label: {
//           fill: '#666',
//           fontSize: 12,
//           // fontWeight: 'bold',
//           // fontVariant: 'small-caps',
//         },
//         icon: {
//           width: 20,
//             height: 20,
//             x: 20,
//             y: 10,
//             href: '/icon/1.svg',
//             magnet: true,
//             hide: true
//         }
//       },
//       markup: [
//         {
//           // groupSelector: 'group1',
//           tagName: 'polygon',
//           selector: 'body',
//           // attrs: {
//           //   width: 60,
//           //   height: 60,
//           //   x: 10,
//           //   y: 10,
//           //   fill: '#fff',
//           //   stroke: '#c00',
//           //   strokeWidth: 1,
//           // },
//           className: 'aaa'
//         }, 
//         {
//           tagName: 'text',
//           selector: 'label',
//           attrs: {
//             y: 8
//           },
//           // style: {
//           //   textAnchor: 'end'
//           // }
          
//         },
//         {
//           tagName: 'image',
//           selector: 'icon',
//         }, 
//       ],
//     },
//     {
//       shape: 'polygon',
//       points: "24.8,22 37.3,29.2 37.3,43.7 24.8,50.9 12.3,43.7 12.3,29.2",
//       id: 'node2', // String，节点的唯一标识
//       x: 360,      // Number，必选，节点位置的 x 值
//       y: 180,      // Number，必选，节点位置的 y 值
//       width: 60,   // Number，可选，节点大小的 width 值
//       height: 60,  // Number，可选，节点大小的 height 值
//       label: 'world', // String，节点标签
//     },
//   ],
//   // 边
//   edges: [
//     {
//       source: 'node1', // String，必须，起始节点 id
//       target: 'node2', // String，必须，目标节点 id
//     },
//   ],
// };
import { Graph, Shape } from '@antv/x6';
import { getWorkflow } from '@/api/https.js'

export default{
  name: 'demo',
  data() {
    return {
      graph: null, // 画布实例

      ModList: [], // 组件信息
      lineList: [], // 连线信息
      notInitModList: [], // 未配置的组件id列表
      workflowid: "", // 流程id
    }
  },
  mounted() {
    this.initCanvas()
    this.getWorkflow()
  },
  methods: {

    // 初始化画布
    initCanvas() {
      this.graph = new Graph({
        container: document.getElementById('container'),
        width: window.innerWidth,
        height: window.innerHeight - 74,
        grid: {
          size: 10,      // 网格大小 10px
          visible: true, // 渲染网格背景
        },
        panning: { // 画布平移
          enabled: true,
          modifiers: 'shift',
        },
        connecting: {
          allowPort: true, //是否允许边链接到链接桩
          allowEdge: false, //是否允许边链接到另一个边
          allowNode: false, //是否允许边链接到节点（非节点上的链接桩)
          allowLoop: false, //是否允许创建循环连线，即边的起始节点和终止节点为同一节点
          allowMulti: false, //是否允许在相同的起始节点和终止之间创建多条边
          allowBlank: false, //是否允许连接到画布空白位置的点
          highlight: true, //拖动边时，是否高亮显示所有可用的连接桩或节点
          validateConnection({targetMagnet }) {
            if (targetMagnet.getAttribute('port-group') !== 'in') {
              return false
            }

            return true
          },
          validateEdge({ edge, type, previous }) {
            console.info(edge, type, previous)
            edge.attr('line/strokeDasharray', 0)
            return true
          },
          // 自动吸附
          snap: {
            radius: 20
          },
          createEdge() {
            return new Shape.Edge({
              // connector: { name: 'smooth' },
              attrs: {
                line: {
                  stroke: '#33aa98',
                  strokeWidth: 2,
                  strokeDasharray: 5,
                  targetMarker: {
                    name: 'classic',
                    size: 10,
                  },
                },
              },
            })
          },
        },
      });
      this.graph.on('node:click', ()=> {
        // node.attr('icon/href', '/icon/2.svg')

        // node.attr('icon/hide', false)
      })
      this.graph.on('cell:contextmenu', ()=> {
        // console.log(x,y,e, 'contextmenu')
        // const p = this.graph.clientToGraph(e.clientX, e.clientY)
        // console.log(p, 'p')
        // node.attr('icon/href', '/icon/2.svg')
      })

      this.graph.on('node:mouseenter', ({node})=> {
        node.attr('port/hide', false)
      })
      this.graph.on('node:mouseleave', ({node})=> {
        node.attr('port/hide', true)
      })
      
      
      
    },

    //查询流程配置
    async getWorkflow() {
      const res = await getWorkflow({workflowId: '09c3831852194974a72db1b81ba04154'})
      if (res.code == 200) {
        const {ModList, lineList, notInitModList, workflowid} = res.result
        this.ModList = ModList
        this.lineList = lineList
        this.notInitModList = notInitModList
        this.workflowid = workflowid

        this.renderNode()
      }
    },

    //渲染数据
    renderNode() {
      let nodes = []
      this.ModList.forEach((item) => {
        const node = {
          shape: 'rect',
          id: item.id, // String，可选，节点的唯一标识
          x: item.x,       // Number，必选，节点位置的 x 值
          y: item.y,       // Number，必选，节点位置的 y 值
          width: 125,   // Number，可选，节点大小的 width 值
          height: 40,  // Number，可选，节点大小的 height 值
          label: item.name, // String，节点标签
          markup: [
            {
              tagName: 'rect',
              selector: 'body'
            },
            {
              tagName: 'text',
              selector: 'label',
            },
            // {
            //   tagName: 'polygon',
            //   selector: 'polygon',
            // },
            {
              tagName: 'image',
              selector: 'icon',
            },
            {
              tagName: 'image',
              selector: 'port',
            }, 
          ],
          attrs: {
            body: {
              stroke: '#dadada', // 边框颜色
              strokeWidth: 1,
              fill: '#fff',   // 填充颜色
              
            },
            label: {
              // y: 40,
              fill: '#666',
              fontSize: 13,
              // fontWeight: 'bold',
            },
            // polygon: {
            //   stroke: '#dadada',
            //   fill: '#fff',
            //   refPoints: "24.8,22 37.3,29.2 37.3,43.7 24.8,50.9 12.3,43.7 12.3,29.2",
            //   strokeWidth: 1,
            // },
            icon: {
              width: 25,
              height: 25,
              x: 5,
              y: 7,
              href: `/icon/${item.startFlag || item.endFlag ? 1 : 2}.svg`
            },
            port: {
              width: 20,
              height: 20,
              x: 42,
              y: 10,
              href: `/icon/port.svg`,
              hide: true
            },
          }
        }
        nodes.push(node)
      })

      let edges = []
      this.lineList.forEach((item) => {
        const edge = {
          source: item.sourceNodeId,
          target: item.targetNodeId
        }
        edges.push(edge)
      })
      this.graph.fromJSON({
        nodes,
        edges
      })
    }

  },
}
</script>

<style>
.x6-node [hide='true'] {
  display: none;
}
</style>