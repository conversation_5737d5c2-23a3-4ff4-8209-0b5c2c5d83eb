import feach, {download} from '../libs/feach'

const baseUrl = window.applicationServerPath
const dataOsUrl = window.dataos_urlDaasMeta
const jobUrl = window.executorgover



// 流程完整性校验
export function integrityValidate(workflowId) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${workflowId}/integrity_validate`,
    method: 'get',
    // params
  })
}

// 获取流程运行状态
export function getStatus(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/state`,
    method: 'get',
    // params
  })
}

// 导出任务结果
export function getResult(taskId) {
  return download(baseUrl + `/api/v2/tasks/export/${taskId}/result`,{},'流程结果.xlsx')
}

// 获取流程信息
export function getWorkflowInfo(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.workflowId}/basic`,
    method: 'get',
    // params
  })
}


// 保存流程/保存草稿
export function saveWorkflow(workflowId) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${workflowId}/save_to_draft`,
    method: 'post'
  })
}

// 执行流程
export function executeWorkflow(data) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${data.workflowId}/execute`,
    method: 'post',
    // data
  })
}

// 关闭
export function flinkStop(data) {
  return feach({
    url: baseUrl + `/api/v2/tasks/${data.workflowId}/stop`,
    method: 'post',
    // data
  })
}

// 执行调度
export function scheduleWorkflow(data) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${data.workflowId}/schedules/trigger`,
    method: 'post',
    // data
  })
}

// 停止调度 
export function freezeWorkflow(data) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${data.workflowId}/schedules/${data.scheduleId}/stop`,
    method: 'post',
    // data
  })
}


// 获取组件配置信息 
export function queryPreviousAndSelfParam(params) {
  return feach({
    url: baseUrl + '/api/v2/modules/function_param',
    method: 'get',
    params
  })
}

//
export function updateFunctionParam(data, moduleId) {
  return feach({
    url: baseUrl + `/api/v2/modules/${moduleId}/function_param/update`,
    method: 'post',
    data: data
  })
}

export function updateFunctionParamClear(moduleId) {
  return feach({
    url: baseUrl + `/api/v2/modules/${moduleId}/function_param/update`,
    method: 'post'
  })
}

// 组件信息
export function getApplication(params) {
  return feach({
    // url: 'http://testbuild.youedata.cc/shujuzhili/fanso-governance-task' + '/api/v2/application/queryApplicationListAllInfo',
    url: baseUrl + `/api/v2/modules/group_by_category`,
    method: 'get',
    params,
  })
}

// 查询流程配置
export function getWorkflow(params) {
  return feach({
    url: baseUrl + `/api/v2/workflows/${params.id}/draw_configs`,
    method: 'get',
    params
  })
}

// 删除连线信息
export function deleteConnInfo(data) {
  return feach({
    url: baseUrl + '/api/v2/line/delete',
    method: 'post',
    data
  })
}

// 删除工作流中的组件
export function deleteNode(params) {
  return feach({
    url: baseUrl + `/api/v2/modules/${params.moduleId}/delete`,
    method: 'post',
    // params
  })
}

// 更新组件自定义名称
export function updateName(data, moduleId) {
  return feach({
    url: baseUrl + `/api/v2/modules/${moduleId}/update`,
    method: 'post',
    data
  })
}

// 拉取组件(新增)
export function nodeAdd(data) {
  return feach({
    url: baseUrl + '/api/v2/modules',
    method: 'post',
    data
  })
}

// 新增连线信息
export function edgeAdd(data) {
  return feach({
    url: baseUrl + '/api/v2/line',
    method: 'post',
    data
  })
}

// 更新组件位置信息
export function updatePosition(data) {
  return feach({
    url: baseUrl + '/api/v2/modules/position/update',
    method: 'post',
    data
  })
}

// 更新组件位置信息
export function nodeCopy(params, data) {
  return feach({
    url: baseUrl + `/api/v2/modules/${params.id}/copy`,
    method: 'post',
    // params,
    data
  })
}

// 更新组件位置信息
export function workflowAdd(data) {
  return feach({
    url: baseUrl + '/api/v2/workflows',
    method: 'post',
    data
  })
}
















// 任务资源配置-新增/修改
export function updateTaskRe(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/updateTaskRe',
    method: 'post',
    data: params
  })
}

// 任务资源配置-保存/保存为草稿
export function saveTaskData(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/updateTaskRe9',
    method: 'post',
    data: params
  })
}

// 获取数据资源树第一层
export function getResourceTree() {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/mainMenu',
    method: 'get'
  })
}

// 获取数据中心应用子菜单
export function getAppSubMenu(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/appMenu',
    method: 'get',
    params
  })
}

// 获取数据中心应用子菜单
export function getResourceTreeDb(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/dbMenu',
    method: 'get',
    params
  })
}

// 获取数据中心文件子菜单
export function getResourceTreeFile(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/fileMenu',
    method: 'get',
    params
  })
}
// 获取数据中心数据源列表
export function getResourceSourceList(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/sourceList',
    method: 'get',
    params
  })
}

// 获取数据中心应用列表
export function getResourceAppList(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/appList',
    method: 'get',
    params
  })
}

// 获取数据中心数据库列表
export function getResourceBaseList(params) {
  return feach({
    // /api/v2/daas/meta/dataCenter/baseList
    url: dataOsUrl + '/api/v2/daas/meta/dataCenterApi/baseList',
    method: 'get',
    params
  })
}

// 获取数据中心数据库表列表
export function getResourceTableList(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/tableList',
    method: 'get',
    params
  })
}

// 获取数据中心文件列表
export function getResourceFileList(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/fileList',
    method: 'get',
    params
  })
}

// 获取表结构
export function getListMetaData(params) {
  return feach({
    url: dataOsUrl + '/api/v2/daas/meta/listMetaData',
    method: 'get',
    params
  })
}

export function storage(data) {
  return feach({
    url: baseUrl + '/api/v2/storage/storage',
    method: 'post',
    data
  })
}
// 任务资源配置-组件-新增/修改
export function reqUpdateTask(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/updateTaskReProcDef',
    method: 'POST',
    data: params
  })
}

// 任务资源配置-删除功能组件
export function reqDeleteTask(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/deleteTaskReProcDef',
    method: 'POST',
    data: params
  })
}

// 任务资源配置-更新连接信息
export function updataConnlist(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/updateConnlist',
    method: 'POST',
    data: params
  })
}

// 任务资源配置-数据初始化
export function initTaskReProcDef(params) {
  return feach({
    url: baseUrl + '/api/v2/taskre/initTaskReProcDef',
    method: 'POST',
    data: params
  })
}

// POST /api/v2/taskrunlog/findLog  控制台
export function findLog(data) {
  return feach({
    url: baseUrl + '/api/v2/taskruninglog/findLog',
    method: 'POST',
    data: data
  })
}

// POST /api/v2/taskrunlog/findLogInfo
export function findTaskExecInfo(params) {
  return feach({
    url: baseUrl + '/api/v2/taskruninglog/findTaskExecInfo',
    method: 'POST',
    params
  })
}

// 通过任务名称查询是否有该任务
export function getTaskByIdInfo(params) {
  return feach({
    url: baseUrl + '/api/v2/task/getTaskById',
    method: 'get',
    params
  })
}

// 指数立即运行的状态
export function runImmediatelyStatus(params) {
  return feach({
    url: jobUrl + '/api/v2/runImmediately/runImmediately',
    method: 'get',
    params
  })
}

// 获取各节点运行状态
export function getNodeRuningStatus(params) {
  return feach({
    url: baseUrl + '/api/v2/taskruninglog/infos',
    method: 'get',
    params
  })
}

// 获取字段类型转换规则
export function getDictionary(params) {
  return feach({
    url: `${baseUrl}/api/v2/application/dictionary`,
    method: 'get',
    params
  })
}

// 任务资源配置-获取未保存的组件状态
export function getState(params) {
  return feach({
    url: `${baseUrl}/api/v2/taskre/getState`,
    method: 'get',
    params
  })
}

// export function reqFindLog(params) {
//     return feach({
//         url: baseUrl + '/taskrunlog/findLog',
//         method: 'POST',
//         data: params
//     })
// }

// // POST /api/v2/taskrunlog/findLogInfo
// export function reqFindLogInfo(params) {
//     return feach({
//         url: baseUrl + '/taskrunlog/findLogInfo',
//         method: 'POST',
//         data: params
//     })
// }
