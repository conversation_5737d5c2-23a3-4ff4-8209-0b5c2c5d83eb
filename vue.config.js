const path = require('path')

module.exports = {
  publicPath: './',
  runtimeCompiler: true,
  productionSourceMap: true,
  devServer: {
    proxy: {
      '/api': {
        // target: 'http://192.168.1.128:8000',// 要跨域的域名
        target: 'http://172.16.32.12:30383/aiengine/application-server',// 要跨域的域名
        changeOrigin: true, // 是否开启跨域
        pathRewrite: {   //重写路径
          '^/api': ''
        }
      }
    }
  },
  chainWebpack: config => {
    config.module
      .rule('svg')
      .exclude.add(path.join(__dirname, 'src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(path.join(__dirname, 'src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  }
}