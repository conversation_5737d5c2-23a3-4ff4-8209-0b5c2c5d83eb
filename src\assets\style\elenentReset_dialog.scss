// 全局弹框样式

.el-dialog {
  min-width: 460px;
  border-radius: 4px !important;
  box-shadow: 0 17px 40px 0 rgba(36, 36, 36, 0.21) !important;
  font-size: 14px;
  overflow: hidden;
  background-color: #F5F7FA !important;

  // 头部
  .el-dialog__header {
    padding: 12px 20px;
    background: #276EB7;
    position: relative;

    // 头部文字
    .el-dialog__title, .header-title {
      color: #fff;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
    }

    // 头部关闭按钮
    .el-dialog__headerbtn {
      top: 50%;
      transform: translateY(-50%);

      .el-dialog__close {
        color: #fff;
      }
    }
  }

  // body主体
  .el-dialog__body {
    padding: 0px;
    margin: 16px;
    background-color: #fff;
    .el-form-item:last-child {
      margin-bottom: 0 !important;
    }
  }

  // 底部
  .el-dialog__footer {
    height: 80px;
    background-color: #fff;
    padding: 20px 0;
    text-align: center;

    .el-button--info,
    .el-button--primary {
      width: 96px;
      height: 40px;
      font-size: 14px;
      font-weight: 400;
      color: #606266;
      transition: all .3s ease-in-out;
      ;
    }
    .el-button--info {
      border-color: #DCDFE6;
      background-color: #fff;
    }
    .el-button--primary {
      border-color: #276EB7;
      background-color: #276EB7;
      color: #fff;
    }
  }
}

// .task-config {
//   margin-top: 28px !important;
//   width: calc(100% - 264px) !important;
//   margin: 0 auto 0 112px;
//   .el-dialog__header {
//     background-color: #276EB7;
//     padding: 13px 20px;
//     .header-title {
//       font-size: 16px;
//       color: #ffffff;
//       font-weight: 400;
//     }
//   }
// }

// .header-title {
//   font-size: 16px;
//   color: #ffffff
// }

.el-dialog.task-config {
  margin-top: 28px !important;
  width: calc(100% - 264px) !important;
  margin: 0 auto 0 112px;
  .el-dialog__body {
    height: calc( 100vh - 216px);
    .el-form-item:last-child {
      margin-bottom: 0 !important;
    }
    #frame-layout {
      height: 100%;
    }
  }

  .el-dialog__footer {

    .dialog-footer {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .checkButton {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 12px;
      .text {
        height: 40px;
        font-size: 14px;
        font-weight: 400;
        color: #909399;
        line-height: 40px;
      }
      .icon {
        width: 20px;
        height: 20px;
      }
    }
  }

  //.componentId {
  //  height: 340px;
  //  overflow-y: auto;
  //}
  //
  //// 滚动条
  //.componentId::-webkit-scrollbar-thumb {
  //  border-radius: 3px;
  //  background: #CFD3DB;
  //}
  //
  //// 滚动条里面轨道
  //.componentId::-webkit-scrollbar-track {
  //  border-radius: 3px;
  //  background: #EBEDF3;
  //}
  //
  //.componentId::-webkit-scrollbar {
  //  width: 8px;
  //  height: 1px;
  //}
}