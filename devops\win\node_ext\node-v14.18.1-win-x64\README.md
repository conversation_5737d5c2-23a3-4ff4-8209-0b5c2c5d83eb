<!--lint disable no-literal-urls-->
<p align="center">
  <a href="https://nodejs.org/">
    <img
      alt="Node.js"
      src="https://nodejs.org/static/images/logo-light.svg"
      width="400"
    />
  </a>
</p>

Node.js is an open-source, cross-platform, JavaScript runtime environment. It
executes JavaScript code outside of a browser. For more information on using
Node.js, see the [Node.js Website][].

The Node.js project uses an [open governance model](./GOVERNANCE.md). The
[OpenJS Foundation][] provides support for the project.

**This project is bound by a [Code of Conduct][].**

# Table of contents

* [Support](#support)
* [Release types](#release-types)
  * [Download](#download)
    * [Current and LTS releases](#current-and-lts-releases)
    * [Nightly releases](#nightly-releases)
    * [API documentation](#api-documentation)
  * [Verifying binaries](#verifying-binaries)
* [Building Node.js](#building-nodejs)
* [Security](#security)
* [Contributing to Node.js](#contributing-to-nodejs)
* [Current project team members](#current-project-team-members)
  * [TSC (Technical Steering Committee)](#tsc-technical-steering-committee)
  * [Collaborators](#collaborators)
  * [Release keys](#release-keys)
* [License](#license)

## Support

Looking for help? Check out the
[instructions for getting support](.github/SUPPORT.md).

## Release types

* **Current**: Under active development. Code for the Current release is in the
  branch for its major version number (for example,
  [v10.x](https://github.com/nodejs/node/tree/v10.x)). Node.js releases a new
  major version every 6 months, allowing for breaking changes. This happens in
  April and October every year. Releases appearing each October have a support
  life of 8 months. Releases appearing each April convert to LTS (see below)
  each October.
* **LTS**: Releases that receive Long Term Support, with a focus on stability
  and security. Every even-numbered major version will become an LTS release.
  LTS releases receive 12 months of _Active LTS_ support and a further 18 months
  of _Maintenance_. LTS release lines have alphabetically-ordered code names,
  beginning with v4 Argon. There are no breaking changes or feature additions,
  except in some special circumstances.
* **Nightly**: Code from the Current branch built every 24-hours when there are
  changes. Use with caution.

Current and LTS releases follow [Semantic Versioning](https://semver.org). A
member of the Release Team [signs](#release-keys) each Current and LTS release.
For more information, see the
[Release README](https://github.com/nodejs/Release#readme).

### Download

Binaries, installers, and source tarballs are available at
<https://nodejs.org/en/download/>.

#### Current and LTS releases
<https://nodejs.org/download/release/>

The [latest](https://nodejs.org/download/release/latest/) directory is an
alias for the latest Current release. The latest-_codename_ directory is an
alias for the latest release from an LTS line. For example, the
[latest-carbon](https://nodejs.org/download/release/latest-carbon/) directory
contains the latest Carbon (Node.js 8) release.

#### Nightly releases
<https://nodejs.org/download/nightly/>

Each directory name and filename contains a date (in UTC) and the commit
SHA at the HEAD of the release.

#### API documentation

Documentation for the latest Current release is at <https://nodejs.org/api/>.
Version-specific documentation is available in each release directory in the
_docs_ subdirectory. Version-specific documentation is also at
<https://nodejs.org/download/docs/>.

### Verifying binaries

Download directories contain a `SHASUMS256.txt` file with SHA checksums for the
files.

To download `SHASUMS256.txt` using `curl`:

```console
$ curl -O https://nodejs.org/dist/vx.y.z/SHASUMS256.txt
```

To check that a downloaded file matches the checksum, run
it through `sha256sum` with a command such as:

```console
$ grep node-vx.y.z.tar.gz SHASUMS256.txt | sha256sum -c -
```

For Current and LTS, the GPG detached signature of `SHASUMS256.txt` is in
`SHASUMS256.txt.sig`. You can use it with `gpg` to verify the integrity of
`SHASUMS256.txt`. You will first need to import
[the GPG keys of individuals authorized to create releases](#release-keys). To
import the keys:

```console
$ gpg --keyserver pool.sks-keyservers.net --recv-keys DD8F2338BAE7501E3DD5AC78C273792F7D83545D
```

See the bottom of this README for a full script to import active release keys.

Next, download the `SHASUMS256.txt.sig` for the release:

```console
$ curl -O https://nodejs.org/dist/vx.y.z/SHASUMS256.txt.sig
```

Then use `gpg --verify SHASUMS256.txt.sig SHASUMS256.txt` to verify
the file's signature.

## Building Node.js

See [BUILDING.md](BUILDING.md) for instructions on how to build Node.js from
source and a list of supported platforms.

## Security

For information on reporting security vulnerabilities in Node.js, see
[SECURITY.md](./SECURITY.md).

## Contributing to Node.js

* [Contributing to the project][]
* [Working Groups][]
* [Strategic initiatives][]
* [Technical values and prioritization][]

## Current project team members

For information about the governance of the Node.js project, see
[GOVERNANCE.md](./GOVERNANCE.md).

<!-- node-core-utils depends on the format of the TSC list. If the
     format changes, those utilities need to be tested and updated. -->
### TSC (Technical Steering Committee)

<!--lint disable prohibited-strings-->
* [aduh95](https://github.com/aduh95) -
**Antoine du Hamel** &lt;<EMAIL>&gt; (he/him)
* [apapirovski](https://github.com/apapirovski) -
**Anatoli Papirovski** &lt;<EMAIL>&gt; (he/him)
* [BethGriggs](https://github.com/BethGriggs) -
**Beth Griggs** &lt;<EMAIL>&gt; (she/her)
* [BridgeAR](https://github.com/BridgeAR) -
**Ruben Bridgewater** &lt;<EMAIL>&gt; (he/him)
* [ChALkeR](https://github.com/ChALkeR) -
**Сковорода Никита Андреевич** &lt;<EMAIL>&gt; (he/him)
* [cjihrig](https://github.com/cjihrig) -
**Colin Ihrig** &lt;<EMAIL>&gt; (he/him)
* [codebytere](https://github.com/codebytere) -
**Shelley Vohr** &lt;<EMAIL>&gt; (she/her)
* [danbev](https://github.com/danbev) -
**Daniel Bevenius** &lt;<EMAIL>&gt; (he/him)
* [danielleadams](https://github.com/danielleadams) -
**Danielle Adams** &lt;<EMAIL>&gt; (she/her)
* [fhinkel](https://github.com/fhinkel) -
**Franziska Hinkelmann** &lt;<EMAIL>&gt; (she/her)
* [gabrielschulhof](https://github.com/gabrielschulhof) -
**Gabriel Schulhof** &lt;<EMAIL>&gt;
* [gireeshpunathil](https://github.com/gireeshpunathil) -
**Gireesh Punathil** &lt;<EMAIL>&gt; (he/him)
* [jasnell](https://github.com/jasnell) -
**James M Snell** &lt;<EMAIL>&gt; (he/him)
* [joyeecheung](https://github.com/joyeecheung) -
**Joyee Cheung** &lt;<EMAIL>&gt; (she/her)
* [mcollina](https://github.com/mcollina) -
**Matteo Collina** &lt;<EMAIL>&gt; (he/him)
* [mhdawson](https://github.com/mhdawson) -
**Michael Dawson** &lt;<EMAIL>&gt; (he/him)
* [mmarchini](https://github.com/mmarchini) -
**Mary Marchini** &lt;<EMAIL>&gt; (she/her)
* [MylesBorins](https://github.com/MylesBorins) -
**Myles Borins** &lt;<EMAIL>&gt; (he/him)
* [ronag](https://github.com/ronag) -
**Robert Nagy** &lt;<EMAIL>&gt;
* [targos](https://github.com/targos) -
**Michaël Zasso** &lt;<EMAIL>&gt; (he/him)
* [tniessen](https://github.com/tniessen) -
**Tobias Nießen** &lt;<EMAIL>&gt;
* [Trott](https://github.com/Trott) -
**Rich Trott** &lt;<EMAIL>&gt; (he/him)

<details>

<summary>Emeriti</summary>

### TSC emeriti

* [addaleax](https://github.com/addaleax) -
**Anna Henningsen** &lt;<EMAIL>&gt; (she/her)
* [bnoordhuis](https://github.com/bnoordhuis) -
**Ben Noordhuis** &lt;<EMAIL>&gt;
* [chrisdickinson](https://github.com/chrisdickinson) -
**Chris Dickinson** &lt;<EMAIL>&gt;
* [evanlucas](https://github.com/evanlucas) -
**Evan Lucas** &lt;<EMAIL>&gt; (he/him)
* [Fishrock123](https://github.com/Fishrock123) -
**Jeremiah Senkpiel** &lt;<EMAIL>&gt; (he/they)
* [gibfahn](https://github.com/gibfahn) -
**Gibson Fahnestock** &lt;<EMAIL>&gt; (he/him)
* [indutny](https://github.com/indutny) -
**Fedor Indutny** &lt;<EMAIL>&gt;
* [isaacs](https://github.com/isaacs) -
**Isaac Z. Schlueter** &lt;<EMAIL>&gt;
* [joshgav](https://github.com/joshgav) -
**Josh Gavant** &lt;<EMAIL>&gt;
* [mscdex](https://github.com/mscdex) -
**Brian White** &lt;<EMAIL>&gt;
* [nebrius](https://github.com/nebrius) -
**Bryan Hughes** &lt;<EMAIL>&gt;
* [ofrobots](https://github.com/ofrobots) -
**Ali Ijaz Sheikh** &lt;<EMAIL>&gt; (he/him)
* [orangemocha](https://github.com/orangemocha) -
**Alexis Campailla** &lt;<EMAIL>&gt;
* [piscisaureus](https://github.com/piscisaureus) -
**Bert Belder** &lt;<EMAIL>&gt;
* [rvagg](https://github.com/rvagg) -
**Rod Vagg** &lt;<EMAIL>&gt;
* [sam-github](https://github.com/sam-github) -
**Sam Roberts** &lt;<EMAIL>&gt;
* [shigeki](https://github.com/shigeki) -
**Shigeki Ohtsu** &lt;<EMAIL>&gt; (he/him)
* [thefourtheye](https://github.com/thefourtheye) -
**Sakthipriyan Vairamani** &lt;<EMAIL>&gt; (he/him)
* [TimothyGu](https://github.com/TimothyGu) -
**Tiancheng "Timothy" Gu** &lt;<EMAIL>&gt; (he/him)
* [trevnorris](https://github.com/trevnorris) -
**Trevor Norris** &lt;<EMAIL>&gt;

</details>

<!-- node-core-utils and find-inactive-collaborators.mjs depend on the format
     of the collaborator list. If the format changes, those utilities need to be
     tested and updated. -->
### Collaborators

* [addaleax](https://github.com/addaleax) -
**Anna Henningsen** &lt;<EMAIL>&gt; (she/her)
* [aduh95](https://github.com/aduh95) -
**Antoine du Hamel** &lt;<EMAIL>&gt; (he/him)
* [ak239](https://github.com/ak239) -
**Aleksei Koziatinskii** &lt;<EMAIL>&gt;
* [antsmartian](https://github.com/antsmartian) -
**Anto Aravinth** &lt;<EMAIL>&gt; (he/him)
* [apapirovski](https://github.com/apapirovski) -
**Anatoli Papirovski** &lt;<EMAIL>&gt; (he/him)
* [AshCripps](https://github.com/AshCripps) -
**Ash Cripps** &lt;<EMAIL>&gt;
* [bcoe](https://github.com/bcoe) -
**Ben Coe** &lt;<EMAIL>&gt; (he/him)
* [bengl](https://github.com/bengl) -
**Bryan English** &lt;<EMAIL>&gt; (he/him)
* [benjamingr](https://github.com/benjamingr) -
**Benjamin Gruenbaum** &lt;<EMAIL>&gt;
* [BethGriggs](https://github.com/BethGriggs) -
**Beth Griggs** &lt;<EMAIL>&gt; (she/her)
* [bmeck](https://github.com/bmeck) -
**Bradley Farias** &lt;<EMAIL>&gt;
* [bmeurer](https://github.com/bmeurer) -
**Benedikt Meurer** &lt;<EMAIL>&gt;
* [boneskull](https://github.com/boneskull) -
**Christopher Hiller** &lt;<EMAIL>&gt; (he/him)
* [BridgeAR](https://github.com/BridgeAR) -
**Ruben Bridgewater** &lt;<EMAIL>&gt; (he/him)
* [bzoz](https://github.com/bzoz) -
**Bartosz Sosnowski** &lt;<EMAIL>&gt;
* [cclauss](https://github.com/cclauss) -
**Christian Clauss** &lt;<EMAIL>&gt; (he/him)
* [ChALkeR](https://github.com/ChALkeR) -
**Сковорода Никита Андреевич** &lt;<EMAIL>&gt; (he/him)
* [cjihrig](https://github.com/cjihrig) -
**Colin Ihrig** &lt;<EMAIL>&gt; (he/him)
* [codebytere](https://github.com/codebytere) -
**Shelley Vohr** &lt;<EMAIL>&gt; (she/her)
* [danbev](https://github.com/danbev) -
**Daniel Bevenius** &lt;<EMAIL>&gt; (he/him)
* [danielleadams](https://github.com/danielleadams) -
**Danielle Adams** &lt;<EMAIL>&gt; (she/her)
* [davisjam](https://github.com/davisjam) -
**Jamie Davis** &lt;<EMAIL>&gt; (he/him)
* [DerekNonGeneric](https://github.com/DerekNonGeneric) -
**Derek Lewis** &lt;<EMAIL>&gt; (he/him)
* [devnexen](https://github.com/devnexen) -
**David Carlier** &lt;<EMAIL>&gt;
* [devsnek](https://github.com/devsnek) -
**Gus Caplan** &lt;<EMAIL>&gt; (they/them)
* [dmabupt](https://github.com/dmabupt) -
**Xu Meng** &lt;<EMAIL>&gt; (he/him)
* [dnlup](https://github.com/dnlup)
**Daniele Belardi** &lt;<EMAIL>&gt; (he/him)
* [edsadr](https://github.com/edsadr) -
**Adrian Estrada** &lt;<EMAIL>&gt; (he/him)
* [eugeneo](https://github.com/eugeneo) -
**Eugene Ostroukhov** &lt;<EMAIL>&gt;
* [evanlucas](https://github.com/evanlucas) -
**Evan Lucas** &lt;<EMAIL>&gt; (he/him)
* [fhinkel](https://github.com/fhinkel) -
**Franziska Hinkelmann** &lt;<EMAIL>&gt; (she/her)
* [Fishrock123](https://github.com/Fishrock123) -
**Jeremiah Senkpiel** &lt;<EMAIL>&gt;  (he/they)
* [Flarna](https://github.com/Flarna) -
**Gerhard Stöbich** &lt;<EMAIL>&gt;  (he/they)
* [gabrielschulhof](https://github.com/gabrielschulhof) -
**Gabriel Schulhof** &lt;<EMAIL>&gt;
* [geek](https://github.com/geek) -
**Wyatt Preul** &lt;<EMAIL>&gt;
* [gengjiawen](https://github.com/gengjiawen) -
**Jiawen Geng** &lt;<EMAIL>&gt;
* [GeoffreyBooth](https://github.com/geoffreybooth) -
**Geoffrey Booth** &lt;<EMAIL>&gt; (he/him)
* [gireeshpunathil](https://github.com/gireeshpunathil) -
**Gireesh Punathil** &lt;<EMAIL>&gt; (he/him)
* [guybedford](https://github.com/guybedford) -
**Guy Bedford** &lt;<EMAIL>&gt; (he/him)
* [HarshithaKP](https://github.com/HarshithaKP) -
**Harshitha K P** &lt;<EMAIL>&gt; (she/her)
* [hashseed](https://github.com/hashseed) -
**Yang Guo** &lt;<EMAIL>&gt; (he/him)
* [himself65](https://github.com/himself65) -
**Zeyu Yang** &lt;<EMAIL>&gt; (he/him)
* [hiroppy](https://github.com/hiroppy) -
**Yuta Hiroto** &lt;<EMAIL>&gt; (he/him)
* [iansu](https://github.com/iansu) -
**Ian Sutherland** &lt;<EMAIL>&gt;
* [indutny](https://github.com/indutny) -
**Fedor Indutny** &lt;<EMAIL>&gt;
* [JacksonTian](https://github.com/JacksonTian) -
**Jackson Tian** &lt;<EMAIL>&gt;
* [jasnell](https://github.com/jasnell) -
**James M Snell** &lt;<EMAIL>&gt; (he/him)
* [jkrems](https://github.com/jkrems) -
**Jan Krems** &lt;<EMAIL>&gt; (he/him)
* [joaocgreis](https://github.com/joaocgreis) -
**João Reis** &lt;<EMAIL>&gt;
* [joyeecheung](https://github.com/joyeecheung) -
**Joyee Cheung** &lt;<EMAIL>&gt; (she/her)
* [juanarbol](https://github.com/juanarbol) -
**Juan José Arboleda** &lt;<EMAIL>&gt; (he/him)
* [JungMinu](https://github.com/JungMinu) -
**Minwoo Jung** &lt;<EMAIL>&gt; (he/him)
* [legendecas](https://github.com/legendecas) -
**Chengzhong Wu** &lt;<EMAIL>&gt; (he/him)
* [Leko](https://github.com/Leko) -
**Shingo Inoue** &lt;<EMAIL>&gt; (he/him)
* [linkgoron](https://github.com/linkgoron) -
**Nitzan Uziely** &lt;<EMAIL>&gt;
* [lpinca](https://github.com/lpinca) -
**Luigi Pinca** &lt;<EMAIL>&gt; (he/him)
* [lundibundi](https://github.com/lundibundi) -
**Denys Otrishko** &lt;<EMAIL>&gt; (he/him)
* [Lxxyx](https://github.com/Lxxyx) -
**Zijian Liu** &lt;<EMAIL>&gt; (he/him)
* [mafintosh](https://github.com/mafintosh) -
**Mathias Buus** &lt;<EMAIL>&gt; (he/him)
* [mcollina](https://github.com/mcollina) -
**Matteo Collina** &lt;<EMAIL>&gt; (he/him)
* [mhdawson](https://github.com/mhdawson) -
**Michael Dawson** &lt;<EMAIL>&gt; (he/him)
* [miladfarca](https://github.com/miladfarca) -
**Milad Fa** &lt;<EMAIL>&gt; (he/him)
* [mildsunrise](https://github.com/mildsunrise) -
**Alba Mendez** &lt;<EMAIL>&gt; (she/her)
* [misterdjules](https://github.com/misterdjules) -
**Julien Gilli** &lt;<EMAIL>&gt;
* [mmarchini](https://github.com/mmarchini) -
**Mary Marchini** &lt;<EMAIL>&gt; (she/her)
* [mscdex](https://github.com/mscdex) -
**Brian White** &lt;<EMAIL>&gt;
* [MylesBorins](https://github.com/MylesBorins) -
**Myles Borins** &lt;<EMAIL>&gt; (he/him)
* [oyyd](https://github.com/oyyd) -
**Ouyang Yadong** &lt;<EMAIL>&gt; (he/him)
* [panva](https://github.com/panva) -
**Filip Skokan** &lt;<EMAIL>&gt;
* [PoojaDurgad](https://github.com/PoojaDurgad) -
**Pooja D P** &lt;<EMAIL>&gt; (she/her)
* [puzpuzpuz](https://github.com/puzpuzpuz) -
**Andrey Pechkurov** &lt;<EMAIL>&gt; (he/him)
* [Qard](https://github.com/Qard) -
**Stephen Belanger** &lt;<EMAIL>&gt; (he/him)
* [RaisinTen](https://github.com/RaisinTen) -
**Darshan Sen** &lt;<EMAIL>&gt; (he/him)
* [refack](https://github.com/refack) -
**Refael Ackermann (רפאל פלחי)** &lt;<EMAIL>&gt; (he/him/הוא/אתה)
* [rexagod](https://github.com/rexagod) -
**Pranshu Srivastava** &lt;<EMAIL>&gt; (he/him)
* [richardlau](https://github.com/richardlau) -
**Richard Lau** &lt;<EMAIL>&gt;
* [rickyes](https://github.com/rickyes) -
**Ricky Zhou** &lt;<EMAIL>&gt; (he/him)
* [ronag](https://github.com/ronag) -
**Robert Nagy** &lt;<EMAIL>&gt;
* [ruyadorno](https://github.com/ruyadorno) -
**Ruy Adorno** &lt;<EMAIL>&gt; (he/him)
* [rvagg](https://github.com/rvagg) -
**Rod Vagg** &lt;<EMAIL>&gt;
* [ryzokuken](https://github.com/ryzokuken) -
**Ujjwal Sharma** &lt;<EMAIL>&gt; (he/him)
* [saghul](https://github.com/saghul) -
**Saúl Ibarra Corretgé** &lt;<EMAIL>&gt;
* [santigimeno](https://github.com/santigimeno) -
**Santiago Gimeno** &lt;<EMAIL>&gt;
* [seishun](https://github.com/seishun) -
**Nikolai Vavilov** &lt;<EMAIL>&gt;
* [shisama](https://github.com/shisama) -
**Masashi Hirano** &lt;<EMAIL>&gt; (he/him)
* [silverwind](https://github.com/silverwind) -
**Roman Reiss** &lt;<EMAIL>&gt;
* [srl295](https://github.com/srl295) -
**Steven R Loomis** &lt;<EMAIL>&gt;
* [starkwang](https://github.com/starkwang) -
**Weijia Wang** &lt;<EMAIL>&gt;
* [sxa](https://github.com/sxa) -
**Stewart X Addison** &lt;<EMAIL>&gt; (he/him)
* [targos](https://github.com/targos) -
**Michaël Zasso** &lt;<EMAIL>&gt; (he/him)
* [TimothyGu](https://github.com/TimothyGu) -
**Tiancheng "Timothy" Gu** &lt;<EMAIL>&gt; (he/him)
* [tniessen](https://github.com/tniessen) -
**Tobias Nießen** &lt;<EMAIL>&gt;
* [trivikr](https://github.com/trivikr) -
**Trivikram Kamat** &lt;<EMAIL>&gt;
* [Trott](https://github.com/Trott) -
**Rich Trott** &lt;<EMAIL>&gt; (he/him)
* [vdeturckheim](https://github.com/vdeturckheim) -
**Vladimir de Turckheim** &lt;<EMAIL>&gt; (he/him)
* [watilde](https://github.com/watilde) -
**Daijiro Wachi** &lt;<EMAIL>&gt; (he/him)
* [watson](https://github.com/watson) -
**Thomas Watson** &lt;<EMAIL>&gt;
* [XadillaX](https://github.com/XadillaX) -
**Khaidi Chu** &lt;<EMAIL>&gt; (he/him)
* [yashLadha](https://github.com/yashLadha) -
**Yash Ladha** &lt;<EMAIL>&gt; (he/him)
* [yhwang](https://github.com/yhwang) -
**Yihong Wang** &lt;<EMAIL>&gt;
* [yorkie](https://github.com/yorkie) -
**Yorkie Liu** &lt;<EMAIL>&gt;
* [yosuke-furukawa](https://github.com/yosuke-furukawa) -
**Yosuke Furukawa** &lt;<EMAIL>&gt;
* [ZYSzys](https://github.com/ZYSzys) -
**Yongsheng Zhang** &lt;<EMAIL>&gt; (he/him)

<details>

<summary>Emeriti</summary>

<!-- find-inactive-collaborators.mjs depends on the format of the emeriti list.
     If the format changes, those utilities need to be tested and updated. -->
### Collaborator emeriti

* [andrasq](https://github.com/andrasq) -
**Andras** &lt;<EMAIL>&gt;
* [AnnaMag](https://github.com/AnnaMag) -
**Anna M. Kedzierska** &lt;<EMAIL>&gt;
* [AndreasMadsen](https://github.com/AndreasMadsen) -
**Andreas Madsen** &lt;<EMAIL>&gt; (he/him)
* [aqrln](https://github.com/aqrln) -
**Alexey Orlenko** &lt;<EMAIL>&gt; (he/him)
* [bnoordhuis](https://github.com/bnoordhuis) -
**Ben Noordhuis** &lt;<EMAIL>&gt;
* [brendanashworth](https://github.com/brendanashworth) -
**Brendan Ashworth** &lt;<EMAIL>&gt;
* [calvinmetcalf](https://github.com/calvinmetcalf) -
**Calvin Metcalf** &lt;<EMAIL>&gt;
* [chrisdickinson](https://github.com/chrisdickinson) -
**Chris Dickinson** &lt;<EMAIL>&gt;
* [claudiorodriguez](https://github.com/claudiorodriguez) -
**Claudio Rodriguez** &lt;<EMAIL>&gt;
* [DavidCai1993](https://github.com/DavidCai1993) -
**David Cai** &lt;<EMAIL>&gt; (he/him)
* [digitalinfinity](https://github.com/digitalinfinity) -
**Hitesh Kanwathirtha** &lt;<EMAIL>&gt; (he/him)
* [eljefedelrodeodeljefe](https://github.com/eljefedelrodeodeljefe) -
**Robert Jefe Lindstaedt** &lt;<EMAIL>&gt;
* [estliberitas](https://github.com/estliberitas) -
**Alexander Makarenko** &lt;<EMAIL>&gt;
* [firedfox](https://github.com/firedfox) -
**Daniel Wang** &lt;<EMAIL>&gt;
* [gdams](https://github.com/gdams) -
**George Adams** &lt;<EMAIL>&gt; (he/him)
* [gibfahn](https://github.com/gibfahn) -
**Gibson Fahnestock** &lt;<EMAIL>&gt; (he/him)
* [glentiki](https://github.com/glentiki) -
**Glen Keane** &lt;<EMAIL>&gt; (he/him)
* [iarna](https://github.com/iarna) -
**Rebecca Turner** &lt;<EMAIL>&gt;
* [imran-iq](https://github.com/imran-iq) -
**Imran Iqbal** &lt;<EMAIL>&gt;
* [imyller](https://github.com/imyller) -
**Ilkka Myller** &lt;<EMAIL>&gt;
* [isaacs](https://github.com/isaacs) -
**Isaac Z. Schlueter** &lt;<EMAIL>&gt;
* [italoacasas](https://github.com/italoacasas) -
**Italo A. Casas** &lt;<EMAIL>&gt; (he/him)
* [jasongin](https://github.com/jasongin) -
**Jason Ginchereau** &lt;<EMAIL>&gt;
* [jbergstroem](https://github.com/jbergstroem) -
**Johan Bergström** &lt;<EMAIL>&gt;
* [jdalton](https://github.com/jdalton) -
**John-David Dalton** &lt;<EMAIL>&gt;
* [jhamhader](https://github.com/jhamhader) -
**Yuval Brik** &lt;<EMAIL>&gt;
* [joshgav](https://github.com/joshgav) -
**Josh Gavant** &lt;<EMAIL>&gt;
* [julianduque](https://github.com/julianduque) -
**Julian Duque** &lt;<EMAIL>&gt; (he/him)
* [kfarnung](https://github.com/kfarnung) -
**Kyle Farnung** &lt;<EMAIL>&gt; (he/him)
* [kunalspathak](https://github.com/kunalspathak) -
**Kunal Pathak** &lt;<EMAIL>&gt;
* [lance](https://github.com/lance) -
**Lance Ball** &lt;<EMAIL>&gt; (he/him)
* [lucamaraschi](https://github.com/lucamaraschi) -
**Luca Maraschi** &lt;<EMAIL>&gt; (he/him)
* [lxe](https://github.com/lxe) -
**Aleksey Smolenchuk** &lt;<EMAIL>&gt;
* [maclover7](https://github.com/maclover7) -
**Jon Moss** &lt;<EMAIL>&gt; (he/him)
* [matthewloring](https://github.com/matthewloring) -
**Matthew Loring** &lt;<EMAIL>&gt;
* [micnic](https://github.com/micnic) -
**Nicu Micleușanu** &lt;<EMAIL>&gt; (he/him)
* [mikeal](https://github.com/mikeal) -
**Mikeal Rogers** &lt;<EMAIL>&gt;
* [monsanto](https://github.com/monsanto) -
**Christopher Monsanto** &lt;<EMAIL>&gt;
* [MoonBall](https://github.com/MoonBall) -
**Chen Gang** &lt;<EMAIL>&gt;
* [not-an-aardvark](https://github.com/not-an-aardvark) -
**Teddy Katz** &lt;<EMAIL>&gt; (he/him)
* [ofrobots](https://github.com/ofrobots) -
**Ali Ijaz Sheikh** &lt;<EMAIL>&gt; (he/him)
* [Olegas](https://github.com/Olegas) -
**Oleg Elifantiev** &lt;<EMAIL>&gt;
* [orangemocha](https://github.com/orangemocha) -
**Alexis Campailla** &lt;<EMAIL>&gt;
* [othiym23](https://github.com/othiym23) -
**Forrest L Norvell** &lt;<EMAIL>&gt; (he/him)
* [petkaantonov](https://github.com/petkaantonov) -
**Petka Antonov** &lt;<EMAIL>&gt;
* [phillipj](https://github.com/phillipj) -
**Phillip Johnsen** &lt;<EMAIL>&gt;
* [piscisaureus](https://github.com/piscisaureus) -
**Bert Belder** &lt;<EMAIL>&gt;
* [pmq20](https://github.com/pmq20) -
**Minqi Pan** &lt;<EMAIL>&gt;
* [princejwesley](https://github.com/princejwesley) -
**Prince John Wesley** &lt;<EMAIL>&gt;
* [psmarshall](https://github.com/psmarshall) -
**Peter Marshall** &lt;<EMAIL>&gt; (he/him)
* [rlidwka](https://github.com/rlidwka) -
**Alex Kocharin** &lt;<EMAIL>&gt;
* [rmg](https://github.com/rmg) -
**Ryan Graham** &lt;<EMAIL>&gt;
* [robertkowalski](https://github.com/robertkowalski) -
**Robert Kowalski** &lt;<EMAIL>&gt;
* [romankl](https://github.com/romankl) -
**Roman Klauke** &lt;<EMAIL>&gt;
* [ronkorving](https://github.com/ronkorving) -
**Ron Korving** &lt;<EMAIL>&gt;
* [RReverser](https://github.com/RReverser) -
**Ingvar Stepanyan** &lt;<EMAIL>&gt;
* [rubys](https://github.com/rubys) -
**Sam Ruby** &lt;<EMAIL>&gt;
* [sam-github](https://github.com/sam-github) -
**Sam Roberts** &lt;<EMAIL>&gt;
* [sebdeckers](https://github.com/sebdeckers) -
**Sebastiaan Deckers** &lt;<EMAIL>&gt;
* [shigeki](https://github.com/shigeki) -
**Shigeki Ohtsu** &lt;<EMAIL>&gt; (he/him)
* [stefanmb](https://github.com/stefanmb) -
**Stefan Budeanu** &lt;<EMAIL>&gt;
* [tellnes](https://github.com/tellnes) -
**Christian Tellnes** &lt;<EMAIL>&gt;
* [thefourtheye](https://github.com/thefourtheye) -
**Sakthipriyan Vairamani** &lt;<EMAIL>&gt; (he/him)
* [thlorenz](https://github.com/thlorenz) -
**Thorsten Lorenz** &lt;<EMAIL>&gt;
* [trevnorris](https://github.com/trevnorris) -
**Trevor Norris** &lt;<EMAIL>&gt;
* [tunniclm](https://github.com/tunniclm) -
**Mike Tunnicliffe** &lt;<EMAIL>&gt;
* [vkurchatkin](https://github.com/vkurchatkin) -
**Vladimir Kurchatkin** &lt;<EMAIL>&gt;
* [vsemozhetbyt](https://github.com/vsemozhetbyt) -
**Vse Mozhet Byt** &lt;<EMAIL>&gt; (he/him)
* [whitlockjc](https://github.com/whitlockjc) -
**Jeremy Whitlock** &lt;<EMAIL>&gt;

</details>
<!--lint enable prohibited-strings-->

Collaborators follow the [Collaborator Guide](./doc/guides/collaborator-guide.md) in
maintaining the Node.js project.

### Triagers

* [Ayase-252](https://github.com/Ayase-252) -
**Qingyu Deng** &lt;<EMAIL>&gt;
* [himadriganguly](https://github.com/himadriganguly) -
**Himadri Ganguly** &lt;<EMAIL>&gt; (he/him)
* [marsonya](https://github.com/marsonya) -
**Akhil Marsonya** &lt;<EMAIL>&gt; (he/him)
* [PoojaDurgad](https://github.com/PoojaDurgad) -
**Pooja Durgad** &lt;<EMAIL>&gt;
* [RaisinTen](https://github.com/RaisinTen) -
**Darshan Sen** &lt;<EMAIL>&gt;

### Release keys

Primary GPG keys for Node.js Releasers (some Releasers sign with subkeys):

* **Beth Griggs** &lt;<EMAIL>&gt;
`4ED778F539E3634C779C87C6D7062848A1AB005C`
* **Colin Ihrig** &lt;<EMAIL>&gt;
`94AE36675C464D64BAFA68DD7434390BDBE9B9C5`
* **Danielle Adams** &lt;<EMAIL>&gt;
`74F12602B6F1C4E913FAA37AD3A89613643B6201`
* **James M Snell** &lt;<EMAIL>&gt;
`71DCFD284A79C3B38668286BC97EC7A07EDE3FC1`
* **Michaël Zasso** &lt;<EMAIL>&gt;
`8FCCA13FEF1D0C2E91008E09770F7A9A5AE15600`
* **Myles Borins** &lt;<EMAIL>&gt;
`C4F0DFFF4E8C1A8236409D08E73BC641CC11F4C8`
* **Richard Lau** &lt;<EMAIL>&gt;
`C82FA3AE1CBEDC6BE46B9360C43CEC45C17AB93C`
* **Rod Vagg** &lt;<EMAIL>&gt;
`DD8F2338BAE7501E3DD5AC78C273792F7D83545D`
* **Ruben Bridgewater** &lt;<EMAIL>&gt;
`A48C2BEE680E841632CD4E44F07496B3EB3C1762`
* **Ruy Adorno** &lt;<EMAIL>&gt;
`108F52B48DB57BB0CC439B2997B01419BD92F80A`
* **Shelley Vohr** &lt;<EMAIL>&gt;
`B9E2F5981AA6E0CD28160D9FF13993A75599653C`

To import the full set of trusted release keys (including subkeys possibly used
to sign releases):

```bash
gpg --keyserver pool.sks-keyservers.net --recv-keys 4ED778F539E3634C779C87C6D7062848A1AB005C
gpg --keyserver pool.sks-keyservers.net --recv-keys 94AE36675C464D64BAFA68DD7434390BDBE9B9C5
gpg --keyserver pool.sks-keyservers.net --recv-keys 74F12602B6F1C4E913FAA37AD3A89613643B6201
gpg --keyserver pool.sks-keyservers.net --recv-keys 71DCFD284A79C3B38668286BC97EC7A07EDE3FC1
gpg --keyserver pool.sks-keyservers.net --recv-keys 8FCCA13FEF1D0C2E91008E09770F7A9A5AE15600
gpg --keyserver pool.sks-keyservers.net --recv-keys C4F0DFFF4E8C1A8236409D08E73BC641CC11F4C8
gpg --keyserver pool.sks-keyservers.net --recv-keys C82FA3AE1CBEDC6BE46B9360C43CEC45C17AB93C
gpg --keyserver pool.sks-keyservers.net --recv-keys DD8F2338BAE7501E3DD5AC78C273792F7D83545D
gpg --keyserver pool.sks-keyservers.net --recv-keys A48C2BEE680E841632CD4E44F07496B3EB3C1762
gpg --keyserver pool.sks-keyservers.net --recv-keys 108F52B48DB57BB0CC439B2997B01419BD92F80A
gpg --keyserver pool.sks-keyservers.net --recv-keys B9E2F5981AA6E0CD28160D9FF13993A75599653C
```

See the section above on [Verifying Binaries](#verifying-binaries) for how to
use these keys to verify a downloaded file.

<details>

<summary>Other keys used to sign some previous releases</summary>

* **Chris Dickinson** &lt;<EMAIL>&gt;
`9554F04D7259F04124DE6B476D5A82AC7E37093B`
* **Danielle Adams** &lt;<EMAIL>&gt;
`1C050899334244A8AF75E53792EF661D867B9DFA`
* **Evan Lucas** &lt;<EMAIL>&gt;
`B9AE9905FFD7803F25714661B63B535A4C206CA9`
* **Gibson Fahnestock** &lt;<EMAIL>&gt;
`77984A986EBC2AA786BC0F66B01FBB92821C587A`
* **Isaac Z. Schlueter** &lt;<EMAIL>&gt;
`93C7E9E91B49E432C2F75674B0A78B0A6C481CF6`
* **Italo A. Casas** &lt;<EMAIL>&gt;
`56730D5401028683275BD23C23EFEFE93C4CFFFE`
* **Jeremiah Senkpiel** &lt;<EMAIL>&gt;
`FD3A5288F042B6850C66B31F09FE44734EB7990E`
* **Julien Gilli** &lt;<EMAIL>&gt;
`114F43EE0176B71C7BC219DD50A3051F888C628D`
* **Timothy J Fontaine** &lt;<EMAIL>&gt;
`7937DFD2AB06298B2293C3187D33FF9D0246406D`

</details>

## License

Node.js is available under the
[MIT license](https://opensource.org/licenses/MIT). Node.js also includes
external libraries that are available under a variety of licenses.  See
[LICENSE](https://github.com/nodejs/node/blob/HEAD/LICENSE) for the full
license text.

[Code of Conduct]: https://github.com/nodejs/admin/blob/HEAD/CODE_OF_CONDUCT.md
[Contributing to the project]: CONTRIBUTING.md
[Node.js Website]: https://nodejs.org/
[OpenJS Foundation]: https://openjsf.org/
[Strategic initiatives]: doc/guides/strategic-initiatives.md
[Technical values and prioritization]: doc/guides/technical-values.md
[Working Groups]: https://github.com/nodejs/TSC/blob/HEAD/WORKING_GROUPS.md
