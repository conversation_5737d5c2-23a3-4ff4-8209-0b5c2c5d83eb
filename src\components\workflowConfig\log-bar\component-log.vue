<!-- 组件日志 -->
<template>
  <div class="components-logs">
    <el-table ref="componentsTableRef" v-if="tableData.length !== 0"
      :data="tableData" style="width: 100%" border size="small"
      v-loading="loading" height="100%">
      <el-table-column prop="moduleName" label="节点名称" min-width="120px" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="executeState" label="运行状态" min-width="80px">
        <template slot-scope="scope">
          <span :class="['dot', `b-${componentMapping[scope.row.executeState].icon}`]"></span>
          <span>{{ componentMapping[scope.row.executeState].label }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="exeStartTime" label="开始时间" min-width="112px" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="exeEndTime" label="结束时间" min-width="112px" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="exeResult" label="操作结果" min-width="169px" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="address" label="操作" min-width="82px">
        <template slot-scope="scope">
          <LogDetails :row="scope.row" />
          <span class="opt-text" @click="locationComponent(scope.row)" style="margin:0 12px">定位</span>
          <!-- <span class="opt-text" @click="componentResult(scope.row)" style="margin-left: 12px">组件结果</span> -->
          <ComponentResult v-if="scope.row.showBtn" :row="scope.row" />
        </template>
      </el-table-column>
    </el-table>
    <el-empty class="empty" v-else :image-size="40" description="暂无组件日志信息">
    </el-empty>

    <!-- <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>
<script >
import { getTaskIdApi, getCompnentsResult, getCompnentsLogsApi } from '@/api/log.js';
import LogDetails from './log-details.vue';
import ComponentResult from './componentResult';
import LogBase from './log-base.vue';
import { bus } from '@/libs/bus';
import { componentMapping } from '@/libs/types.js'
import { getWorkflow } from '@/api/https'
export default {
  data() {
    return {
      tableData: [],
      logDetaisVisibility: false,
      componentMapping,
      dialogTitle: '',
      dialogVisible: false,
      workflowId: ''
    };
  },
  extends: LogBase,
  components: {
    LogDetails,
    ComponentResult
  },
  created () {
    this.workflowId = window.GetQueryValue('workflowId')
    
    getWorkflow({id: this.workflowId}).then(res => {
      
      const arr = res.result.connList
      let fromList = []
      let toList = []
      // 除MYSQL 隐藏组件结果 to里面不含form里的id
      if (res.result.category == 'MYSQL') {
        const a = []
        res.result.modList.forEach(item => {
          a.push(item.id)
        })
        this.getList(a)
      } else {
        arr.forEach(item => {
          fromList.push(item.from)
          toList.push(item.to)
        })
        let lastList = []
        toList.forEach(item => {
          const a = fromList.filter(row => row == item)
          if (a.length == 0) {
            lastList.push(item)
          }
        })
        this.getList(lastList)
      }
      
    })
  },
  methods: {
    getList (list) {
      getCompnentsLogsApi(this.$store.state.taskId).then(res => {
        
        const arr = res.result.moduleExecuteLogDTOList
        arr.map(item => {
          const a = list.filter(row => row == item.moduleId)
          if (a.length > 0) {
            item.showBtn = true
          }
        })
        this.tableData = arr
      })
    },
    clickDetails() {
      this.logDetaisVisibility = true;
    },
    // 定位
    locationComponent(row) {
      if (!row.moduleId) return this.$message.error('组件ID不能为空')
      bus.$emit('highlight', row.moduleId);
    },
    componentResult(val) {
      console.log(val)
      if (window.GetQueryValue('workflowId')) {
        getTaskIdApi(window.GetQueryValue('workflowId')).then(res => {
          if (res.code == 200) {
            let params = {
              taskId: res.result,
              moduleId: val.moduleId
            }
            // this.$emit('openComponentResult')
            getCompnentsResult(params).then(flas => {
              console.log(flas)
            })
          }
        })
        // this.taskIdIsChanged = this.$store.state.taskId === result ? false : true
        // if (result) this.$store.commit('setTaskId', result);
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then(() => {
          done();
        })
        .catch(() => { });
    }

  },
};
</script>
<style lang='scss' scoped>
.components-logs {
  height: 100%;

  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }

  .empty {
    height: 100%;
    border: 1px #dcdfe6 solid;
  }
}
</style>