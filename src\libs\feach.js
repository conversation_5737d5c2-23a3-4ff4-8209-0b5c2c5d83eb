import axios from 'axios'
import EncryptionDecrypt from '@/libs/encryption-decrypt'
// import store from '../store'
import {
    // MessageBox,
    Message
} from 'element-ui'
// 创建axios实例
const service = axios.create({
    // baseURL: process.env.VUE_APP_URL, // api 的 base_url
    // baseURL: window.base_url,
    timeout: 10000 // 请求超时时间
})
// console.log("token:"+dataOsToken)
// ?accountToken=72befe8612354f6eaa43ee0bb800b0f4000000&workflowId=9cc1b20c4447368553fc368149d4a4ba&category=FLINK&name=test1111&applicationCode=ai20230403001
service.defaults.headers = {
    // 'Content-Type': 'application/x-www-form-urlencoded'
    'Content-Type': 'application/json; charset=utf-8',
    accessToken: window.dataOsToken,
    applicationCode: window.applicationCode
}

// request拦截器
service.interceptors.request.use(
    config => {
        // 敏感字段加密处理
        EncryptionDecrypt.encryption(config)
        config.headers?.timestamp || new Date().getTime()
        // if(localStorage.accessToken){
        //   config.headers['Authorization'] = localStorage.accessToken
        // }
        // if (store.getters.token) {
        //   config.headers['X-Token'] = store.getters.token
        // }
        // config.headers['X-Token'] = 'a2619fc5cb4b4cde9f25911873f37536'
        return config
    },
    error => {
        console.log(error)
        Promise.reject(error)
    }
)

var showModal = true // 控制弹窗显示

function showMessage(msg) {
    if (showModal) {
        showModal = false
        Message({
            showClose: true,
            dangerouslyUseHTMLString: true,
            message: '<p style="max-height:350px; word-wrap: break-word;word-break: break-all;overflow: hidden;">' + msg + '</p>',
            type: 'error'
        })
        setTimeout(function () {
            showModal = true
        }, 1000)
        // MessageBox.alert(msg, '提示', {
        //     type: "warning",
        //     dangerouslyUseHTMLString: true,
        //     message: '<pre style="max-height:350px;overflow-y: auto;">' + msg + '</pre>',
        //     customClass: 'customClass',
        //     callback: () => {
        //         // 清除失效token
        //         removeToken();
        //         //跳转登录页
        //         router.push({
        //             path: '/login',
        //             query: {
        //                 redirect: location.hostname
        //             }
        //         })
        //         showModal = true
        //     }
        // });
    }
}

service.interceptors.response.use(
    response => {
        // 敏感字段加密处理
        if(response.data) EncryptionDecrypt.decryption(response)
        // ...请求成功后的后续操作
        const res = response.data
        if (!res.code) {
            return res
        } else {
            if (res.code == 11030113) {
                showMessage("登录信息已过期,请重新登录")
            }
            if (res.code !== 200) {

                if (document.querySelectorAll('.el-message--success').length > 0) {
                    return false
                }
                showMessage(res.message)
            }
            return res
        }
    },
    err => {
        if (err.message.indexOf('timeout') != -1) {
            showMessage("请求超时，请稍后重试")
        } else {
            if (err.response || err.message) {
                if (document.querySelectorAll('.el-message--success').length > 0) {
                    return false
                }
                showMessage(err.response.data.message || err.message)
            }
        }
        return Promise.reject(err);
    }
)

const service1 = axios.create({
  // 超时
  timeout: 5 * 60000
})
service1.defaults.headers = {
  // 'Content-Type': 'application/x-www-form-urlencoded'
  accessToken: window.dataOsToken,
  applicationCode: window.applicationCode
}
service1.interceptors.request.use(config => {
  return config
}, error => {
  Promise.reject(error)
})

// 通用下载方法
export function download(url, params, filename) {
  return service1(
    {
      url,
      method: "get",
      params,
      responseType: 'arraybuffer'
    }
  ).then((_res) => {
    const blob = new Blob([_res.data], { type: 'application/vnd.ms-excel;' })
    const a = document.createElement('a')
    // 生成文件路径
    const href = (window.URL || window.webkitURL).createObjectURL(blob)
    a.href = href
    var disposition = _res.headers['content-disposition']
    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    var matches = filenameRegex.exec(disposition)
    if (matches != null && matches[1]) {
      filename = decodeURIComponent(matches[1].replace(/['"]/g, ''))
    }
    a.download = filename
    // 利用a标签做下载
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(href)
  }).catch((error) => {
    showMessage(error.response.data.message || error.message)
  })
}


export default service