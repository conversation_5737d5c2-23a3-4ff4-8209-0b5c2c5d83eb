/**
 * 敏感接口请求参数、响应数据加密
 */
 const CryptoJS = require('crypto-js');


 const EncryptionDecrypt = {
   // 加密配置项
   option: {
     mode: CryptoJS.mode.ECB,
     padding: CryptoJS.pad.Pkcs7
   },
 
   // 敏感接口黑名单
   sensitiveAPIs: [
     '/resources'
   ],
 
 
   // 检测接口是否需要加密
   checkAPI(url) {
     return EncryptionDecrypt.sensitiveAPIs.some(item => url.includes(item))
   },
 
   // 获取加密Key
   getKey(timestamp) {
     return CryptoJS.enc.Utf8.parse('000' + timestamp);
   },
 
   // 加密处理
   encryptionHandle(sensitiveValue, timestamp) {
     const ciphertext = CryptoJS.AES.encrypt(sensitiveValue, EncryptionDecrypt.getKey(timestamp), EncryptionDecrypt.option)
     return ciphertext.toString();
   },
 
   // 解密处理
   decryptionHandle(sensitiveValue, timestamp) {
     const bytes = CryptoJS.AES.decrypt(sensitiveValue, EncryptionDecrypt.getKey(timestamp), EncryptionDecrypt.option)
     return CryptoJS.enc.Utf8.stringify(bytes).toString()
   },
 
   // 加密 data: 加密数据对象， options请求参数
   encryption(options) {
     const { params, data, url } = options;
     if((!params && !data) || !this.checkAPI(url)) return
     if(!options.headers) options.headers = {'Content-Type': 'application/json; charset=utf-8'};
     options.headers.timestamp = new Date().getTime()
     // get请求不加密入参
     if(options.method === 'get') return
     const encrypteData = this.encryptionHandle(JSON.stringify(data), options.headers.timestamp)
     if(encrypteData) params ? options.params = encrypteData: options.data = encrypteData
     // console.log('加密：', url, encrypteData);
   },
 
   // 解密 data: 加密数据对象， timestamp请求头时间戳
   decryption(response) {
     const { data, config } = response
     const timestamp = config?.headers?.timestamp
     if(!timestamp || !this.checkAPI(config.url) || !data?.result) return
     const decrypteData = this.decryptionHandle(data.result, timestamp)
     if(decrypteData) response.data.data = JSON.parse(decrypteData)
     // console.log('解密：', config.url, JSON.parse(decrypteData));
   },
 }
 
 export default EncryptionDecrypt