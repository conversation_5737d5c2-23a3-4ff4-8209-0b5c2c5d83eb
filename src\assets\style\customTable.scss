.el-table {
  &::before {
    background-color: transparent
  }

  /deep/ .el-table--small td,
  .el-table--small th {
    padding: 0px
  }
 

  /deep/ thead {
    background: #F5F7FA;
    background: rgba(234, 238, 239, 1);
    border-radius: 4px 4px 0px 0px;

    tr th {
      background: #F5F7FA;
      border-bottom: 1px solid #e3e3e3;
      height: 32px;
      line-height: 32px;
      font-weight: 400;
    }

    tr th>.cell {
      font-size: 14px;
      color: #606266
    }
  }

  /deep/.el-table__row td .cell {
    font-size: 14px;
    color: #606266
    // padding: 0px
  }
  /deep/.el-table__row {
    tr th {
      background: #F5F7FA;
      border-bottom: 1px solid #e3e3e3;
      height: 32px;
      line-height: 32px;
      font-weight: 400;
    }
  }

  /deep/.el-table__row td {
    // padding: 0px 10px
  }
  
  /deep/ .el-table__body-wrapper tr {
    td {
      border-bottom: 1px solid #F2F6F7;
      border-right: none !important;
    }
  }

  /deep/ .el-table__body tr:hover>td {
    // background-color: #ffffff !important;
  }

  /deep/ .el-table__body-wrapper tr td
  /deep/.el-table__body tr.current-row>td {
    background: #CADBDF;
    border-right: none !important;

    /deep/.el-input__inner {
      color: #5E6572;
      background: #fff;
      border: 1px solid #CADBDF;
      cursor: default;
    }
  }

  ::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    background: #D7DFE4 !important;
    ;
  }

  ::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    background: #fff !important;
    ;
  }
.operationBtn {
    color: #279eb7;
  }
.operationBtn:hover {
    cursor: pointer;
  }
.operationBtnDel {
    color: #f06c6c;
  }
.operationBtnDel:hover {
    cursor: pointer;
  }
.operationBtnDisabled {
    opacity: 0.3;
  }
.operationBtnDisabled:hover {
    cursor: auto;
  }
}