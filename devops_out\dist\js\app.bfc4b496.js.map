{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/components/workflowConfig/log-bar/component-log.vue?f53c", "webpack:///./src/views/Home.vue?3825", "webpack:///./src/assets/icons/auto_layout.svg", "webpack:///./src/components/workflowConfig/log-bar/log-bar.vue?0b35", "webpack:///./src/components/nodeSelector.vue?fa14", "webpack:///./src/components/contextMenu.vue?9b68", "webpack:///./node_modules/moment/locale sync ^\\.\\/.*$", "webpack:///./src/components/workflowConfig/right-config-bar.vue?579f", "webpack:///./src/App.vue?d0d4", "webpack:///./src/App.vue", "webpack:///./src/views/Home.vue?c4ff", "webpack:///./src/components/menuBar.vue?068a", "webpack:///./src/libs/encryption-decrypt.js", "webpack:///./src/libs/feach.js", "webpack:///./src/api/https.js", "webpack:///./src/libs/bus.js", "webpack:///src/components/menuBar.vue", "webpack:///./src/components/menuBar.vue?ae95", "webpack:///./src/components/menuBar.vue", "webpack:///./src/components/nodeSelector.vue?83cf", "webpack:///src/components/nodeSelector.vue", "webpack:///./src/components/nodeSelector.vue?683e", "webpack:///./src/components/nodeSelector.vue", "webpack:///./src/components/canvasToolbox.vue?6a18*", "webpack:///src/components/canvasToolbox.vue", "webpack:///./src/components/canvasToolbox.vue?1932", "webpack:///./src/components/canvasToolbox.vue", "webpack:///./src/components/graphCanvas.vue?704c", "webpack:///./src/components/shape.vue?7199", "webpack:///./src/libs/types.js", "webpack:///src/components/shape.vue", "webpack:///./src/components/shape.vue?7bd6", "webpack:///./src/components/shape.vue", "webpack:///./src/components/contextMenu.vue?c771", "webpack:///src/components/contextMenu.vue", "webpack:///./src/components/contextMenu.vue?7e44", "webpack:///./src/components/contextMenu.vue", "webpack:///./src/api/log.js", "webpack:///./src/mixin/logs.js", "webpack:///./src/mixin/graph.js", "webpack:///src/components/graphCanvas.vue", "webpack:///./src/components/graphCanvas.vue?e711", "webpack:///./src/components/graphCanvas.vue", "webpack:///./src/components/workflowConfig/right-config-bar.vue?d951", "webpack:///src/components/workflowConfig/right-config-bar.vue", "webpack:///./src/components/workflowConfig/right-config-bar.vue?1239", "webpack:///./src/components/workflowConfig/right-config-bar.vue", "webpack:///./src/components/workflowConfig/right-config-resource.vue?5562", "webpack:///./src/api/workflowConfig.js", "webpack:///src/components/workflowConfig/right-config-resource.vue", "webpack:///./src/components/workflowConfig/right-config-resource.vue?ebae", "webpack:///./src/components/workflowConfig/right-config-resource.vue", "webpack:///./src/components/workflowConfig/right-config-dispatch.vue?2ce5", "webpack:///./src/libs/common.js", "webpack:///./src/libs/validDate.js", "webpack:///src/components/workflowConfig/right-config-dispatch.vue", "webpack:///./src/components/workflowConfig/right-config-dispatch.vue?a5fb", "webpack:///./src/components/workflowConfig/right-config-dispatch.vue", "webpack:///./src/components/workflowConfig/log-bar/log-bar.vue?3d45", "webpack:///./src/components/workflowConfig/log-bar/process-log.vue?e818", "webpack:///./src/components/workflowConfig/log-bar/log-base.vue?8a29", "webpack:///src/components/workflowConfig/log-bar/log-base.vue", "webpack:///./src/components/workflowConfig/log-bar/log-base.vue?6659", "webpack:///./src/components/workflowConfig/log-bar/log-base.vue", "webpack:///src/components/workflowConfig/log-bar/process-log.vue", "webpack:///./src/components/workflowConfig/log-bar/process-log.vue?200d", "webpack:///./src/components/workflowConfig/log-bar/process-log.vue", "webpack:///./src/components/workflowConfig/log-bar/component-log.vue?7e9b", "webpack:///./src/components/workflowConfig/log-bar/log-details.vue?f75b", "webpack:///./src/libs/utils.js", "webpack:///src/components/workflowConfig/log-bar/log-details.vue", "webpack:///./src/components/workflowConfig/log-bar/log-details.vue?1e66", "webpack:///./src/components/workflowConfig/log-bar/log-details.vue", "webpack:///src/components/workflowConfig/log-bar/component-log.vue", "webpack:///./src/components/workflowConfig/log-bar/component-log.vue?1766", "webpack:///./src/components/workflowConfig/log-bar/component-log.vue", "webpack:///src/components/workflowConfig/log-bar/log-bar.vue", "webpack:///./src/components/workflowConfig/log-bar/log-bar.vue?05d2", "webpack:///./src/components/workflowConfig/log-bar/log-bar.vue", "webpack:///src/views/Home.vue", "webpack:///./src/views/Home.vue?73d4", "webpack:///./src/views/Home.vue", "webpack:///./src/views/workflowAdd.vue?c1fe", "webpack:///src/views/workflowAdd.vue", "webpack:///./src/views/workflowAdd.vue?aa93", "webpack:///./src/views/workflowAdd.vue", "webpack:///./src/router/index.js", "webpack:///./src/store/index.js", "webpack:///./src/libs/directive.js", "webpack:///./src/components/SvgIcon.vue?089a", "webpack:///src/components/SvgIcon.vue", "webpack:///./src/components/SvgIcon.vue?0be2", "webpack:///./src/components/SvgIcon.vue", "webpack:///./src/main.js", "webpack:///./src/assets/images/log.png", "webpack:///./src/components/workflowConfig/log-bar/process-log.vue?4aff", "webpack:///./src/components/shape.vue?781b", "webpack:///./src/assets/images/components/icon-runFailed.png", "webpack:///./src/components/graphCanvas.vue?985e", "webpack:///./src/assets/icons sync nonrecursive \\.svg$", "webpack:///./src/components/menuBar.vue?2786", "webpack:///./src/components/workflowConfig/right-config-dispatch.vue?e295", "webpack:///./src/components/canvasToolbox.vue?d1d5", "webpack:///./src/assets/icons/funnel.svg", "webpack:///./src/components/SvgIcon.vue?7f17", "webpack:///./src/components/workflowConfig/right-config-resource.vue?95ff", "webpack:///./src/assets/images/components sync ^\\.\\/icon\\-.*\\.png$", "webpack:///./src/assets/images/warning.png", "webpack:///./src/assets/images/components/icon-default.png", "webpack:///./src/assets/images/components/icon-noConfigure.png", "webpack:///./src/assets/images/components/icon-runSuccessfully.png", "webpack:///./src/components/workflowConfig/log-bar/log-details.vue?196c", "webpack:///./src/assets/images/components/icon-inOperation.png"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "symbol", "add", "map", "webpackContext", "req", "id", "webpackContextResolve", "e", "Error", "code", "keys", "resolve", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "staticRenderFns", "script", "component", "staticClass", "on", "$event", "nodeSelectorHideToggle", "nodeSelectorHide", "_v", "class", "refresh", "processValid", "handleSave", "handleExecute", "workflowStatus", "handleSchedule", "_e", "handleFreeze", "validDialog", "validDialogClose", "validOption", "unConnectModules", "_s", "staticStyle", "scopedSlots", "_u", "fn", "scope", "handlePosition", "row", "unConfigModules", "trackUnique", "startNodeIsValid", "finishNodeIsValid", "slot", "monitorDialog", "processConfigVaild", "executeParamConfigured", "isNow", "scheduleConfigured", "CryptoJS", "require", "EncryptionDecrypt", "option", "ECB", "padding", "pad", "Pkcs7", "sensitiveAPIs", "checkAPI", "url", "some", "item", "includes", "<PERSON><PERSON><PERSON>", "timestamp", "enc", "Utf8", "parse", "encryptionHandle", "sensitiveValue", "ciphertext", "AES", "encrypt", "toString", "decryptionHandle", "bytes", "decrypt", "stringify", "encryption", "options", "params", "headers", "Date", "getTime", "method", "encrypteData", "JSON", "decryption", "response", "config", "decrypteData", "service", "axios", "timeout", "defaults", "accessToken", "dataOsToken", "applicationCode", "interceptors", "request", "use", "error", "console", "log", "Promise", "reject", "showModal", "showMessage", "msg", "Message", "showClose", "dangerouslyUseHTMLString", "message", "type", "setTimeout", "res", "document", "querySelectorAll", "err", "indexOf", "baseUrl", "applicationServerPath", "dataos_urlDaasMeta", "executorgover", "integrityValidate", "workflowId", "feach", "getStatus", "getWorkflowInfo", "saveWorkflow", "executeWorkflow", "scheduleWorkflow", "freezeWorkflow", "scheduleId", "queryPreviousAndSelfParam", "updateFunctionParam", "updateFunctionParamClear", "getApplication", "getWorkflow", "deleteConnInfo", "deleteNode", "updateName", "nodeAdd", "edgeAdd", "updatePosition", "nodeCopy", "workflowAdd", "bus", "<PERSON><PERSON>", "callback", "test", "category", "workflowForm", "desc", "workflowFormRules", "computed", "created", "GetQueryValue", "mounted", "$on", "getScheduleId", "methods", "val", "$emit", "$confirm", "confirmButtonText", "cancelButtonText", "then", "centerContent", "fullScreen", "toRedo", "alert", "toUndo", "model", "$$v", "keyword", "expression", "_l", "items", "index", "shrink", "identifier", "moduleDescription", "dragNode", "img", "displayName", "componentsList", "componentsData", "trim", "filter", "itemList", "watch", "$nextTick", "style", "height", "querySelector", "event", "offsetHeight", "isFullScreen", "scale", "directives", "rawName", "autoLayout", "gridToggle", "fullScreenChange", "level", "ref", "contextMenuPosition", "contextMenuVisible", "currentNode", "contextMenuEvent", "dialogVisible", "dialogWidth", "templateTitle", "componentUrl", "loadandpostmessage", "arg", "loading", "onConfirmation", "function", "input", "output", "shapeData", "undefined", "runStatus", "executeStatus", "getIcon", "componentMapping", "EXECUTING", "label", "icon", "FAIL", "COMPLETED", "UNSETING", "NOTRUNNING", "processStatusMapping", "SUBMITTING", "UN_EXECUTE", "STOP_EXECUTE", "IGNORE", "inject", "self", "node", "getData", "current", "edges", "edge", "attr", "left", "position", "x", "top", "y", "display", "visible", "copy", "handleRename", "clearFunctionParam", "props", "required", "default", "Boolean", "$prompt", "closeOnClickModal", "closeOnPressEscape", "inputPattern", "inputErrorMessage", "inputValue", "text", "getTaskIdApi", "getProcessLogsApi", "taskId", "getCompnentsLogsApi", "executeState", "taskIdIsChanged", "timer", "firstGetProcessLogs", "handle", "setInterval", "getTaskId", "$store", "state", "logBarIsOpen", "getComponentsLogs", "getProcessLogs", "commit", "taskExecuteLogList", "for<PERSON>ach", "uuId", "uuidv4", "ou<PERSON>", "recordDatetime", "extendMsg", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "$off", "renderLayoutGraph", "nodes", "isDagreLayout", "dagreLayout", "DagreLayout", "rankdir", "ranksep", "nodesep", "layout", "graph", "fromJSON", "layoutModelPositions", "updateModelPosition", "modelPositions", "bindComponentStatus", "cells", "get<PERSON>ells", "componentLogs", "moduleExecuteLogDTOList", "logData", "logItem", "moduleStatus", "prop", "correctParentId", "parent<PERSON>son", "nodeData", "mixins", "logs", "components", "contextMenu", "lastTargetCell", "navList", "codeList", "ability", "showGrid", "knob", "container", "headHeight", "dnd", "resizeTimer", "updateStatus", "modList", "connList", "notInitModList", "resize", "innerWidth", "addEventListener", "onmessage", "parent", "postMessage", "highlight", "onResize", "componentsUrl", "v", "initCanvas", "width", "innerHeight", "background", "color", "panning", "history", "enabled", "ignoreChange", "snapline", "selecting", "highlighting", "magnetAvailable", "args", "fill", "stroke", "magnetAdsorbed", "connecting", "allowPort", "allowEdge", "allowNode", "allowLoop", "allow<PERSON><PERSON><PERSON>", "allowBlank", "connector", "sourceAnchor", "targetAnchor", "connectionPoint", "validateConnection", "targetMagnet", "getAttribute", "validateEdge", "maxConnections", "targetId", "num", "$message", "validateEdgeStatus", "from", "source", "cell", "to", "target", "sourceCell", "findViewByCell", "removeClass", "snap", "radius", "createEdge", "router", "step", "anchor", "line", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetMarker", "grid", "size", "mousewheel", "factor", "maxScale", "minScale", "zoom", "inherit", "template", "myShape", "showPorts", "ports", "visibility", "show", "targetCell", "addClass", "matrix", "centerCell", "initAddon", "Dnd", "scaled", "animation", "validateNode", "instanceCode", "moduleName", "maxOutputs", "startFlag", "endFlag", "webUrl", "getDragNode", "shape", "html", "wrap", "className", "innerHTML", "getDropNode", "groups", "in", "circle", "magnet", "out", "zIndex", "nodeId", "removeNode", "dbOrrightConfig", "renderNode", "port", "start", "MouseEvent", "createPorts", "componentInfo", "portsItems", "group", "Math", "sign", "<PERSON><PERSON><PERSON>", "checkFull", "isFull", "fullscreen", "webkitIsFullScreen", "msFullscreenEnabled", "exitFullscreen", "<PERSON><PERSON><PERSON><PERSON>", "requestFullscreen", "undo", "redo", "clearTimeout", "iframeUrl", "location", "origin", "contentWindow", "configSubmit", "updateTask", "$refs", "myApplication", "isEqual", "a", "b", "classNameA", "classNameB", "propsA", "getOwnPropertyNames", "propsB", "propName", "toUpperCase", "close", "showDesc", "click", "openUrl", "title", "componentId", "right", "rightConfigActiveName", "active", "rightConfigActiveNameChange", "resourceList", "resourceForm", "trigger", "handleResourceChange", "$set", "resourceCode", "configData", "configForm", "itemKey", "validator", "validateNumber", "itemDesc", "itemUnit", "submitForm", "dataosResource", "clearConfig", "getConfig", "updateConfig", "resourceConfig", "workflowCategory", "scheduleAdd", "scheduleUpdate", "scheduleInfo", "scheduleClear", "cron<PERSON><PERSON><PERSON>", "commonCron", "savedConfig", "allConfigData", "newValue", "getDataosResource", "getSavedConfig", "validate", "valid", "valid2", "unit", "confDesc", "itemValue", "resourceId", "executeConfigParams", "dispatchWayChange", "dispatchWay", "form", "rules", "pickerBeginDateBefore", "currentTime", "effectStartTimeCheck", "pickerBeginDateBefore1", "dateFocus", "dateBlur", "continuous", "continuousChange", "dispatchCycleChange", "dispatchCycle", "scopeStartTimeCheck", "parseInt", "scopeStartTime", "intervalStartTimeCheck", "intervalStartTime", "currentClass", "weekClick", "currentClassDay", "dayClick", "c<PERSON><PERSON><PERSON><PERSON>", "testNum", "executeCron", "elapsedTableData", "empty", "hoursOption", "cycleOption", "minOption", "spacerOptionMin", "spacerOptionHour", "spacerOptionDay", "intervalOptionDay", "secOption", "spacerOptionWeek", "optionDay", "timeValidator", "rule", "now", "specificTimeCheck", "executeTime", "specificTime", "weekTime", "isAdd", "lastSaveData", "dayOfWeeks", "showWeeks", "dayOfMonths", "showMonths", "effectStartTime", "effectEndTime", "scopeEndTime", "intervalEndTime", "spacer", "dayTime", "cron", "<PERSON><PERSON><PERSON>", "getScheduleInfo", "curDate", "$moment", "format", "str", "disabledDate", "time", "selectableRange", "ruleForm", "validateField", "errMsg", "clearValidate", "strategyType", "scheduleConfig", "period", "toLowerCase", "endTime", "startTime", "hourRange", "split", "<PERSON><PERSON><PERSON><PERSON>", "timeInterval", "second", "minute", "<PERSON><PERSON><PERSON><PERSON>", "hour", "getElementsByTagName", "cronExp", "bodyClass", "dateFocus1", "formName", "resetFields", "param", "getPara<PERSON>", "obj", "transform", "transformHeight", "log<PERSON>ar<PERSON><PERSON><PERSON>", "drag", "dragstart", "dragend", "clicklogBar", "logIcon", "getErrComponentNum", "activeName", "processLogs", "scrollToPosition", "el", "scrollTo", "scrollHeight", "behavior", "extends", "locationComponent", "min<PERSON><PERSON><PERSON>", "exeStartTime", "exeEndTime", "getTaskConsumeTime", "timeConsuming", "exeResult", "getConsumeTime", "ms", "h", "trunc", "mms", "ceil", "cancel", "submit", "openDialog", "nextTick", "tableData", "logDetaisVisibility", "LogDetails", "clickDetails", "move", "oldHeight", "showLogContainer", "getShowDragLine", "body", "mouseupClose", "removeEventListener", "err<PERSON><PERSON>ps", "ProcessLog", "ComponentLog", "tabsContent", "clientHeight", "preventDefault", "clientY", "drags<PERSON>al", "dragstartVal", "menuBar", "nodeSelector", "canvasToolbox", "graphCanvas", "rightConfigBar", "rightConfigResource", "rightConfigDispatch", "logBar", "canvasPreventDefault", "resetForm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirect", "Home", "basePath", "Vuex", "Store", "mutations", "setTaskId", "setComponentLogs", "setProcessLogs", "setLogBarIsOpen", "actions", "directive", "inserted", "binding", "timer_end", "String", "SvgIcon", "requireContext", "productionTip", "ElementUI", "moment", "locale", "store", "render", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,iJCvJT,W,oCCAA,W,oCCAA,qDAEIyC,EAAS,IAAI,IAAa,CAC5B,GAAM,mBACN,IAAO,yBACP,QAAW,gBACX,QAAW,6iFAEA,IAAOC,IAAID,GACT,gB,oCCTf,W,sFCAA,W,oCCAA,W,4CCAA,IAAIE,EAAM,CACT,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,QACX,aAAc,QACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,gBAAiB,OACjB,aAAc,OACd,gBAAiB,OACjB,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,WAAY,OACZ,cAAe,OACf,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,YAAa,OACb,eAAgB,OAChB,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,QAAS,OACT,WAAY,OACZ,OAAQ,OACR,UAAW,OACX,QAAS,OACT,WAAY,OACZ,QAAS,OACT,aAAc,OACd,gBAAiB,OACjB,WAAY,OACZ,UAAW,OACX,aAAc,OACd,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,UAAW,OACX,OAAQ,OACR,YAAa,OACb,eAAgB,OAChB,UAAW,OACX,OAAQ,OACR,UAAW,OACX,aAAc,OACd,gBAAiB,OACjB,OAAQ,OACR,UAAW,OACX,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,OACd,UAAW,OACX,aAAc,QAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOtC,EAAoBuC,GAE5B,SAASC,EAAsBF,GAC9B,IAAItC,EAAoBW,EAAEyB,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAO9D,OAAO8D,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBpC,EAAOD,QAAUkC,EACjBA,EAAeE,GAAK,Q,oCCnSpB,W,iICAI,EAAS,WAAa,IAAIO,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,IAC9IG,EAAkB,G,YCAlBC,EAAS,GAKTC,EAAY,eACdD,EACA,EACAD,GACA,EACA,KACA,KACA,MAIa,EAAAE,E,oBCjBX,EAAS,WAAa,IAAIT,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,QAAQ,CAACN,EAAG,WAAWA,EAAG,MAAM,CAACM,YAAY,eAAeJ,MAAM,CAAC,GAAK,mBAAmB,CAACF,EAAG,gBAAgBA,EAAG,iBAAiBA,EAAG,eAAeA,EAAG,kBAAkBA,EAAG,uBAAuBA,EAAG,uBAAuBA,EAAG,WAAW,IAAI,IAClW,EAAkB,GCDlB,EAAS,WAAa,IAAIJ,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,YAAY,CAACN,EAAG,MAAM,CAACM,YAAY,uBAAuBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIa,wBAAwBb,EAAIc,qBAAqB,CAACV,EAAG,OAAO,CAACM,YAAY,qBAAqB,CAACV,EAAIe,GAAG,SAASX,EAAG,KAAK,CAACM,YAAY,qCAAqCM,MAAM,CAAE,gBAAiBhB,EAAIc,sBAAuBV,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQX,EAAIiB,UAAU,CAACb,EAAG,KAAK,CAACM,YAAY,gCAAgCV,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAG,UAAUX,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQX,EAAIkB,eAAe,CAACd,EAAG,KAAK,CAACM,YAAY,8BAA8BV,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAG,YAAYX,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAImB,gBAAgB,CAACf,EAAG,KAAK,CAACM,YAAY,8BAA8BV,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAG,YAAYX,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQX,EAAIoB,gBAAgB,CAAChB,EAAG,KAAK,CAACM,YAAY,4BAA4BV,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAG,YAAmC,cAAtBf,EAAIqB,gBAAwD,mBAAtBrB,EAAIqB,eAAqC,CAACjB,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQX,EAAIsB,iBAAiB,CAAClB,EAAG,KAAK,CAACM,YAAY,mCAAmCN,EAAG,OAAO,CAACJ,EAAIe,GAAG,aAAaf,EAAIuB,KAA4B,cAAtBvB,EAAIqB,gBAAwD,mBAAtBrB,EAAIqB,eAAqC,CAACjB,EAAG,IAAI,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQX,EAAIwB,eAAe,CAACpB,EAAG,KAAK,CAACM,YAAY,mCAAmCV,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAG,aAAaf,EAAIuB,MAAM,GAAGnB,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,eAAe,cAAc,QAAUN,EAAIyB,YAAY,MAAQ,QAAQ,wBAAuB,EAAM,yBAAwB,GAAOd,GAAG,CAAC,iBAAiB,SAASC,GAAQZ,EAAIyB,YAAYb,GAAQ,MAAQZ,EAAI0B,mBAAmB,CAACtB,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,WAAWX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYC,iBAAiB7F,OAAS,kBAAoB,oBAAoBiE,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYC,iBAAiB7F,OAASiE,EAAI2B,YAAYC,iBAAiB7F,OAAS,MAAQ,eAAgBiE,EAAI2B,YAAYC,iBAAuB,OAAExB,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,aAAa,MAAM,KAAON,EAAI2B,YAAYC,iBAAiB,oBAAoB,CAAE,mBAAoB,UAAW,MAAS,aAAc,CAACxB,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOyB,YAAY/B,EAAIgC,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAImC,eAAeD,EAAME,QAAQ,CAACpC,EAAIe,GAAG,YAAY,MAAK,EAAM,eAAe,IAAI,GAAGf,EAAIuB,OAAOnB,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,WAAWX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYU,gBAAgBtG,OAAS,kBAAoB,oBAAoBiE,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYU,gBAAgBtG,OAASiE,EAAI2B,YAAYU,gBAAgBtG,OAAS,MAAQ,eAAgBiE,EAAI2B,YAAYU,gBAAsB,OAAEjC,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,aAAa,MAAM,KAAON,EAAI2B,YAAYU,gBAAgB,oBAAoB,CAAE,mBAAoB,UAAW,MAAS,aAAc,CAACjC,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,UAAUF,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,KAAK,MAAQ,OAAOyB,YAAY/B,EAAIgC,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,OAAO,KAAO,SAASK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAImC,eAAeD,EAAME,QAAQ,CAACpC,EAAIe,GAAG,YAAY,MAAK,EAAM,eAAe,IAAI,GAAGf,EAAIuB,OAAOnB,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACJ,EAAIe,GAAG,YAAYX,EAAG,aAAa,CAACM,YAAY,OAAOJ,MAAM,CAAC,OAAS,OAAO,QAAU,mCAAmC,UAAY,QAAQ,CAACF,EAAG,KAAK,CAACM,YAAY,oBAAoB,GAAGN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYW,YAAc,kBAAoB,oBAAoBtC,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYW,YAAc,OAAS,kBAAkBlC,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,cAAcX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYY,iBAAmB,kBAAoB,oBAAoBvC,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYY,iBAAmB,OAAS,kBAAkBnC,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,cAAcX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYa,kBAAoB,kBAAoB,oBAAoBxC,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYa,kBAAoB,OAAS,oBAAoBpC,EAAG,OAAO,CAACM,YAAY,gBAAgBJ,MAAM,CAAC,KAAO,UAAUmC,KAAK,UAAU,CAACrC,EAAG,YAAY,CAACO,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIyB,aAAc,KAAS,CAACzB,EAAIe,GAAG,UAAU,KAAKX,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,SAAS,eAAe,cAAc,QAAUN,EAAI0C,cAAc,MAAQ,QAAQ,wBAAuB,EAAM,yBAAwB,GAAO/B,GAAG,CAAC,iBAAiB,SAASC,GAAQZ,EAAI0C,cAAc9B,KAAU,CAACR,EAAG,MAAM,CAACA,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2C,mBAAqB,kBAAoB,oBAAoB3C,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2C,mBAAqB,OAAS,gBAAkB3C,EAAI2C,mBAAoJ3C,EAAIuB,KAApInB,EAAG,MAAM,CAAC0B,YAAY,CAAC,cAAc,WAAW,CAAC9B,EAAIe,GAAG,4BAA4BX,EAAG,MAAMJ,EAAIe,GAAG,8BAAuCX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYiB,uBAAyB,kBAAoB,oBAAoB5C,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYiB,uBAAyB,OAAS,gBAAkB5C,EAAI2B,YAAYiB,uBAA8H5C,EAAIuB,KAA1GnB,EAAG,MAAM,CAAC0B,YAAY,CAAC,cAAc,WAAW,CAAC9B,EAAIe,GAAG,gDAA2Df,EAAI6C,MAAggB7C,EAAIuB,KAA7fnB,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACM,YAAY,aAAa,CAACN,EAAG,KAAK,CAACY,MAAMhB,EAAI2B,YAAYmB,mBAAqB,kBAAoB,oBAAoB9C,EAAIe,GAAG,KAAKX,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI2B,YAAYmB,mBAAqB,OAAS,gBAAkB9C,EAAI2B,YAAYmB,mBAAyH9C,EAAIuB,KAAzGnB,EAAG,MAAM,CAAC0B,YAAY,CAAC,cAAc,WAAW,CAAC9B,EAAIe,GAAG,iDAAmEX,EAAG,OAAO,CAACM,YAAY,gBAAgBJ,MAAM,CAAC,KAAO,UAAUmC,KAAK,UAAU,CAACrC,EAAG,YAAY,CAACO,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAI0C,eAAgB,KAAS,CAAC1C,EAAIe,GAAG,UAAU,MAAM,IACpnO,EAAkB,G,2GCEfgC,G,wCAAWC,EAAQ,SAGnBC,EAAoB,CAExBC,OAAQ,CACN5E,KAAMyE,EAASzE,KAAK6E,IACpBC,QAASL,EAASM,IAAIC,OAIxBC,cAAe,CACb,cAKFC,SAdwB,SAcfC,GACP,OAAOR,EAAkBM,cAAcG,MAAK,SAAAC,GAAI,OAAIF,EAAIG,SAASD,OAInEE,OAnBwB,SAmBjBC,GACL,OAAOf,EAASgB,IAAIC,KAAKC,MAAM,MAAQH,IAIzCI,iBAxBwB,SAwBPC,EAAgBL,GAC/B,IAAMM,EAAarB,EAASsB,IAAIC,QAAQH,EAAgBlB,EAAkBY,OAAOC,GAAYb,EAAkBC,QAC/G,OAAOkB,EAAWG,YAIpBC,iBA9BwB,SA8BPL,EAAgBL,GAC/B,IAAMW,EAAQ1B,EAASsB,IAAIK,QAAQP,EAAgBlB,EAAkBY,OAAOC,GAAYb,EAAkBC,QAC1G,OAAOH,EAASgB,IAAIC,KAAKW,UAAUF,GAAOF,YAI5CK,WApCwB,SAoCbC,GACT,IAAQC,EAAsBD,EAAtBC,OAAQvJ,EAAcsJ,EAAdtJ,KAAMkI,EAAQoB,EAARpB,IACtB,IAAKqB,GAAWvJ,IAAU0E,KAAKuD,SAASC,KACpCoB,EAAQE,UAASF,EAAQE,QAAU,CAAC,eAAgB,oCACxDF,EAAQE,QAAQjB,WAAY,IAAIkB,MAAOC,UAEjB,QAAnBJ,EAAQK,QAAX,CACA,IAAMC,EAAelF,KAAKiE,iBAAiBkB,KAAKT,UAAUpJ,GAAOsJ,EAAQE,QAAQjB,WAC9EqB,IAAcL,EAASD,EAAQC,OAASK,EAAcN,EAAQtJ,KAAO4J,KAK1EE,WAjDwB,SAiDbC,GAAU,MACX/J,EAAiB+J,EAAjB/J,KAAMgK,EAAWD,EAAXC,OACRzB,EAAS,OAAGyB,QAAH,IAAGA,GAAH,UAAGA,EAAQR,eAAX,aAAG,EAAiBjB,UACnC,GAAIA,GAAc7D,KAAKuD,SAAS+B,EAAO9B,MAAQ,OAAClI,QAAD,IAACA,KAAMqB,OAAtD,CACA,IAAM4I,EAAevF,KAAKuE,iBAAiBjJ,EAAKqB,OAAQkH,GACrD0B,IAAcF,EAAS/J,KAAKA,KAAO6J,KAAKnB,MAAMuB,OAKtCvC,I,qBCzDVwC,EAAUC,IAAMjH,OAAO,CAGzBkH,QAAS,MAGbF,EAAQG,SAASb,QAAU,CAEvB,eAAgB,kCAChBc,YAAa5G,OAAO6G,YACpBC,gBAAiB9G,OAAO8G,iBAI5BN,EAAQO,aAAaC,QAAQC,KACzB,SAAAX,GAAU,MAWN,OATAtC,EAAkB2B,WAAWW,IAC7B,UAAAA,EAAOR,eAAP,eAAgBjB,aAAa,IAAIkB,MAAOC,UAQjCM,KAEX,SAAAY,GACIC,QAAQC,IAAIF,GACZG,QAAQC,OAAOJ,MAIvB,IAAIK,GAAY,EAEhB,SAASC,EAAYC,GACbF,IACAA,GAAY,EACZG,qBAAQ,CACJC,WAAW,EACXC,0BAA0B,EAC1BC,QAAS,8FAAgGJ,EAAM,OAC/GK,KAAM,UAEVC,YAAW,WACPR,GAAY,IACb,MAsBXf,EAAQO,aAAaV,SAASY,KAC1B,SAAAZ,GAEOA,EAAS/J,MAAM0H,EAAkBoC,WAAWC,GAE/C,IAAM2B,EAAM3B,EAAS/J,KACrB,GAAK0L,EAAIpH,KAEF,CAIH,GAHgB,UAAZoH,EAAIpH,MACJ4G,EAAY,iBAEC,MAAbQ,EAAIpH,KAAc,CAElB,GAAIqH,SAASC,iBAAiB,wBAAwBpL,OAAS,EAC3D,OAAO,EAEX0K,EAAYQ,EAAIH,SAEpB,OAAOG,EAZP,OAAOA,KAef,SAAAG,GACI,IAAuC,GAAnCA,EAAIN,QAAQO,QAAQ,WACpBZ,EAAY,mBAEZ,GAAIW,EAAI9B,UAAY8B,EAAIN,QAAS,CAC7B,GAAII,SAASC,iBAAiB,wBAAwBpL,OAAS,EAC3D,OAAO,EAEX0K,EAAYW,EAAI9B,SAAS/J,KAAKuL,SAAWM,EAAIN,SAGrD,OAAOR,QAAQC,OAAOa,MAIf3B,QChHT6B,EAAUrI,OAAOsI,sBACLtI,OAAOuI,mBACVvI,OAAOwI,cAKf,SAASC,EAAkBC,GAChC,OAAOC,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwBK,EAAxB,uBACZzC,OAAQ,QAML,SAAS2C,EAAU/C,GACxB,OAAO8C,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwBxC,EAAO6C,WAA/B,UACZzC,OAAQ,QAML,SAAS4C,EAAgBhD,GAC9B,OAAO8C,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwBxC,EAAO6C,YAC3CzC,OAAQ,QAOL,SAAS6C,EAAaJ,GAC3B,OAAOC,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwBK,EAAxB,kBACZzC,OAAQ,SAKL,SAAS8C,EAAgBzM,GAC9B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwB/L,EAAKoM,WAA7B,YACZzC,OAAQ,SAML,SAAS+C,EAAiB1M,GAC/B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwB/L,EAAKoM,WAA7B,sBACZzC,OAAQ,SAML,SAASgD,EAAe3M,GAC7B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwB/L,EAAKoM,WAA7B,sBAAqDpM,EAAK4M,WAA1D,SACZjD,OAAQ,SAOL,SAASkD,EAA0BtD,GACxC,OAAO8C,EAAM,CACXnE,IAAK6D,EAAU,iCACfpC,OAAQ,MACRJ,WAKG,SAASuD,EAAoB9M,EAAMC,GACxC,OAAOoM,EAAM,CACXnE,IAAK6D,EAAU,mBAAH,OAAsB9L,EAAtB,0BACZ0J,OAAQ,OACR3J,KAAMA,IAIH,SAAS+M,EAAyB9M,GACvC,OAAOoM,EAAM,CACXnE,IAAK6D,EAAU,mBAAH,OAAsB9L,EAAtB,0BACZ0J,OAAQ,SAKL,SAASqD,EAAezD,GAC7B,OAAO8C,EAAM,CAEXnE,IAAK6D,EAAU,oCACfpC,OAAQ,MACRJ,WAKG,SAAS0D,EAAY1D,GAC1B,OAAO8C,EAAM,CACXnE,IAAK6D,EAAU,qBAAH,OAAwBxC,EAAOrF,GAA/B,YACZyF,OAAQ,MACRJ,WAKG,SAAS2D,EAAelN,GAC7B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,sBACfpC,OAAQ,OACR3J,SAKG,SAASmN,EAAW5D,GACzB,OAAO8C,EAAM,CACXnE,IAAK6D,EAAU,mBAAH,OAAsBxC,EAAOtJ,SAA7B,WACZ0J,OAAQ,SAML,SAASyD,EAAWpN,EAAMC,GAC/B,OAAOoM,EAAM,CACXnE,IAAK6D,EAAU,mBAAH,OAAsB9L,EAAtB,WACZ0J,OAAQ,OACR3J,SAKG,SAASqN,EAAQrN,GACtB,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,kBACfpC,OAAQ,OACR3J,SAKG,SAASsN,EAAQtN,GACtB,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,eACfpC,OAAQ,OACR3J,SAKG,SAASuN,EAAevN,GAC7B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,kCACfpC,OAAQ,OACR3J,SAKG,SAASwN,EAASjE,EAAQvJ,GAC/B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,mBAAH,OAAsBxC,EAAOrF,GAA7B,SACZyF,OAAQ,OAER3J,SAKG,SAASyN,EAAYzN,GAC1B,OAAOqM,EAAM,CACXnE,IAAK6D,EAAU,oBACfpC,OAAQ,OACR3J,S,gBCxLS0N,EAAM,IAAIC,aC0LvB,GACEvL,KAAM,UACNpC,KAFF,WAGI,IAAJ,kBACoB,KAAV6C,EACF+K,EAAS,IAAIvJ,MAAM,YAEd,oCAAoCwJ,KAAKhL,GAG5C+K,IAFAA,EAAS,IAAIvJ,MAAM,8BAMzB,MAAO,CACLiD,OAAO,EACPF,oBAAoB,EACpBlB,aAAa,EACbiB,eAAe,EACfiF,WAAY,GACZQ,WAAY,GACZkB,SAAU,GACV1H,YAAa,CACXC,iBAAkB,GAClBS,gBAAiB,IAEnBiH,aAAc,CACZ3L,KAAM,GACN4L,KAAM,IAERC,kBAAmB,CACjB7L,KAAM,CACd,CAAU,UAAV,EAAU,QAAV,UAGM0D,eAAgB,KAGpBoI,SAAU,OAAZ,OAAY,CAAZ,GACA,sCAEEC,QAzCF,WA2CIzJ,KAAK0H,WAAa1I,OAAO0K,cAAc,cACvC1J,KAAKoJ,SAAWpK,OAAO0K,cAAc,YACrC1J,KAAK6H,kBACL7H,KAAK4H,aAEP+B,QAhDF,WAkDIX,EAAIY,IAAI,aAAc5J,KAAK6J,gBAE7BC,QAAS,OAAX,OAAW,CAAX,CACID,cADJ,SACA,GACM7J,KAAKkI,WAAa6B,GAGpB,gBALJ,WAKA,gLACA,6BADA,OACA,EADA,OAEA,cACA,WACA,6BACA,gBACA,YACA,cAPA,8CAaI,UAlBJ,WAkBA,8KACA,6BADA,OACA,EADA,OAEA,cACA,kCAHA,8CAQI,cA1BJ,WA0BA,wJACA,sLACA,aADA,oBAGA,6KAEA,uDALA,gCAMA,6BANA,OAMA,EANA,OAOA,cACA,YACA,eACA,mBAEA,gCAZA,uBAeA,uBACA,mBACA,WAjBA,mGADA,8CA+BI7H,eAzDJ,SAyDA,GACM8G,EAAIgB,MAAM,YAAa7H,EAAI3C,IAC3BQ,KAAKwB,aAAc,GAGrB,eA9DJ,WA8DA,wJACA,sLACA,aADA,oBAEA,8KAEA,oFAJA,gCAKA,6BALA,OAKA,EALA,OAMA,cACA,cACA,YACA,eACA,mBAEA,gCAZA,uBAeA,uBACA,mBACA,WAjBA,mGADA,8CA0BI,aAxFJ,WAwFA,8KACA,qDADA,OACA,EADA,OAEA,cACA,cACA,YACA,eACA,oBANA,8CAUIN,WAlGJ,WAkGA,WACMlB,KAAKiK,SAAS,QAApB,gEACQC,kBAAmB,KACnBC,iBAAkB,KAClBrD,KAAM,YACd,iBACQgB,EAAa,EAArB,8BAC0B,KAAZd,EAAIpH,MACN,EAAZ,UACckH,KAAM,UACND,QAAS,eAIvB,uBAEI5F,aAlHJ,WAkHA,WACMwG,EAAkBzH,KAAK0H,YAAY0C,MAAK,SAA9C,GACyB,MAAbpD,EAAIpH,OACN,EAAV,qBACU,EAAV,oBAIIyK,cA1HJ,WA2HMrB,EAAIgB,MAAM,kBAEZhJ,QA7HJ,WA8HMgI,EAAIgB,MAAM,YAEZM,WAhIJ,WAiIMtB,EAAIgB,MAAM,eAEZO,OAnIJ,WAoIMC,MAAM,GACNxB,EAAIgB,MAAM,WAEZS,OAvIJ,WAwIMD,MAAM,GACNxB,EAAIgB,MAAM,WAEZvI,iBA3IJ,cA8IA,6CC7XiV,ICQ7U,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,KACA,OAIa,K,QCnBX,GAAS,WAAa,IAAI1B,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,gBAAgBM,MAAM,CAAE,qBAAsBhB,EAAIc,mBAAoB,CAACV,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,WAAW,CAACE,MAAM,CAAC,cAAc,iBAAiB,KAAO,OAAO,YAAc,WAAW,UAAY,IAAIqK,MAAM,CAACvM,MAAO4B,EAAW,QAAEmJ,SAAS,SAAUyB,GAAM5K,EAAI6K,QAAQD,GAAKE,WAAW,cAAc,GAAG1K,EAAG,MAAM,CAACM,YAAY,aAAaV,EAAI+K,GAAI/K,EAAkB,gBAAE,SAASgL,EAAMC,GAAO,OAAO7K,EAAG,MAAM,CAAC1B,IAAIuM,EAAMvK,YAAY,cAAc,CAACN,EAAG,KAAK,CAACO,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIkL,OAAOD,MAAU,CAACjL,EAAIe,GAAG,IAAIf,EAAI6B,GAAGmJ,EAAM3B,UAAU,KAAKjJ,EAAG,KAAK,CAACM,YAAY,oBAAoBM,MAAM,OAASiK,MAAU7K,EAAG,KAAK,CAACJ,EAAI+K,GAAIC,EAAa,SAAE,SAASrH,GAAM,MAAO,CAACvD,EAAG,aAAa,CAAC1B,IAAIiF,EAAKwH,WAAW7K,MAAM,CAAC,eAAe,eAAe,OAAS,OAAO,aAAa,IAAI,UAAY,QAAQ,WAAY,IAAQ,CAACF,EAAG,MAAM,CAACM,YAAY,mBAAmBJ,MAAM,CAAC,KAAO,WAAWmC,KAAK,WAAW,CAACzC,EAAIe,GAAG,IAAIf,EAAI6B,GAAI,QAAW8B,EAAgB,cAAKvD,EAAG,MAAMJ,EAAIe,GAAG,IAAIf,EAAI6B,GAAG8B,EAAKyH,kBAAqB,QAAWzH,EAAsB,kBAAK,aAAa,OAAOvD,EAAG,KAAK,CAAC1B,IAAIiF,EAAKwH,WAAWxK,GAAG,CAAC,UAAY,SAASC,GAAQ,OAAOZ,EAAIqL,SAASzK,EAAQ+C,MAAS,CAACvD,EAAG,MAAM,CAACM,YAAY,YAAYJ,MAAM,CAAC,IAAMqD,EAAK2H,IAAI,IAAM,MAAMlL,EAAG,OAAO,CAACM,YAAY,cAAc,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,EAAK4H,yBAAwB,QAAO,MAC56C,GAAkB,GCwCtB,I,oBAAA,CACE5N,KAAM,eACNpC,KAFF,WAGI,MAAO,CACLiQ,eAAgB,GAChBX,QAAS,KAGbpB,SAAU,OAAZ,OAAY,CAAZ,CACIgC,eADJ,WACA,WACA,kDACM,MAA4B,KAAxBxL,KAAK4K,QAAQa,OACRnQ,EAEAA,EAAKoQ,QAAO,SAA3B,GACU,IAAV,YACU,GAAIrP,EAAQP,OAAQ,CAClB,IAAZ,wBACc,OAAO4H,EAAKhG,KAAKiG,SAAS,EAAxC,YAEYoH,EAAM1O,QAAUsP,EAElB,OAAOZ,EAAM1O,QAAQP,YAI/B,sCAEE8P,MAAO,CACLJ,eADJ,WACA,WACMxL,KAAK6L,WAAU,WACb,EAAR,sCACU5E,SAASC,iBAAiB,eAAe8D,GAAOc,MAAMC,OAAS,OAC/D9E,SAAS+E,cAAc,QAAjC,mEAKE,QAtCF,WAsCA,qKACA,8CADA,SAEA,uDAFA,OAEA,EAFA,OAGA,cACA,8BACA,+BACA,2BAGA,2BAEA,2CAXA,8CAaElC,QAAS,CACPsB,SADJ,SACA,KACMpC,EAAIgB,MAAM,WAAYiC,EAAOvI,IAG/BuH,OALJ,SAKA,GACUhE,SAASC,iBAAiB,eAAe8D,GAAOkB,aAAe,IACjEjF,SAAS+E,cAAc,QAA/B,8DACQ/E,SAASC,iBAAiB,eAAe8D,GAAOc,MAAMC,OAAS,SAE/D9E,SAASC,iBAAiB,eAAe8D,GAAOc,MAAMC,OAAS,OAC/D9E,SAAS+E,cAAc,QAA/B,gECvGsV,MCQlV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIjM,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,kBAAkB,CAACN,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAUN,EAAIoM,aAAe,OAAS,KAAK,UAAY,MAAM,WAAY,IAAQ,CAAChM,EAAG,KAAK,CAACM,YAAY,eAAeM,MAAMhB,EAAIoM,aAAe,WAAa,sBAAsBzL,GAAG,CAAC,MAAQX,EAAIuK,gBAAgBnK,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAU,KAAK,UAAY,MAAM,WAAY,IAAQ,CAACF,EAAG,KAAK,CAACM,YAAY,0BAA0BC,GAAG,CAAC,MAAQX,EAAIsK,mBAAmBlK,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAU,KAAK,UAAY,MAAM,WAAY,IAAQ,CAACF,EAAG,KAAK,CAACM,YAAY,8BAA8BC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIqM,MAAM,UAAWjM,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAU,KAAK,UAAY,MAAM,WAAY,IAAQ,CAACF,EAAG,KAAK,CAACM,YAAY,+BAA+BC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIqM,OAAO,UAAWjM,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAU,OAAO,UAAY,MAAM,WAAY,IAAQ,CAACF,EAAG,WAAW,CAACkM,WAAW,CAAC,CAAC3O,KAAK,WAAW4O,QAAQ,aAAanO,MAAM,CAAE4B,EAAIwM,WAAY,QAAS,KAAM1B,WAAW,+BAA+BpK,YAAY,cAAcJ,MAAM,CAAC,KAAO,cAAc,MAAQ,cAAc,GAAGF,EAAG,aAAa,CAACM,YAAY,WAAWJ,MAAM,CAAC,OAAS,OAAO,QAAU,QAAQ,UAAY,MAAM,WAAY,IAAQ,CAACF,EAAG,KAAK,CAACM,YAAY,6BAA6BC,GAAG,CAAC,MAAQX,EAAIyM,iBAAiB,IACxkD,GAAkB,GC+BtB,IACE9O,KAAM,gBACNpC,KAFF,WAGI,MAAO,CACL6Q,cAAc,IAGlBxC,QAPF,WASIX,EAAIY,IAAI,mBAAoB5J,KAAKyM,mBAEnC3C,QAAS,CACP2C,iBADJ,SACA,GACMzM,KAAKmM,aAAepC,GAEtBqC,MAJJ,SAIA,GACMpD,EAAIgB,MAAM,QAAS0C,IAErBpC,WAPJ,WAQMtB,EAAIgB,MAAM,eAEZK,cAVJ,WAWMrB,EAAIgB,MAAM,kBAEZwC,WAbJ,WAcMxD,EAAIgB,MAAM,eAEZuC,WAhBJ,WAiBMvD,EAAIgB,MAAM,iBC5DuU,MCQnV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIjK,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,UAAU,CAACM,YAAY,YAAYqL,MAAO/L,EAAIc,iBAAmB,kBAAoB,sBAAuBR,MAAM,CAAC,GAAK,eAAeF,EAAG,cAAc,CAACwM,IAAI,iBAAiBtM,MAAM,CAAC,SAAWN,EAAI6M,oBAAoB,QAAU7M,EAAI8M,mBAAmB,YAAc9M,EAAI+M,aAAapM,GAAG,CAAC,iBAAmBX,EAAIgN,oBAAqBhN,EAAiB,cAAEI,EAAG,YAAY,CAACE,MAAM,CAAC,kBAAiB,EAAK,wBAAuB,EAAM,wBAAuB,EAAM,QAAUN,EAAIiN,cAAc,MAAQjN,EAAIkN,YAAY,eAAe,eAAevM,GAAG,CAAC,iBAAiB,SAASC,GAAQZ,EAAIiN,cAAcrM,KAAU,CAACR,EAAG,MAAM,CAACM,YAAY,eAAeJ,MAAM,CAAC,KAAO,SAASmC,KAAK,SAAS,CAACrC,EAAG,OAAO,CAACM,YAAY,OAAO,CAACV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAImN,kBAAwC,QAArBnN,EAAImN,cAAyB/M,EAAG,OAAO,CAAC0B,YAAY,CAAC,YAAY,SAAS,CAAC1B,EAAG,MAAM,CAAC0B,YAAY,CAAC,SAAW,WAAW,IAAM,MAAM,OAAS,iBAAiBxB,MAAM,CAAC,OAAS,OAAO,IAAM,EAAQ,WAAmCN,EAAIe,GAAG,0BAA0Bf,EAAIuB,OAAOnB,EAAG,SAAS,CAACwM,IAAI,gBAAgBlM,YAAY,eAAeJ,MAAM,CAAC,IAAMN,EAAIoN,aAAa,YAAc,IAAI,OAAS,OAAO,aAAe,IAAI,YAAc,IAAI,MAAQ,QAAQzM,GAAG,CAAC,KAAOX,EAAIqN,sBAA0C,OAAnBrN,EAAIkN,YAAsB9M,EAAG,OAAO,CAACM,YAAY,gBAAgBJ,MAAM,CAAC,KAAO,UAAUmC,KAAK,UAAU,CAACrC,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,QAAQK,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIiN,eAAgB,KAAS,CAACjN,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAACkM,WAAW,CAAC,CAAC3O,KAAK,UAAU4O,QAAQ,oBAAoBe,IAAI,YAAYhN,MAAM,CAAC,SAAWN,EAAIuN,QAAQ,KAAO,WAAW5M,GAAG,CAAC,MAAQX,EAAIwN,iBAAiB,CAACxN,EAAIe,GAAG,QAAQX,EAAG,aAAa,CAAC1B,IAAI,IAAIgC,YAAY,OAAOJ,MAAM,CAAC,eAAe,cAAc,OAAS,QAAQ,WAAY,EAAK,UAAY,cAAc,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,KAAO,WAAWmC,KAAK,WAAW,CAAEzC,EAAIuJ,KAAa,SAAEnJ,EAAG,MAAM,CAACA,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,YAAY,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASX,EAAG,IAAI,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIuJ,KAAKkE,eAAgBzN,EAAIuJ,KAAKmE,MAAM3R,OAAS,EAAGqE,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASf,EAAI+K,GAAI/K,EAAIuJ,KAAU,OAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,GAAG3D,EAAIuB,KAAMvB,EAAIuJ,KAAKoE,OAAO5R,OAAS,EAAGqE,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASf,EAAI+K,GAAI/K,EAAIuJ,KAAW,QAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,GAAG3D,EAAIuB,KAAKnB,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,WAAWf,EAAI+K,GAAI/K,EAAIuJ,KAAY,SAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,OAAOvD,EAAG,IAAI,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAG,gBAAgBX,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,OAAO,CAACwM,IAAI,WAAWlM,YAAY,QAAQ,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,oBAAoB,GAAGf,EAAIuB,KAAyB,OAAnBvB,EAAIkN,YAAsB9M,EAAG,OAAO,CAACM,YAAY,gBAAgBJ,MAAM,CAAC,KAAO,UAAUmC,KAAK,UAAU,CAACrC,EAAG,YAAY,CAACE,MAAM,CAAC,SAAWN,EAAIuN,QAAQ,KAAO,WAAW5M,GAAG,CAAC,MAAQX,EAAIwN,iBAAiB,CAACxN,EAAIe,GAAG,QAAQX,EAAG,aAAa,CAAC1B,IAAI,IAAIgC,YAAY,OAAOJ,MAAM,CAAC,eAAe,cAAc,OAAS,QAAQ,WAAY,EAAK,UAAY,cAAc,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,KAAO,WAAWmC,KAAK,WAAW,CAAEzC,EAAIuJ,KAAa,SAAEnJ,EAAG,MAAM,CAACA,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,YAAY,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASX,EAAG,IAAI,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIuJ,KAAKkE,eAAgBzN,EAAIuJ,KAAKmE,MAAM3R,OAAS,EAAGqE,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASf,EAAI+K,GAAI/K,EAAIuJ,KAAU,OAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,GAAG3D,EAAIuB,KAAMvB,EAAIuJ,KAAKoE,OAAO5R,OAAS,EAAGqE,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,SAASf,EAAI+K,GAAI/K,EAAIuJ,KAAW,QAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,GAAG3D,EAAIuB,KAAKnB,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,WAAWf,EAAI+K,GAAI/K,EAAIuJ,KAAY,SAAE,SAAS5F,EAAKsH,GAAO,OAAO7K,EAAG,IAAI,CAAC1B,IAAIuM,EAAMvK,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG8B,UAAY,OAAOvD,EAAG,IAAI,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAG,gBAAgBX,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,OAAO,CAACwM,IAAI,WAAWlM,YAAY,QAAQ,CAACN,EAAG,KAAK,CAACM,YAAY,gBAAgB,CAACV,EAAIe,GAAG,oBAAoB,GAAGf,EAAIuB,OAAOvB,EAAIuB,MAAM,IACryJ,GAAkB,G,qGCDlB,I,UAAS,WAAa,IAAIvB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,SAAS,CAACN,EAAG,MAAM,CAACM,YAAY,UAAU,CAACN,EAAG,MAAM,CAACM,YAAY,kBAAkBJ,MAAM,CAAC,IAAMN,EAAI4N,UAAUtC,YAAoCuC,IAA5B7N,EAAI4N,UAAUE,WAAuD,IAA5B9N,EAAI4N,UAAUE,UAAiB1N,EAAG,MAAM,CAACM,YAAY,QAAQM,MAAM,CAAEuM,QAAqC,IAA5BvN,EAAI4N,UAAUE,WAAkBxN,MAAM,CAAC,IAAM,UAAS,WAAmE,IAA5BN,EAAI4N,UAAUE,UAAkB,cAA4C,IAA5B9N,EAAI4N,UAAUE,UAAkB,kBAAgD,IAA5B9N,EAAI4N,UAAUE,UAAkB,YAA0C,IAA5B9N,EAAI4N,UAAUE,UAAkB,cAAgB,SAAW,QAAS,IAAM,MAAM9N,EAAIuB,WAAWnB,EAAG,MAAM,CAACM,YAAY,cAAc,CAAkC,aAAhCV,EAAI4N,UAAUG,cAA8B3N,EAAG,aAAa,CAACE,MAAM,CAAC,QAAU,cAAc,UAAY,MAAM,OAAS,UAAU,CAACF,EAAG,IAAI,CAACY,MAAMhB,EAAIgO,YAA6C,eAAhChO,EAAI4N,UAAUG,cAAgC3N,EAAG,WAAW,CAAC0B,YAAY,CAAC,YAAY,QAAQxB,MAAM,CAAC,KAAO,SAAS,MAAQ,UAAUF,EAAG,IAAI,CAACY,MAAMhB,EAAIgO,UAAUhO,EAAIe,GAAG,IAAIf,EAAI6B,GAAG7B,EAAI4N,UAAUjQ,MAAM,MAAM,OACjpC,GAAkB,GCCTsQ,GAAmB,CAC9BC,UAAW,CACTC,MAAO,MACPC,KAAM,UACNhQ,MAAO,aAETiQ,KAAM,CACJF,MAAO,KACPC,KAAM,QACNhQ,MAAO,QAETkQ,UAAW,CACTH,MAAO,KACPC,KAAM,UACNhQ,MAAO,aAETmQ,SAAU,CACRJ,MAAO,MACPC,KAAM,UACNhQ,MAAO,YAEToQ,WAAY,CACVL,MAAO,MACPC,KAAM,SACNhQ,MAAO,eAIEqQ,GAAuB,CAClCC,WAAY,CACVP,MAAO,MACP/P,MAAO,cAETuQ,WAAY,CACVR,MAAO,MACP/P,MAAO,cAET8P,UAAW,CACTC,MAAO,MACP/P,MAAO,aAETwQ,aAAc,CACZT,MAAO,OACP/P,MAAO,gBAETkQ,UAAW,CACTH,MAAO,OACP/P,MAAO,aAETyQ,OAAQ,CACNV,MAAO,KACP/P,MAAO,UAETiQ,KAAM,CACJF,MAAO,OACP/P,MAAO,SC/BX,IACET,KAAM,UACNmR,OAAQ,CAAC,WAAY,WACrBvT,KAHF,WAII,MAAO,CACLqS,UAAW,KAGfnE,SAAU,CACRuE,QADJ,WAEM,IAAK/N,KAAK2N,UAAUG,cAAe,MAAO,GAC1C,IAAN,wCACM,MAAO,WAAb,4BAGEnE,QAfF,WAgBI,IAAJ,OACA,iBACA,kBACImF,EAAKnB,UAAYoB,EAAKC,UAEtBD,EAAKrO,GAAG,eAAe,SAA3B,mBACA,wBACA,gCACMoO,EAAKnB,UAAYsB,EACvB,OAAMC,QAAN,8BAC8B,cAAlBpB,GACFqB,EAAKC,KAAK,uBAAwB,GAClCD,EAAKC,KAAK,uBAAwB,sCAElCD,EAAKC,KAAK,uBAAwB,IAClCD,EAAKC,KAAK,uBAAwB,YAK1CtF,QAAS,IC9DoU,MCQ3U,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAI/J,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAC2L,MAAM,CAAGuD,KAAMtP,EAAIuP,SAASC,GAAKxP,EAAIc,iBAAmB,EAAI,KAAO,KAAM2O,IAAKzP,EAAIuP,SAASG,EAAI,KAAMC,QAAS3P,EAAI4P,QAAU,QAAU,QAAUtP,MAAM,CAAC,GAAK,iBAAiB,CAACF,EAAG,KAAK,CAACA,EAAG,KAAK,CAACM,YAAY,YAAYC,GAAG,CAAC,MAAQX,EAAIuF,SAAS,CAACnF,EAAG,KAAK,CAACM,YAAY,oBAAoBV,EAAIe,GAAG,UAAUX,EAAG,KAAK,CAACM,YAAY,YAAYC,GAAG,CAAC,MAAQX,EAAI0I,aAAa,CAACtI,EAAG,KAAK,CAACM,YAAY,mBAAmBV,EAAIe,GAAG,UAAUX,EAAG,KAAK,CAACM,YAAY,YAAYC,GAAG,CAAC,MAAQX,EAAI6P,OAAO,CAACzP,EAAG,KAAK,CAACM,YAAY,0BAA0BV,EAAIe,GAAG,UAAUX,EAAG,KAAK,CAACM,YAAY,YAAYC,GAAG,CAAC,MAAQX,EAAI8P,eAAe,CAAC1P,EAAG,KAAK,CAACM,YAAY,yBAAyBV,EAAIe,GAAG,WAAWX,EAAG,KAAK,CAACM,YAAY,YAAYC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAI+P,mBAAmB,OAAO,CAAC3P,EAAG,KAAK,CAACM,YAAY,yBAAyBV,EAAIe,GAAG,iBAC75B,GAAkB,GC2BtB,IACEpD,KAAM,cACNqS,MAAO,CACLT,SAAU,CAERU,UAAU,EACVlJ,KAAM,KACNmJ,QAAS,WAAf,kBAEIN,QAAS,CAEPK,UAAU,EACVlJ,KAAMoJ,QACND,SAAS,GAEXnD,YAAa,CAEXkD,UAAU,EACVlJ,KAAM,OAGV0C,SAAU,OAAZ,OAAY,CAAZ,GACA,sCAEEM,QAAS,CAEPxE,OAFJ,WAGMtF,KAAKgK,MAAM,mBAAoB,WAGjC,WANJ,WAMA,+JACA,4BADA,SAEA,8CACA,uBACA,sBACA,iBACA,KAJA,wCAIA,6GACA,GACA,2BAFA,SAIA,KAJA,OAIA,EAJA,OAKA,YACA,yCAEA,0CARA,4CAUA,sBAhBA,8CAqBI6F,aA3BJ,WA2BA,WACM7P,KAAKgK,MAAM,oBACXhK,KAAKmQ,QAAQ,QAAS,OAAQ,CAC5BjG,kBAAmB,KACnBC,iBAAkB,KAClBiG,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAc,qCACdC,kBAAmB,kBACnBC,WAAYxQ,KAAK8M,YAAYzM,MAAM6N,MAAMuC,OAEjD,KATA,yDASA,0HACA,GAEA,QAHA,SAKA,sBALA,OAKA,EALA,OAMA,YAEA,mBAEA,0CAVA,2CATA,uDAsBA,uBAMI,KAzDJ,WAyDA,+KACA,KACA,GACA,qBAHA,EAKA,2BALA,EAKA,IALA,EAKA,EACA,GACA,IACA,OARA,SAUA,OAVA,OAWA,0CAXA,8CAeI,mBAxEJ,SAwEA,iLAKA,uBALA,UAKA,EALA,OAMA,YANA,oBAOA,kDACA,EARA,iDASA,YACA,aACA,eACA,iBAZA,wBAeA,0CAfA,iDC5HqV,MCQjV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCjBTpJ,GAAUrI,OAAOsI,sBAGhB,SAASoJ,GAAahJ,GAC3B,OAAOC,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBK,EAAxB,mBACZzC,OAAQ,QAKL,SAAS0L,GAAkBC,GAChC,OAAOjJ,EAAM,CACXnE,IAAK6D,GAAU,iBAAH,OAAoBuJ,EAApB,iBACZ3L,OAAQ,QAKL,SAAS4L,GAAoBD,GAClC,OAAOjJ,EAAM,CACXnE,IAAK6D,GAAU,iBAAH,OAAoBuJ,EAApB,gBACZ3L,OAAQ,Q,8BCfG,IACb3J,KADa,WAEX,MAAO,CACLwV,cAAc,EACdC,iBAAiB,EACjBC,MAAO,KACPC,qBAAqB,IAGzBxH,QATa,WASH,WACRzJ,KAAKkR,SACLlR,KAAKgR,MAAQG,2BAAY,WACvB,EAAKD,WACJ,MAELvH,QAfa,WAeH,WACRX,EAAIY,IAAI,uBAAuB,WAC7B,EAAKkH,cAAe,EACpB,EAAKI,aAGTtF,MAAO,CACL,4BADK,SACuB7B,GACtBA,GAAK/J,KAAKkR,WAGlBpH,QAAS,CAEDoH,OAFC,WAEQ,gLACP,EAAKE,YADE,UAEPR,EAAS,EAAKS,OAAOC,MAAMV,OAC5BA,EAHQ,iDAIPW,EAAe,EAAKF,OAAOC,MAAMC,eAClC,EAAKT,cAAiB,EAAKA,cAAgB,EAAKC,mBACnD,EAAKS,kBAAkBZ,IACnBW,GAAgB,EAAKN,sBAAqB,EAAKQ,eAAeb,IAPvD,8CAWTQ,UAbC,WAaW,oKACZpS,OAAO0K,cAAc,cADT,gCAEWgH,GACvB1R,OAAO0K,cAAc,eAHT,gBAEN/M,EAFM,EAENA,OAGR,EAAKoU,gBAAkB,EAAKM,OAAOC,MAAMV,SAAWjU,EAChDA,GAAQ,EAAK0U,OAAOK,OAAO,YAAa/U,GAN9B,8CAUZ6U,kBAvBC,SAuBiBZ,GAAQ,gLACLC,GAAoBD,GADf,gBACtBjU,EADsB,EACtBA,OACR,EAAK0U,OAAOK,OAAO,mBAAoB/U,GACvC,EAAKmU,cAI+B,IAJhB,CAClBtC,GAAqBG,aAAaxQ,MAClCqQ,GAAqBH,UAAUlQ,MAC/BqQ,GAAqBJ,KAAKjQ,OAC1BiJ,QAAQzK,EAAOmU,cAPa,8CAU1BW,eAjCC,SAiCcb,GAAQ,uKAC3B,EAAKK,qBAAsB,EADA,SAEFN,GAAkBC,GAFhB,gBAEnBjU,EAFmB,EAEnBA,OACRA,EAAOgV,mBAAmBC,SAAQ,SAAClO,GACjCA,EAAKmO,KAAOC,kBACZpO,EAAKqO,MAAL,UAAgBrO,EAAKsO,eAArB,cAAyCtO,EAAKgJ,MAA9C,cAAyDhJ,EAAK+C,IAA9D,cAAuE/C,EAAKuO,WAAa,OAG3F,EAAKZ,OAAOK,OAAO,iBAAkB/U,EAAOgV,oBARjB,+CAW/BO,cAtEa,WAuEXC,cAAcnS,KAAKgR,OACnBhI,EAAIoJ,KAAK,yB,aC9EE,IACbtI,QAAS,CAEPuI,kBAFO,SAEWC,EAAOpD,GACvBlP,KAAKuS,eAAgB,EACrB,IAAMC,EAAc,IAAIC,QAAY,CAClC3L,KAAM,QACN4L,QAAS,KACTC,QAAS,GACTC,QAAS,KAELlI,EAAQ8H,EAAYK,OAAO,CAC/BP,QACApD,UAEFlP,KAAK8S,MAAMC,SAASrI,GAGpB,IAAMsI,EAAuBtI,EAAM4H,MAAMjT,KAAI,SAAAqE,GAC3C,MAAO,CACLlE,GAAIkE,EAAKlE,GACT+P,EAAG7L,EAAK6L,EACRE,EAAG/L,EAAK+L,MAGZzP,KAAKiT,oBAAoBD,IAIrBC,oBA5BC,SA4BmBC,GAAgB,8KACtBrK,EAAeqK,GADO,OAClClM,EADkC,OAExB,KAAZA,EAAIpH,MACN,EAAK2I,cAHiC,8CAQ1C4K,oBApCO,WAqCL,IAAMC,EAAQpT,KAAK8S,MAAMO,WACzB,GAAKD,GAA0B,IAAjBA,EAAMtX,OAApB,CACA,MAAkDkE,KAAKqR,OAAOC,MAAMgC,cAA5DC,EAAR,EAAQA,wBAAyBzC,EAAjC,EAAiCA,aACjCsC,EAAMxB,SAAQ,SAAClO,GACb,GAAKA,EAAKpI,KAAV,CACA,IAAMkY,EAAUD,EAAwB7H,QAAO,SAAA+H,GAAO,OAAIA,EAAQlY,WAAamI,EAAKlE,MAAI,GACpFkU,EAAehQ,EAAKpI,KAAKwS,cACzB0F,EACFE,EAAgC,aAAjBA,EAA8B,WAAaF,EAAQ1C,aAG9D,CAAC,aAAc,aAAc,aAAanN,SAASmN,KAAe4C,EAAe,cAEvFhQ,EAAKiQ,KAAK,OAAV,iCAAuBjQ,EAAKpI,MAA5B,IAAkCwS,cAAe4F,WAMrDE,gBAxDO,SAwDStY,GACd,GAAKA,KAAQA,EAAKQ,QAAU,GAA5B,CACA,IAAM+X,EAAa7T,KAAK8T,SAASD,WACjCvY,EAAKsW,SAAQ,SAAClO,EAAMsH,GACd6I,EAAW7I,KACbtH,EAAKlE,GAAK2F,KAAKnB,MAAM6P,EAAW7I,IAAQ,GAAGxL,WCuCrD,eAQA,IACE9B,KAAM,cACNqW,OAAQ,CAACC,GAAMlB,IACfmB,WAAY,CACVC,YAAJ,IAEE5Y,KANF,WA2BI,OApBA,GAAJ,uBACA,kBACA,cACM,IAAN,IACA,oBACA,oBAEA,GAAQ,EAAR,QAAQ,EAAR,KACA,GAAQ,EAAR,QAAQ,EAAR,KAEM,OAAN,8BACA,IADA,YACA,IADA,yBAEA,MAFA,YAEA,IAFA,yBAGA,IAHA,YAGA,IAHA,YAGA,IAHA,YAGA,IAHA,YAGA,MAHA,YAGA,IAHA,yBAIA,IAJA,YAIA,IAJA,oBAQA,GAEW,CACL6Y,eAAgB,GAChBhI,cAAc,EACdiI,QAAS,GACTC,SAAU,GACVrH,eAAe,EACfC,YAAa,MACbC,cAAe,GACfC,aAAc,GACdG,SAAS,EACThE,KAAM,CACJkE,SAAU,GACVC,MAAO,GACPC,OAAQ,GACR4G,QAAS,IAEXC,UAAU,EACVC,KAAM,KACNC,UAAW,KACXC,WAAY,GACZ5B,MAAO,KACP6B,IAAK,KACLC,YAAa,KACbhI,oBAAqB,CACnB2C,EAAG,EACHE,EAAG,GAEL5C,oBAAoB,EACpBiH,SAAU,GACVe,cAAc,EACdC,QAAS,GACTC,SAAU,GACVC,eAAgB,GAChBtN,WAAY,mCAEZoF,YAAa,GAEbyF,eAAe,IAGnB/I,SAAU,OAAZ,OAAY,CAAZ,GACA,sCAEEoC,MAAO,CACL,iBADJ,SACA,GACU7B,EACF/J,KAAK8S,MAAMmC,OAAOjW,OAAOkW,YAEzBlV,KAAK8S,MAAMmC,OAAOjW,OAAOkW,WAAa,MAG1C,6BARJ,WASMlV,KAAKmT,wBAGT1J,QAlFF,WAmFIzJ,KAAK0H,WAAa1I,OAAO0K,cAAc,eAGzCC,QAtFF,WAsFA,WACI3K,OAAOmW,iBAAiB,UAAU,WAC3B,EAAX,YAGQ,EAAR,gBAFQ,EAAR,gBAIMnM,EAAIgB,MAAM,mBAAoB,EAApC,iBAEIhL,OAAOoW,UAAY,SAAvB,GAIU1V,EAAEpE,MAAuB,iBAAfoE,EAAEpE,KAAKoC,OACC,WAAhBgC,EAAEpE,KAAKwL,KACT,EAAV,YAEU,EAAV,YAGQ,EAAR,qBAyBgC,eAApBpH,EAAEpE,KAAKkF,YACT,EAAV,wBAKyB,YAAfd,EAAEpE,KAAKoC,MACTsB,OAAOqW,OAAOC,YACtB,CACU,KAAV,SACU,KAAV,GACU,UAAV,IAEA,KAI0B,SAAhB5V,EAAEpE,KAAKoC,OAET,EAAR,mBAKIsL,EAAIY,IAAI,iBAAkB5J,KAAKuL,gBAC/BvC,EAAIY,IAAI,WAAY5J,KAAKoL,UACzBpC,EAAIY,IAAI,gBAAiB5J,KAAKqK,eAC9BrB,EAAIY,IAAI,QAAS5J,KAAKoM,OACtBpD,EAAIY,IAAI,aAAc5J,KAAKwM,YAC3BxD,EAAIY,IAAI,aAAc5J,KAAKuM,YAC3BvD,EAAIY,IAAI,UAAW5J,KAAKgB,SACxBgI,EAAIY,IAAI,aAAc5J,KAAKsK,YAE3BtB,EAAIY,IAAI,SAAU5J,KAAKuK,QACvBvB,EAAIY,IAAI,SAAU5J,KAAKyK,QAEvBzB,EAAIY,IAAI,YAAa5J,KAAKuV,WAC1BvW,OAAOmW,iBAAiB,SAAUnV,KAAKwV,UAAU,GACjDxV,KAAK6L,WAAU,WACb,EAAN,aACM,EAAN,gBAKE/B,QAAS,OAAX,OAAW,CAAX,CACIyB,eADJ,SACA,GACMvM,OAAOyW,cAAgB,GAEvBzV,KAAKoU,QAAU9Y,EACfA,EAAKsW,SAAQ,SAAUlO,GAQrBA,EAAKrH,QAAQuV,SAAQ,SAA7B,GACU5S,OAAOyW,cAAcrZ,KAAKsZ,SAG9B1V,KAAKuI,eAEPoN,WAnBJ,WAmBA,WACA,OACA,uCACM3V,KAAKyU,UAAYA,EACjBzU,KAAK8S,MAAQ,IAAI,GAAvB,MAEQ2B,UAAWA,EACXmB,MAAO5W,OAAOkW,WAAa,IAC3BnJ,OAAQ/M,OAAO6W,YAAc,GAC7BC,WAAY,CACVC,MAAO,QAETC,SAAS,EAETC,QAAS,CACPC,SAAS,EACTC,cAAc,GAEhBC,UAAU,EACVC,UAAW,CACTH,SAAS,GAOXI,aAAc,CACZC,gBAAiB,CACf7Y,KAAM,SACN8Y,KAAM,CACJnW,MAAO,CACLoW,KAAM,OACNC,OAAQ,aAIdC,eAAgB,CACdjZ,KAAM,SACN8Y,KAAM,CACJnW,MAAO,CACLoW,KAAM,OACNC,OAAQ,cAKhBE,WAAY,CACVC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZ3B,WAAW,EACX4B,UAAW,iBACXC,aAAc,QACdC,aAAc,OACdC,gBAAiB,SAyBjBC,mBApCV,YAoCA,qBACY,MAAgD,OAA5CC,EAAaC,aAAa,eAOhCC,aA5CV,YA4CA,aACA,KACA,gBACA,yBAGA,iDAEY,GAAuB,IAAnBC,EAGF,IAFA,IAAd,qBACA,IACA,oBACgB,IAAhB,mBAIgB,GAHIC,IAAapY,GACfqY,IAEEA,EAAM,EAMR,OALA/I,EAAKgJ,SAAS,CACZjR,QAAS,KAA7B,qBACoBC,KAAM,YAERiR,GAAqB,EACdA,EAIb5I,EAAKC,KAAK,uBAAwB,GAClC,IAAZ,GACc4I,KAAM7I,EAAK8I,OAAOC,KAClBC,GAAIhJ,EAAKiJ,OAAOF,MAclB,OAXAtP,EAAQ/D,GAAQuF,MAAK,SAAjC,GACc,GAAgB,KAAZpD,EAAIpH,KAAa,CACnB,IAAIyY,EAAavJ,EAAKgE,MAAMwF,eAAenJ,EAAK8I,OAAOC,MACvDG,EAAWE,YAAY,sBAIvBzJ,EAAKvG,iBAIFwP,GAITS,KAAM,CACJC,OAAQ,IAEVC,WA9FV,WA+FY,OAAO,IAAI,GAAvB,WACcC,OAAQ,CACNjb,KAAM,YACN8Y,KAAM,CACJoC,KAAM,KAGVC,OAAQ,SACRvB,gBAAiB,SACjBJ,YAAY,EACZsB,KAAM,CACJC,OAAQ,IAEVpY,MAAO,CACLyY,KAAM,CACJpC,OAAQ,UACRqC,YAAa,IACbC,gBAAiB,EACjBC,aAAc,CACZvb,KAAM,UACNkY,MAAO,EACP7J,OAAQ,SAOpBmN,KAAM,CACJC,KAAM,GACNxJ,SAAS,EACT7I,KAAM,QAERsS,WAAY,CACVlD,SAAS,EAETmD,OAAQ,IACRC,SAAU,IACVC,SAAU,MAIdvZ,KAAK8S,MAAM0G,KAAK,OAEhB,GAAN,2BACQC,QAAS,YACTlK,EAAG,IACHE,EAAG,IACHmG,MAAO,GACP7J,OAAQ,GACRmC,MAAO,MACP1N,UAAW,CACTkZ,SAAU,eACVzF,WAAY,CACV0F,QAAZ,OAKM3Z,KAAK8S,MAAMpS,GAAG,mBAAmB,WAC/B,EAAR,mCAEMV,KAAK8S,MAAMpS,GAAG,eAAe,WAC3B,EAAR,yBAEMV,KAAK8S,MAAMpS,GAAG,cAAc,WAC1B,EAAR,yBAEMV,KAAK8S,MAAMpS,GAAG,iBAAiB,WAC7B,EAAR,yBAEMV,KAAK8S,MAAMpS,GAAG,SAAS,WACrB,EAAR,yBAEMV,KAAK8S,MAAMpS,GAAG,UAAU,WACtB,EAAR,yBAEMV,KAAK8S,MAAMpS,GAAG,aAAa,WACzB,EAAR,yBAGMV,KAAK8S,MAAMpS,GAAG,oBAAoB,SAAxC,sBACY,EAAZ,gBACU,EAAV,4CASQ,EAAR,cACQ,IAAR,6CACQ,EAAR,sBACQ,EAAR,yBAGMV,KAAK8S,MAAMpS,GAAG,gBAApB,uKACA,qBADA,kGAGMV,KAAK8S,MAAMpS,GAAG,iBAAiB,SAArC,gBACQ,EAAR,oBACQ,EAAR,iBAIMV,KAAK8S,MAAMpS,GAAG,aAApB,iLACA,eADA,EACA,IADA,EACA,EACA,IACA,QACA,IACA,MAEA,yBAPA,kGAaMV,KAAK8S,MAAMpS,GAAG,mBAAmB,WAG/B,IAAR,sCACQ,EAAR,mBAEMV,KAAK8S,MAAMpS,GAAG,mBAAmB,WAE/B,IAAR,sCACQ,EAAR,oBAGIkZ,UArSJ,SAqSA,KACM,IAAK,IAAX,wBACQC,EAAMje,GAAGkQ,MAAMgO,WAAaC,EAAO,UAAY,UAGnDxE,UA1SJ,SA0SA,GACUvV,KAAKmU,gBACPnU,KAAKmU,eAAeoE,YAAY,iBAElC,IAAIyB,EAAaha,KAAK8S,MAAMwF,eAAe9Y,GAC3CQ,KAAKmU,eAAiB6F,EAEtBA,EAAWC,SAAS,iBAMpBja,KAAK8S,MAAMoH,OAAO,MAClBla,KAAK8S,MAAMqH,WAAWH,IAExBI,UA1TJ,WA0TA,WACA,OACMpa,KAAK2U,IAAM,IAAI0F,GAAI,CACjBjC,OAAQpY,KAAK8S,MACbwH,QAAQ,EACRC,WAAW,EAGXC,aANR,SAMA,GACU,IAAV,2BAEA,EAWA,YATA,EAFA,EAEA,KACA,EAHA,EAGA,WAHA,IAIA,oBAJA,MAIA,GAJA,EAKA,EALA,EAKA,KACA,EANA,EAMA,WACA,EAPA,EAOA,eACA,EARA,EAQA,UACA,EATA,EASA,QACA,EAVA,EAUA,OAEA,GAEY5a,KAAZ,EACYsL,WAAZ,EACYuP,aAAZ,EACYC,WAAZ,EACYC,WAAZ,EACYhD,eAAZ,EACYiD,UAAZ,EACYC,QAAZ,EACYC,OAAZ,EACYpT,WAAYoH,EAAKpH,WACjB6H,EAAZ,EACYE,EAAZ,GAEA,GACY7P,KAAZ,EACYsL,WAAZ,EACYwP,WAAZ,EACYhT,WAAYoH,EAAKpH,WACjB6H,EAAZ,EACYE,EAAZ,GAGU,OADAV,EAAKzT,KAAOA,EACL,IAAI+K,SAAQ,SAA7B,KACYsC,EAAQ9D,GAAQuF,MAAK,SAAjC,GACA,QAA8B,KAAZpD,EAAIpH,MACNmP,EAAKvP,GAArB,4CACgBuP,EAAKzT,KAAKkE,GAA1B,4CACgBuP,EAAKzT,KAAKwS,cAAgB,WAC1BhO,GAAQ,KAERgP,EAAKvG,cACLjC,GAAO,WAKfyU,YAAa,SAArB,GACU,IAAV,SACU,OAAO,EAAjB,kBACYnF,MAAO,GACP7J,OAAQ,GACRiP,MAAO,OACP1f,KAAZ,EACY2f,KAAM,WACJ,IAAd,gCAMc,OALAC,EAAKC,UAAY,iBACjBD,EAAKE,UAAY,iDAA/B,OACA,MADA,0DAEA,OAFA,6BAIqBF,MAKbG,YAAa,SAArB,GAKU,IAAV,8BACU,OAAO,EAAjB,kBACYzF,MAAO,GACP7J,OAAQ,GAURiP,MAAO,QACPzL,EAAZ,EACYE,EAAZ,EACYnU,KAAMyT,EAAKzT,KACXue,MAAO,CACLyB,OAAQ,CAENC,GAAI,CAKFjM,SAAU,OACVjP,MAAO,CACLmb,OAAQ,CACNxd,EAAG,EAEHyd,OAAQ,UACR/E,OAAQ,UACRqC,YAAa,EACbtC,KAAM,QAKRqC,KAAM,CACJpC,OAAQ,aAKdgF,IAAK,CACHpM,SAAU,QACVpB,MAAO,CACLoB,SAAU,UAEZjP,MAAO,CAELmb,OAAQ,CACNxd,EAAG,EACHyd,QAAQ,EACR/E,OAAQ,UACRqC,YAAa,EACbtC,KAAM,QAERqC,KAAM,CACJpC,OAAQ,YAGZiF,OAAQ,IAGZ5Q,MAAO,EAArB,2BAMIwB,WAtdJ,WAudMvM,KAAKuS,eAAgB,EACrBvS,KAAKuI,eAIPwE,iBA5dJ,SA4dA,GAKM,GAJA/M,KAAK6M,oBAAqB,EAId,eAARpO,EAAsB,CAAhC,MACA,sDACYmd,GACF5b,KAAK8S,MAAM+I,WAAWD,GAGd,gBAARnd,GACFuB,KAAKuI,cAEK,WAAR9J,GACFuB,KAAK8b,gBAAgB9b,KAAK8M,aAGhB,uBAARrO,GACFuB,KAAKuI,eAKT,YApfJ,WAofA,wLACA,GACA,kBAFA,OACA,EADA,OAIA,gBACA,WADA,EACA,UADA,EACA,WADA,EACA,eACA,YAEA,KACA,+BACA,+BACA,8CAGA,+BACA,2BACA,kCAEA,6BACA,qCACA,2BACA,uBACA,qBACA,eACA,+CACA,UAIA,aACA,mBACA,+BAEA,gBAjCA,8CAsCIwT,WA1hBJ,WA0hBA,WACA,KACM/b,KAAK8U,QAAQlD,SAAQ,SAA3B,GACQ,IAAR,GACUoJ,MAAO,QACPxb,GAAIkE,EAAKlE,GACT+P,EAAG7L,EAAK6L,EACRE,EAAG/L,EAAK+L,EACRmG,MAAO,GACP7J,OAAQ,GAURzQ,KAAMoI,EACNmW,MAAO,CACLyB,OAAQ,CAENC,GAAI,CAKFjM,SAAU,OACVjP,MAAO,CACLmb,OAAQ,CACNxd,EAAG,EAEHyd,OAAQ,UACR/E,OAAQ,UACRqC,YAAa,EACbtC,KAAM,OACN3K,MAAO,CACLgO,WAAY,WAGhBhB,KAAM,CACJpC,OAAQ,aAKdgF,IAAK,CACHpM,SAAU,QACVpB,MAAO,CACLoB,SAAU,UAEZjP,MAAO,CAELmb,OAAQ,CACNxd,EAAG,EACHyd,QAAQ,EACR/E,OAAQ,UACRqC,YAAa,EACbtC,KAAM,OACN3K,MAAO,CACLgO,WAAY,WAGhBhB,KAAM,CACJpC,OAAQ,YAGZiF,OAAQ,IAGZ5Q,MAAO,EAAnB,iBAGQuH,EAAMlW,KAAK2S,MAGb,IAAN,KAyCM,GAxCA/O,KAAK+U,SAASnD,SAAQ,SAA5B,GACQ,IAAR,GACUqG,OAAQ,CACNC,KAAMxU,EAAKsU,KACXgE,KAAM,WAER5D,OAAQ,CACNF,KAAMxU,EAAKyU,GACX6D,KAAM,UAGRrD,OAAQ,CACNjb,KAAM,YACN8Y,KAAM,CACJoC,KAAM,KAGVC,OAAQ,SACRvB,gBAAiB,SACjBJ,YAAY,EACZsB,KAAM,CACJC,OAAQ,IAEVpY,MAAO,CACLyY,KAAM,CACJpC,OAAQ,UACRqC,YAAa,IACbC,gBAAiB,EACjBC,aAAc,CACZvb,KAAM,UACNkY,MAAO,EACP7J,OAAQ,KAId4P,OAAQ,GAEVzM,EAAM9S,KAAK+S,MAGTnP,KAAKuS,cAAe,OAAOvS,KAAKqS,kBAAkBC,EAAOpD,GAE7DlP,KAAK8S,MAAMC,SAAS,CAClBT,MAAR,EACQpD,MAAR,KAKI,WAzpBJ,YAypBA,iMACA,GACA,UACA,aAHA,SAKA,KALA,OAKA,EALA,OAMA,aACA,gBAPA,8CAWI9D,SApqBJ,SAoqBA,KAEM,IAAN,yBACQ9P,KAAR,IAEM0E,KAAK2U,IAAIsH,MAAMlN,EAAMmN,IAIvBC,YA7qBJ,SA6qBA,GACM,IAAN,KA+BM,OA7BIC,EAAcxB,UAEhByB,EAAWjgB,KAAK,CACdoD,GAAI,UACJ8c,MAAO,MACPX,OAAQ,KAElB,UAEQU,EAAWjgB,KAAK,CACdoD,GAAI,SACJ8c,MAAO,KACPX,OAAQ,KAIVU,EAAWjgB,KACnB,CACU,GAAV,SACU,MAAV,KACU,OAAV,IAEA,CACU,GAAV,UACU,MAAV,MACU,OAAV,KAIaigB,GAGThS,cAhtBJ,WAitBMrK,KAAK8S,MAAMzI,iBAIb+B,MArtBJ,SAqtBA,GACM,IAAN,oBAC+B,IAArBmQ,KAAKC,KAAK9P,IAAgB8M,EAAO,MACX,IAAtB+C,KAAKC,KAAK9P,IAAiB8M,EAAO,IACtCxZ,KAAK8S,MAAM0G,KAAK9M,IAIlBF,WA7tBJ,WA8tBMxM,KAAKuU,UAAYvU,KAAKuU,SACtBvU,KAAKuU,SAAWvU,KAAK8S,MAAMyB,WAAavU,KAAK8S,MAAM2J,YAIrDzb,QAnuBJ,WAouBMhB,KAAKuI,eAGPmU,UAvuBJ,WAwuBM,IAAIC,EAAS1V,SAAS2V,YAAc3V,SAAS4V,oBAAsB5V,SAAS6V,oBAG5E,YADelP,IAAX+O,IAAV,MACaA,GAGTrS,WA9uBJ,WA+uBM,IAAN,2BACUtK,KAAKmM,aACPlF,SAAS8V,iBAETC,EAAOC,qBAMXxS,OAzvBJ,WA0vBMzK,KAAK8S,MAAMoK,QAIb3S,OA9vBJ,WA+vBMvK,KAAK8S,MAAMqK,QAGb3H,SAlwBJ,WAkwBA,WACUxV,KAAK4U,cACT5U,KAAK4U,YAAc7N,YAAW,WAC5B,EAAR,aACA,kBACA,iCAEQqW,aAAa,EAArB,aACQ,EAAR,mBACA,OAGIhQ,mBA9wBJ,SA8wBA,GACMpN,KAAKsN,SAAU,EACf,IAAI+P,EAAY,GAEdA,EADErd,KAAKmN,aAAa/F,QAAQ,SAAW,GAAKpH,KAAKmN,aAAa/F,QAAQ,UAAY,EACtEpH,KAAKmN,aAELnO,OAAOse,SAASC,OAASvd,KAAKmN,aAE5ClB,EAAMmM,OAAOoF,cAAclI,YACjC,CACQ,KAAR,gBACQ,KAAR,CACU,GAAV,iBACU,OAAV,YACU,MAAV,mBACU,OAAV,cACU,SAAV,cACU,cAAV,oBAEQ,QAAR,GAEA,IAIImI,aAvyBJ,SAuyBA,GAEMtX,QAAQC,IAAIjB,KAAKnB,MAAMyC,EAAInL,MAAO,WAGlC0E,KAAKgN,eAAgB,EACrBhN,KAAKsN,SAAU,EACftN,KAAK6U,cAAe,EACpB7U,KAAK0d,WAAWvY,KAAKnB,MAAMyC,EAAInL,MAAOmL,EAAIjH,KAG5C,WAlzBJ,SAkzBA,0KAEA,qBAFA,SAIA,OAJA,OAIA,EAJA,OAKA,eACA,YACA,aACA,eACA,iBAEA,iBAXA,8CAsCI+N,eAx1BJ,WA21BM,IAAI8P,EAAY,GAEdA,EADErd,KAAKmN,aAAa/F,QAAQ,SAAW,GAAKpH,KAAKmN,aAAa/F,QAAQ,UAAY,EACtEpH,KAAKmN,aAELnO,OAAOse,SAASC,OAASvd,KAAKmN,aAE5CnN,KAAK2d,MAAMC,cAAcJ,cAAclI,YAC7C,CACQ,KAAR,aACQ,KAAR,GACQ,QAAR,GAEA,IAGIuI,QA12BJ,SA02BA,KAEM,GAAIC,IAAMC,EAER,OAAa,IAAND,GAAW,EAAIA,IAAM,EAAIC,EAGlC,GAAS,MAALD,GAAkB,MAALC,EACf,OAAOD,IAAMC,EAGf,IAAIC,EAAa1Z,SAASpI,KAAK4hB,GACrC,mBAEM,GAAIE,IAAeC,EACjB,OAAO,EAGT,OAAQD,GACN,IAAK,kBACL,IAAK,kBAEH,MAAO,GAAKF,IAAM,GAAKC,EACzB,IAAK,kBAEH,OAAKD,KAAOA,GACFC,KAAOA,EAGH,KAAND,EAAU,GAAKA,IAAM,EAAIC,GAAKD,KAAOC,EAC/C,IAAK,gBACL,IAAK,mBACH,OAAQD,KAAOC,EAGnB,GAAkB,mBAAdC,EAAiC,CAEnC,IAAIE,EAASniB,OAAOoiB,oBAAoBL,GAChD,gCACQ,GAAII,EAAOpiB,QAAUsiB,EAAOtiB,OAC1B,OAAO,EAET,IAAK,IAAIF,EAAI,EAAGA,EAAIsiB,EAAOpiB,OAAQF,IAAK,CACtC,IAAIyiB,EAAWH,EAAOtiB,GAEtB,GAAiB,SAAbyiB,GACF,GAAIP,EAAEO,GAAUC,gBAAkBP,EAAEM,GAAUC,cAC5C,OAAO,OAErB,8BACY,OAAO,EAGX,OAAO,EAGT,MAAkB,kBAAdN,EACEF,EAAExZ,YAAcyZ,EAAEzZ,gBADxB,GAQF,gBA16BJ,SA06BA,wLACA,oCACA,+BACA,SAGA,yBACA,KACA,GACA,cACA,eAEA,KAEA,YAdA,oBAiBA,+BACA,EAlBA,gLAoBA,KACA,KACA,KACA,uBACA,yBAEA,mBA1BA,SA2BA,4BA3BA,UA2BA,EA3BA,OA4BA,8BACA,iBACA,4BAEA,uBACA,oCACA,kCACA,uBACA,gBAEA,iCAEA,qCACA,0CAKA,sDACA,gBACA,qCACA,iEAEA,6BACA,8FACA,KACA,cACA,qDACA,6GACA,8GAKA,qBAEA,CAKA,IAJA,sBACA,wGAEA,KACA,mBACA,wBAEA,gCACA,uBACA,sDACA,8GACA,6GACA,CAUA,KACA,OAKA,EACA,gBAEA,+DA/CA,GAmDA,uBApGA,wBAqGA,gCArGA,sBAsGA,IAtGA,QAyGA,KAzGA,4LA6GA,8BA7GA,0DAiHA,KAjHA,UAkHA,cAlHA,QAkHA,EAlHA,OAmHA,gBACA,qCApHA,WAuHA,EAvHA,2CAwHA,GAxHA,QA0HA,aACA,oBAEA,wBACA,4BACA,+BAEA,qBAEA,eACA,cAGA,WAEA,yBAzIA,+CAgSIia,MA1sCJ,WA2sCMve,KAAK2d,MAAMa,SAASC,SAGtBC,QA9sCJ,SA8sCA,OAEM1e,KAAKmN,aACX,EACA,kBACA,mBACA,cACA,iBAJA,qBAOMnN,KAAKkN,cAAgByR,EACrB3e,KAAK4e,YAAcpf,EACnBQ,KAAKgN,eAAgB,IAE3B,kDC5/CqV,MCQjV,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIjN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,mBAAmBqL,MAAM,CAAE+S,MAAkD,KAA3C9e,EAAIsR,OAAOC,MAAMwN,sBAA+B,EAAI,UAAW,CAAC3e,EAAG,IAAI,CAACY,MAAM,CAACge,OAAmD,SAA3Chf,EAAIsR,OAAOC,MAAMwN,uBAAkCpe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIif,4BAA4B,WAAW,CAACjf,EAAIe,GAAG,UAAUX,EAAG,IAAI,CAACY,MAAM,CAACge,OAAmD,SAA3Chf,EAAIsR,OAAOC,MAAMwN,uBAAkCpe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIif,4BAA4B,WAAW,CAACjf,EAAIe,GAAG,aACpiB,GAAkB,GCStB,IACEpD,KAAM,iBACNoM,QAAS,OAAX,OAAW,CAAX,GACA,kDCbyW,MCQrW,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAI/J,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,qBAAqBqL,MAAM,CAAE+S,MAAkD,SAA3C9e,EAAIsR,OAAOC,MAAMwN,sBAAmC,EAAI,KAAM,CAAC3e,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,MAAM,CAACM,YAAY,4BAA4B,CAACN,EAAG,KAAK,CAACJ,EAAIe,GAAG,UAAWf,EAAIkf,aAAmB,OAAE9e,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,UAAU,CAACwM,IAAI,eAAetM,MAAM,CAAC,MAAQN,EAAImf,aAAa,cAAc,QAAQ,iBAAiB,MAAM,KAAO,UAAU,CAAC/e,EAAG,eAAe,CAAC0B,YAAY,CAAC,gBAAgB,QAAQxB,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,MAAQ,CACnnB,CAAE2P,UAAU,EAAMnJ,QAAS,UAAWsY,QAAS,kBAAoB,CAAChf,EAAG,WAAW,CAACqC,KAAK,SAAS,CAACzC,EAAIe,GAAG,UAAUX,EAAG,aAAa,CAACM,YAAY,OAAOJ,MAAM,CAAC,OAAS,OAAO,QAAU,eAAe,UAAY,UAAU,CAACF,EAAG,KAAK,CAACM,YAAY,wBAAwB,GAAGN,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAOK,GAAG,CAAC,OAASX,EAAIqf,sBAAsB1U,MAAM,CAACvM,MAAO4B,EAAImf,aAAyB,aAAEhW,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAImf,aAAc,eAAgBvU,IAAME,WAAW,8BAA8B9K,EAAI+K,GAAI/K,EAAgB,cAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAK4b,aAAajf,MAAM,CAAC,MAAQqD,EAAKhG,KAAK,MAAQgG,EAAKlE,SAAQ,IAAI,IAAI,GAAKO,EAAIwf,WAAWzjB,OAAiHiE,EAAIuB,KAA7GnB,EAAG,MAAM,CAAC0B,YAAY,CAAC,aAAa,SAAS,MAAQ,OAAO,iBAAiB,SAAS,CAAC9B,EAAIe,GAAG,WAAoBX,EAAG,UAAU,CAACwM,IAAI,aAAatM,MAAM,CAAC,MAAQN,EAAIyf,WAAW,cAAc,QAAQ,iBAAiB,MAAM,KAAO,UAAU,CAACzf,EAAI+K,GAAI/K,EAAc,YAAE,SAAS2D,GAAM,OAAOvD,EAAG,eAAe,CAAC1B,IAAIiF,EAAK+b,QAAQpf,MAAM,CAAC,MAAQqD,EAAK+b,QAAQ,KAAO/b,EAAK+b,QAAQ,MAAQ,CACjiC,CAAEC,UAAW3f,EAAI4f,eAAgBR,QAAS,WACxC,CAAChf,EAAG,WAAW,CAACqC,KAAK,SAAS,CAACzC,EAAIe,GAAG,IAAIf,EAAI6B,GAAG8B,EAAK+b,SAAS,KAAKtf,EAAG,aAAa,CAACM,YAAY,OAAOJ,MAAM,CAAC,OAAS,OAAO,QAAUqD,EAAKkc,SAAS,UAAY,UAAU,CAACzf,EAAG,KAAK,CAACM,YAAY,wBAAwB,GAAGN,EAAG,WAAW,CAACuK,MAAM,CAACvM,MAAO4B,EAAIyf,WAAW9b,EAAK+b,SAAUvW,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAIyf,WAAY9b,EAAK+b,QAAS9U,IAAME,WAAW,6BAA6B,CAAC1K,EAAG,WAAW,CAACqC,KAAK,UAAU,CAACzC,EAAIe,GAAGf,EAAI6B,GAAG8B,EAAKmc,cAAc,IAAI,MAAK1f,EAAG,eAAe,CAAC0B,YAAY,CAAC,aAAa,SAAS,cAAc,SAAS,CAAC1B,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,UAAYN,EAAIwf,WAAWzjB,QAAQ4E,GAAG,CAAC,MAAQX,EAAI+f,aAAa,CAAC/f,EAAIe,GAAG,SAAS,IAAI,IAAI,GAAGX,EAAG,MAAM,CAACJ,EAAIe,GAAG,wCAChsB,GAAkB,GCFhBuG,GAAUrI,OAAOsI,sBAMhB,SAASyY,GAAelb,GAC7B,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,2BACfpC,OAAQ,MACRJ,WAcG,SAASmb,GAAYnb,GAC1B,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBxC,EAAO6C,WAA/B,2BACZzC,OAAQ,SAML,SAASgb,GAAUpb,GACxB,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBxC,EAAO6C,WAA/B,sBACZzC,OAAQ,QAML,SAASib,GAAa5kB,EAAMoM,GACjC,OAAOC,EAAM,CAEXnE,IAAK6D,GAAU,qBAAH,OAAwBK,EAAxB,sBACZzC,OAAQ,OACR3J,SAKG,SAAS6kB,GAAetb,GAC7B,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,+CAAH,OAAkDxC,EAAOub,kBACrEnb,OAAQ,QAOL,SAASob,GAAY/kB,EAAMoM,GAChC,OAAOC,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBK,EAAxB,cACZzC,OAAQ,OACR3J,SAKG,SAASglB,GAAehlB,EAAMoM,EAAYQ,GAC/C,OAAOP,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBK,EAAxB,sBAAgDQ,EAAhD,WACZjD,OAAQ,OACR3J,SAKG,SAASilB,GAAa1b,GAC3B,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBxC,EAAO6C,WAA/B,cACZzC,OAAQ,QAML,SAASub,GAAc3b,GAC5B,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,qBAAH,OAAwBxC,EAAO6C,WAA/B,sBAAuD7C,EAAOqD,WAA9D,WACZjD,OAAQ,SAML,SAASwb,GAAU5b,GACxB,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,4BACfpC,OAAQ,OACRJ,WAKG,SAAS6b,GAAW7b,GACzB,OAAO8C,EAAM,CACXnE,IAAK6D,GAAU,sBACfpC,OAAQ,MACRJ,WCnDJ,QACEnH,KAAM,sBACNpC,KAFF,WAGI,MAAO,CACLoM,WAAY,GACZiZ,YAAa,GACbzB,aAAc,CACZI,aAAc,IAEhBE,WAAY,GAGZP,aAAc,CACd,CACE,WAAR,wCACQ,aAAR,IACQ,aAAR,QACQ,aAAR,SAEA,CACQ,WAAR,UACQ,aAAR,IACQ,aAAR,QACQ,aAAR,UAGM2B,cAAe,GACfrB,WAAY,KAmBhB3T,MAAO,CACL2T,WADJ,SACA,GACM,IAAN,KACMsB,EAASjP,SAAQ,SAAvB,GACQ4N,EAAW9b,EAAK+b,SAAW/b,EAAK,gBAElC1D,KAAKwf,WAAaA,GAEpB,qCARJ,SAQA,GACkB,SAARzV,GAEF/J,KAAK8gB,sBAKXrX,QA9DF,WAiEIzJ,KAAK0H,WAAa1I,OAAO0K,cAAc,cACvC1J,KAAKoJ,SAAWpK,OAAO0K,cAAc,YACrC1J,KAAK8gB,oBACL9gB,KAAK+gB,kBAEPjX,QAAS,CACP,kBADJ,WACA,uKACA,GACA,yBAFA,SAIA,MAJA,OAIA,EAJA,OAKA,cACA,yBAEA,mBARA,8CAUI,eAXJ,WAWA,gLACA,IACA,0BAFA,OACA,EADA,OAIA,mCAEA,gDACA,wCACA,uBACA,sBACA,kBACA,uBAGA,gBAdA,8CAiBI,qBA5BJ,WA4BA,8KAKA,kCALA,OAKA,EALA,OAMA,cACA,sBACA,oCACA,iBATA,8CAYI,UAxCJ,WAwCA,qKACA,oCADA,SAEA,8BAFA,OAEA,EAFA,OAGA,cAEA,gBACA,YACA,eACA,kBARA,8CAYIgW,WApDJ,WAoDA,WAEM9f,KAAK2d,MAAM,gBAAgBqD,UAAS,SAA1C,GACQ,IAAIC,EAmCF,OADA9a,QAAQC,IAAI,mBACL,EAlCP,EAAV,0CACY,IAAI8a,EA4BF,OADA/a,QAAQC,IAAI,mBACL,EA3BP,IAAd,2CACcmZ,EAAW3N,SAAQ,SAAjC,GACgBlO,EAAKvF,MAAQ,EAA7B,sBACgBuF,EAAKyd,KAAOzd,EAAKmc,SACjBnc,EAAK0d,SAAW1d,EAAKkc,gBACdlc,EAAKmc,gBACLnc,EAAKkc,gBACLlc,EAAK2d,aAGd,IAAd,GAEgBC,WAAY,EAA5B,0BAEgBC,oBAAqBhC,GAEvBW,GAAa5kB,EAAM,EAAjC,8BACgC,KAAZ0L,EAAIpH,OACN,EAAlB,iBACkB,EAAlB,UACoBiH,QAAS,OACTC,KAAM,uBAiBtB6Y,eAhGJ,SAgGA,OACW,eAAX,QAGQzW,IAFAA,EAAS,IAAIvJ,MAAM,iBCtOmV,MCQ1W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAII,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,qBAAqBqL,MAAM,CAAE+S,MAAkD,SAA3C9e,EAAIsR,OAAOC,MAAMwN,sBAAmC,EAAI,KAAM,CAAC3e,EAAG,MAAM,CAACM,YAAY,qBAAqB,CAACN,EAAG,IAAI,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAG,UAAUX,EAAG,IAAI,CAACM,YAAY,aAAa,CAACV,EAAIe,GAAG,UAAUX,EAAG,iBAAiB,CAACO,GAAG,CAAC,OAASX,EAAIyhB,mBAAmB9W,MAAM,CAACvM,MAAO4B,EAAe,YAAEmJ,SAAS,SAAUyB,GAAM5K,EAAI0hB,YAAY9W,GAAKE,WAAW,gBAAgB,CAAC1K,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACN,EAAIe,GAAG,UAAUX,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,UAAU,CAACN,EAAIe,GAAG,UAAUX,EAAG,WAAW,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAG,aAAa,GAAGX,EAAG,IAAI,CAACM,YAAY,YAAYoB,YAAY,CAAC,OAAS,kBAAkB,CAAC9B,EAAIe,GAAG,UAAUX,EAAG,UAAU,CAACwM,IAAI,WAAWlM,YAAY,gBAAgBJ,MAAM,CAAC,MAAQN,EAAI2hB,KAAK,MAAQ3hB,EAAI4hB,MAAM,iBAAiB,MAAM,cAAc,UAAU,CAAsB,WAApB5hB,EAAI0hB,YAA0BthB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,gBAAgB,CAACF,EAAG,iBAAiB,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,iBAAgB,IAAI0E,MAAOC,UAAY,KAAW,iBAAiBjF,EAAI6hB,sBAAsB,KAAO,WAAW,eAAe,sBAAsB,YAAc,UAAUlhB,GAAG,CAAC,OAAS,SAASC,GAAQZ,EAAI8hB,aAAc,IAAI9c,MAAOC,YAAY0F,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAgB,YAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,cAAe/W,IAAME,WAAW,uBAAuB,IAAI,GAAG9K,EAAIuB,KAA0B,UAApBvB,EAAI0hB,YAAyBthB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,kBAAkB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAI+hB,qBAAsB3C,QAAS,WAAY,CAAChf,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,iBAAiB,CAACM,YAAY,YAAYoB,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,iBAAiBN,EAAIgiB,uBAAuB,KAAO,WAAW,eAAe,sBAAsB,YAAc,UAAUrhB,GAAG,CAAC,OAAS,SAASC,GAAQZ,EAAI8hB,aAAc,IAAI9c,MAAOC,WAAW,MAAQjF,EAAIiiB,UAAU,KAAOjiB,EAAIkiB,UAAUvX,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAoB,gBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,kBAAmB/W,IAAME,WAAW,0BAA0B1K,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,iBAAiB,CAACM,YAAY,YAAYoB,YAAY,CAAC,MAAQ,QAAQ,aAAa,QAAQxB,MAAM,CAAC,iBAAiBN,EAAIgiB,uBAAuB,KAAO,WAAW,SAAWhiB,EAAImiB,WAAW,eAAe,sBAAsB,YAAc,UAAUxhB,GAAG,CAAC,OAAS,SAASC,GAAQZ,EAAI8hB,aAAc,IAAI9c,MAAOC,WAAW,MAAQjF,EAAIiiB,UAAU,KAAOjiB,EAAIkiB,UAAUvX,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAkB,cAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,gBAAiB/W,IAAME,WAAW,yBAAyB,GAAG1K,EAAG,cAAc,CAACO,GAAG,CAAC,OAASX,EAAIoiB,kBAAkBzX,MAAM,CAACvM,MAAO4B,EAAc,WAAEmJ,SAAS,SAAUyB,GAAM5K,EAAImiB,WAAWvX,GAAKE,WAAW,eAAe,CAAC9K,EAAIe,GAAG,UAAUX,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,kBAAkB,CAACF,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,YAAc,WAAWK,GAAG,CAAC,OAASX,EAAIqiB,qBAAqB1X,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAkB,cAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,gBAAiB/W,IAAME,WAAW,uBAAuB9K,EAAI+K,GAAI/K,EAAe,aAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,IAAI,GAA+B,WAA3B4B,EAAI2hB,KAAKW,cAA4BliB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACM,YAAY,YAAYJ,MAAM,CAAC,MAAQ,KAAK,KAAO,iBAAiB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAIuiB,oBAAqBnD,QAAS,WAAY,CAAChf,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAmB,eAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,iBAAkB/W,IAAME,WAAW,wBAAwB9K,EAAI+K,GAAI/K,EAAe,aAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAG4B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,sBAAsB9K,EAAI+K,GAAI/K,EAAe,aAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,SAAWkiB,SAAS7e,EAAKvF,OAASokB,SAASxiB,EAAI2hB,KAAKc,gBAAgB,MAAQ9e,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,IAAI,GAAGgC,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,oBAAoB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAI0iB,uBAAwBtD,QAAS,aAAc,CAAChf,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAsB,kBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,oBAAqB/W,IAAME,WAAW,2BAA2B9K,EAAI+K,GAAI/K,EAAa,WAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAG4B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAoB,gBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,kBAAmB/W,IAAME,WAAW,yBAAyB9K,EAAI+K,GAAI/K,EAAa,WAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,SAAWkiB,SAAS7e,EAAKvF,QAAUokB,SAASxiB,EAAI2hB,KAAKgB,mBAAmB,MAAQhf,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAGgC,EAAG,MAAM,CAAC0B,YAAY,CAAC,aAAa,SAAS,CAAC1B,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAW,OAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,SAAU/W,IAAME,WAAW,gBAAgB9K,EAAI+K,GAAI/K,EAAmB,iBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,IAAI,IAAI,GAAGgC,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,YAAY,KAAO,iBAAiB,CAACF,EAAG,iBAAiB,CAACM,YAAY,KAAKoB,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,eAAe,KAAK,OAAS,KAAK,YAAc,WAAWqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,wBAAwB,IAAI,GAAG9K,EAAIuB,KAAiC,SAA3BvB,EAAI2hB,KAAKW,cAA0BliB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,oBAAoB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAI0iB,uBAAwBtD,QAAS,aAAc,CAAChf,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAsB,kBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,oBAAqB/W,IAAME,WAAW,2BAA2B9K,EAAI+K,GAAI/K,EAAe,aAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAG4B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAoB,gBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,kBAAmB/W,IAAME,WAAW,yBAAyB9K,EAAI+K,GAAI/K,EAAe,aAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,SAAWkiB,SAAS7e,EAAKvF,QAAUokB,SAASxiB,EAAI2hB,KAAKgB,mBAAmB,MAAQhf,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAGgC,EAAG,MAAM,CAAC0B,YAAY,CAAC,aAAa,SAAS,CAAC1B,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAW,OAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,SAAU/W,IAAME,WAAW,gBAAgB9K,EAAI+K,GAAI/K,EAAoB,kBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,IAAI,IAAI,GAAGgC,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,aAAa,KAAO,iBAAiB,CAACF,EAAG,iBAAiB,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,eAAe,QAAQ,OAAS,QAAQ,YAAc,WAAWqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,wBAAwB,IAAI,GAAG9K,EAAIuB,KAAiC,QAA3BvB,EAAI2hB,KAAKW,cAAyBliB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,oBAAoB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAI0iB,uBAAwBtD,QAAS,aAAc,CAAChf,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAsB,kBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,oBAAqB/W,IAAME,WAAW,2BAA2B9K,EAAI+K,GAAI/K,EAAmB,iBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAG4B,EAAIe,GAAG,OAAOX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAoB,gBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,kBAAmB/W,IAAME,WAAW,yBAAyB9K,EAAI+K,GAAI/K,EAAmB,iBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,SAAWkiB,SAAS7e,EAAKvF,QAAUokB,SAASxiB,EAAI2hB,KAAKgB,mBAAmB,MAAQhf,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,GAAGgC,EAAG,MAAM,CAAC0B,YAAY,CAAC,aAAa,SAAS,CAAC1B,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAAC0B,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,YAAc,QAAQqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAW,OAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,SAAU/W,IAAME,WAAW,gBAAgB9K,EAAI+K,GAAI/K,EAAqB,mBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAAC1B,IAAIiF,EAAKvF,MAAMkC,MAAM,CAAC,MAAQqD,EAAKwK,MAAM,MAAQxK,EAAKvF,YAAW,IAAI,IAAI,GAAGgC,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,cAAc,KAAO,iBAAiB,CAACF,EAAG,iBAAiB,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,eAAe,WAAW,OAAS,WAAW,YAAc,WAAWqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,wBAAwB,IAAI,GAAG9K,EAAIuB,KAAiC,SAA3BvB,EAAI2hB,KAAKW,cAA0BliB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACF,EAAG,cAAc,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,QAAU,UAAU,CAACF,EAAG,OAAO,CAACM,YAAY,oBAAoB,CAACN,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,aAAa,SAAW,GAAG,cAAc,sBAAsBqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAa,SAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,WAAY/W,IAAME,WAAW,oBAAoB,GAAG1K,EAAG,mBAAmB,CAACE,MAAM,CAAC,KAAO,YAAYmC,KAAK,YAAY,CAACrC,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAAC0B,YAAY,CAAC,OAAS,QAAQ,SAAW,SAAS,CAAC1B,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACV,EAAIe,GAAG,QAAQX,EAAG,MAAM,CAAC0B,YAAY,CAAC,QAAU,OAAO,YAAY,SAAS9B,EAAI+K,GAAI/K,EAAoB,kBAAE,SAAS2D,GAAM,OAAOvD,EAAG,MAAM,CAAC1B,IAAIiF,EAAKvF,MAAMsC,YAAY,cAAcM,MAAMhB,EAAI4iB,aAAajf,IAAO,CAACvD,EAAG,MAAM,CAACM,YAAY,aAAaC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAI6iB,UAAUlf,MAAS,CAAC3D,EAAIe,GAAGf,EAAI6B,GAAG8B,EAAKwK,eAAc,UAAU,IAAI,GAAG/N,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,cAAc,KAAO,iBAAiB,CAACF,EAAG,iBAAiB,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,eAAe,WAAW,OAAS,WAAW,YAAc,WAAWqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,wBAAwB,IAAI,GAAG9K,EAAIuB,KAAiC,UAA3BvB,EAAI2hB,KAAKW,cAA2BliB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,YAAY,CAACF,EAAG,cAAc,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,QAAU,UAAU,CAACF,EAAG,OAAO,CAACM,YAAY,oBAAoB,CAACN,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,aAAa,SAAW,GAAG,cAAc,sBAAsBqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAY,QAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,UAAW/W,IAAME,WAAW,mBAAmB,GAAG1K,EAAG,mBAAmB,CAACE,MAAM,CAAC,KAAO,YAAYmC,KAAK,YAAY,CAACrC,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAAC0B,YAAY,CAAC,OAAS,QAAQ,SAAW,SAAS,CAAC1B,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACV,EAAIe,GAAG,QAAQX,EAAG,MAAM,CAAC0B,YAAY,CAAC,QAAU,OAAO,YAAY,SAAS9B,EAAI+K,GAAI/K,EAAa,WAAE,SAAS2D,GAAM,OAAOvD,EAAG,MAAM,CAAC1B,IAAIiF,EAAKvF,MAAMsC,YAAY,iBAAiBM,MAAMhB,EAAI8iB,gBAAgBnf,IAAO,CAACvD,EAAG,MAAM,CAACM,YAAY,gBAAgBC,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAI+iB,SAASpf,MAAS,CAAC3D,EAAIe,GAAGf,EAAI6B,GAAG8B,EAAKvF,eAAc,UAAU,IAAI,GAAGgC,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,cAAc,KAAO,iBAAiB,CAACF,EAAG,iBAAiB,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,eAAe,WAAW,OAAS,WAAW,YAAc,WAAWqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAiB,aAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,eAAgB/W,IAAME,WAAW,wBAAwB,IAAI,GAAG9K,EAAIuB,MAAM,GAAGvB,EAAIuB,KAA0B,SAApBvB,EAAI0hB,YAAwBthB,EAAG,MAAM,CAACA,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,kBAAkB,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAI+hB,qBAAsB3C,QAAS,WAAY,CAAChf,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,iBAAiB,CAACM,YAAY,YAAYoB,YAAY,CAAC,MAAQ,SAASxB,MAAM,CAAC,iBAAiBN,EAAIgiB,uBAAuB,KAAO,WAAW,eAAe,sBAAsB,YAAc,UAAUrhB,GAAG,CAAC,OAAS,SAASC,GAAQZ,EAAI8hB,aAAc,IAAI9c,MAAOC,YAAY0F,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAoB,gBAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,kBAAmB/W,IAAME,WAAW,0BAA0B1K,EAAG,OAAO,CAAC0B,YAAY,CAAC,QAAU,eAAe,MAAQ,SAAS,CAAC9B,EAAIe,GAAG,QAAQX,EAAG,iBAAiB,CAACM,YAAY,YAAYoB,YAAY,CAAC,MAAQ,QAAQ,aAAa,QAAQxB,MAAM,CAAC,iBAAiBN,EAAIgiB,uBAAuB,KAAO,WAAW,SAAWhiB,EAAImiB,WAAW,eAAe,sBAAsB,YAAc,UAAUxhB,GAAG,CAAC,OAAS,SAASC,GAAQZ,EAAI8hB,aAAc,IAAI9c,MAAOC,YAAY0F,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAkB,cAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,gBAAiB/W,IAAME,WAAW,yBAAyB,GAAG1K,EAAG,cAAc,CAACO,GAAG,CAAC,OAASX,EAAIoiB,kBAAkBzX,MAAM,CAACvM,MAAO4B,EAAc,WAAEmJ,SAAS,SAAUyB,GAAM5K,EAAImiB,WAAWvX,GAAKE,WAAW,eAAe,CAAC9K,EAAIe,GAAG,UAAUX,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,UAAU,MAAQ,CAAC,CAAE2P,UAAU,EAAM0P,UAAW3f,EAAIgjB,UAAW5D,QAAS,SAAU,KAAO,SAAS,CAAChf,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,cAAcqK,MAAM,CAACvM,MAAO4B,EAAI2hB,KAAS,KAAExY,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI2hB,KAAM,OAAQ/W,IAAME,WAAW,gBAAgB,GAAG1K,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,MAAM,CAAC0B,YAAY,CAAC,QAAU,OAAO,kBAAkB,kBAAkB,CAAC1B,EAAG,kBAAkB,CAACE,MAAM,CAAC,oBAAoB,QAAQ,IAAM,EAAE,UAAY,EAAE,IAAM,KAAKqK,MAAM,CAACvM,MAAO4B,EAAW,QAAEmJ,SAAS,SAAUyB,GAAM5K,EAAIijB,QAAQrY,GAAKE,WAAW,aAAa1K,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,IAAIK,GAAG,CAAC,MAAQX,EAAIkjB,cAAc,CAACljB,EAAIe,GAAG,SAAS,KAAKX,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,WAAW,CAACF,EAAG,WAAW,CAAC0B,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,OAAS,MAAM,KAAON,EAAImjB,iBAAiB,oBAAoB,CAAC,mBAAoB,UAAU,MAAS,aAAa,CAAC/iB,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,KAAK,KAAO,QAAQ,MAAQ,SAASF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,cAAc,MAAQ,WAAW,IAAI,IAAI,GAAGN,EAAIuB,KAAKnB,EAAG,eAAe,CAAC0B,YAAY,CAAC,aAAa,SAAS,cAAc,QAAQ,CAAC1B,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,IAAIK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIojB,MAAM,eAAe,CAACpjB,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWK,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAI+f,WAAW,eAAe,CAAC/f,EAAIe,GAAG,SAAS,IAAI,IAAI,MAC3ze,GAAkB,GCDTsiB,I,oBAAc,CACvB,CAACjlB,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,SAEZmV,GAAc,CACvB,CAACllB,MAAO,SAAU+P,MAAO,MACzB,CAAC/P,MAAO,OAAQ+P,MAAO,MACvB,CAAC/P,MAAO,MAAO+P,MAAO,KACtB,CAAC/P,MAAO,OAAQ+P,MAAO,KACvB,CAAC/P,MAAO,QAAS+P,MAAO,MAEfoV,GAAY,CACrB,CAACnlB,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,QAEZqV,GAAkB,CAC3B,CAACplB,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,SAEZsV,GAAmB,CAC5B,CAACrlB,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,IAAK+P,MAAO,OACpB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,QACrB,CAAC/P,MAAO,KAAM+P,MAAO,SAGZuV,GAAkB,CAC3B,CAACtlB,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,QAGZwV,GAAoB,CAC7B,CAACvlB,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,QAEZyV,GAAY,CACrB,CAACxlB,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,IAAK+P,MAAO,MACpB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,OACrB,CAAC/P,MAAO,KAAM+P,MAAO,QAEZ0V,GAAmB,CAC5B,CAACzlB,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,OAClB,CAAC/P,MAAO,EAAG+P,MAAO,QAET2V,GAAY,CACrB,CAAC1lB,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,EAAG+P,MAAO,MAClB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,OACnB,CAAC/P,MAAO,GAAI+P,MAAO,QCzSnB4V,GAAgB,SAACC,EAAM5lB,EAAO+K,GAE9B,IAAK/K,EACD,OAAO+K,EAAS,IAAIvJ,MAAM,YAE1B,IAAIoF,KAAK5G,GAAO6G,UAAY,IAAaD,KAAKif,MAC9C9a,EAAS,IAAIvJ,MAAM,6BAEnBuJ,KAGJ+a,GAAoB,SAACF,EAAM5lB,EAAO+K,GAClC,IAAK/K,EACH,OAAO+K,EAAS,IAAIvJ,MAAM,YAE1BuJ,KAGS,IAEbgb,YAAa,CACX,CAAElU,UAAU,EAAM0P,UAAWoE,GAAe3E,QAAS,SAEvDgF,aAAc,CACZ,CAAEnU,UAAU,EAAM0P,UAAWuE,GAAmB9E,QAAS,SAE3DiF,SAAU,CACR,CAAEpU,UAAU,EAAMnJ,QAAS,QAASsY,QAAS,WAE/CkD,cAAe,CACb,CAAErS,UAAU,EAAMnJ,QAAS,UAAWsY,QAAS,YCyRnD,IACEzhB,KAAM,sBACNpC,KAFF,WAGI,MAAO,CACLqmB,MAAN,GACM0C,OAAO,EACPnc,WAAY,GACZoc,aAAc,GACd5c,WAAY,GACZ6c,WAAY,GACZC,UAAW,GACXC,YAAa,GACbC,WAAY,GACZ1B,QAAS,GACTE,iBAAkB,GAClBzB,YAAa,SACbC,KAAM,CACJwC,YAAa,GACbS,gBAAiB,IAAI5f,KACrB6f,cAAe,GACfvC,cAAe,GACfG,eAAgB,IAChBqC,aAAc,KACdnC,kBAAmB,IACnBoC,gBAAiB,KACjBC,OAAQ,KACRZ,aAAc,IACdC,SAAU,GACVY,QAAS,GACTC,KAAM,IAERC,SAAU,EACV9B,YAAN,GACMC,YAAN,GACMC,UAAN,GACMC,gBAAN,GACMC,iBAAN,GACMG,UAAN,GACMF,gBAAN,GACMC,kBAAN,GACME,iBAAN,GACMC,UAAN,GACM3B,YAAY,EACZL,aAAa,IAAI9c,MAAOC,YAG5B4G,MAAO,CACL,qCADJ,SACA,GACkB,SAAR7B,GACF/J,KAAKmlB,oBAKX3b,SAAF,CACIuY,uBADJ,WAGM,IAAN,EAEQqD,EADEplB,KAAK0hB,KAAKiD,gBACF3kB,KAAKqlB,QAAQrlB,KAAK0hB,KAAKiD,iBAAiBW,OAAO,cAE/CtlB,KAAKqlB,QAAQrlB,KAAK6hB,aAAayD,OAAO,cAGlD,IAAN,gDAEA,KAMM,OAJEC,EADR,KACcvlB,KAAKqlB,QAAQtgB,KAAKif,OAAOsB,OAAO,YAEhC,WAED,CACLE,aAAc,SAAtB,GACU,OAAOC,EAAKzgB,UAAYD,KAAKif,MAAQ,OAEvC0B,gBAAiBH,EAAM,gBAG3B3D,sBAzBJ,WA2BM,IAAN,EAEQwD,EADEplB,KAAK0hB,KAAKwC,YACFlkB,KAAKqlB,QAAQrlB,KAAK0hB,KAAKwC,aAAaoB,OAAO,cAE3CtlB,KAAKqlB,QAAQrlB,KAAK6hB,aAAayD,OAAO,cAGlD,IAAN,gDAEA,KAMM,OAJEC,EADR,KACcvlB,KAAKqlB,QAAQtgB,KAAKif,MAAQ,MAAYsB,OAAO,YAE7C,WAED,CACLE,aAAc,SAAtB,GACU,OAAOC,EAAKzgB,UAAYD,KAAKif,MAAQ,OAEvC0B,gBAAiBH,EAAM,iBAI7B9b,QAxGF,WA0GIzJ,KAAK0H,WAAa1I,OAAO0K,cAAc,cAEvC1J,KAAKmlB,mBAEPxb,QA9GF,aAiHEG,QAAS,CACPqb,gBADJ,WACA,WACM5E,GAAa,CAAnB,+CACQ,GAAiB,MAAbvZ,EAAIpH,KACN,GAAIoH,EAAIrK,OAAO,GAMb,GALA,EAAZ,kCACYqM,EAAIgB,MAAM,aAAc,EAApC,YACY,EAAZ,SACY,EAAZ,yBACY,EAAZ,qCACA,UAAgB,EAAhB,YAMc,OALA,EAAd,oDAEc,EAAd,gCACc,EAAd,2CACc,EAAd,8DACA,sBACgB,IAAK,SACH,EAAlB,wDACkB,EAAlB,sDACkB,EAAlB,6DACkB,EAAlB,2DACkB,EAAlB,wCACkB,EAAlB,wCACA,MACgB,IAAK,OACH,EAAlB,6DACkB,EAAlB,2DACkB,EAAlB,wCACkB,EAAlB,4DACA,MACgB,IAAK,MACH,EAAlB,0DACkB,EAAlB,wDACkB,EAAlB,wCACkB,EAAlB,iFACA,MACgB,IAAK,OACH,EAAlB,kCACkB,EAAlB,gCAC6B,KAALuF,EACF,EAAtB,sBACA,OACsB,EAAtB,sBACA,OACsB,EAAtB,sBACA,OACsB,EAAtB,sBACA,OACsB,EAAtB,sBACA,OACsB,EAAtB,sBACA,QACsB,EAAtB,yBAGkB,EAAlB,qCACkB,EAAlB,iFACA,MACgB,IAAK,QACH,EAAlB,oCACkB,EAAlB,iFACkB,EAAlB,iCACoB,EAApB,0BAEkB,EAAlB,qCACA,UAEA,yBACc,EAAd,uCACA,yBACc,EAAd,gCACc,EAAd,2CACc,EAAd,8DACc,EAAd,oCAGY,EAAZ,aAKI0T,YAlFJ,WAkFA,WAEMjjB,KAAK2d,MAAMgI,SAASC,cAAc,QAAQ,SAAhD,GACaC,GACHnF,GAAW,CAArB,2DAC6B,MAAb1Z,EAAIpH,OACN,EAAd,oBACcoH,EAAIrK,OAAOiV,SAAQ,SAAjC,GACgB,EAAhB,kDAOI,UAjGJ,SAiGA,6JACA,EADA,yCAEA,4BAFA,aAIA,eACA,2BALA,SAOA,gBAPA,OAOA,EAPA,QAQA,aACA,IAEA,8BAXA,8CAcIkQ,qBA/GJ,SA+GA,OACM,IAAK3jB,EACH,OAAO+K,EAAS,IAAIvJ,MAAM,YAE5B,GAAKK,KAAKkiB,WAWRhZ,QAXoB,CACpB,IAAKlJ,KAAK0hB,KAAKkD,cACb,OAAO1b,EAAS,IAAIvJ,MAAM,YAE1B,GAAI,IAAIoF,KAAK/E,KAAK0hB,KAAKkD,eAAe5f,WAAa,IAAID,KAAK5G,GAAO6G,UACjE,OAAOkE,EAAS,IAAIvJ,MAAM,oBAE1BuJ,MAORsY,kBAjIJ,SAiIA,cAEM,GADAxhB,KAAK2d,MAAMgI,SAASG,gBAChB9lB,KAAKskB,aACP,GAAIva,IAAQ/J,KAAKskB,aAAayB,aAC5B,GAAY,UAARhc,EAKF,OAJA/J,KAAK0hB,KAAKW,cAAgBriB,KAAKskB,aAAa0B,eAAeC,OAAOC,cAClElmB,KAAKkiB,YAAaliB,KAAKskB,aAAa0B,eAAeG,QACnDnmB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKskB,aAAa0B,eAAeI,UAC7DpmB,KAAK0hB,KAAKkD,cAAgB5kB,KAAKskB,aAAa0B,eAAeG,QAAUnmB,KAAKskB,aAAa0B,eAAeG,QAAU,GAC5H,yBACc,IAAK,SACHnmB,KAAK0hB,KAAKc,eAAiBxiB,KAAKskB,aAAa0B,eAAeK,UAAUC,MAAM,KAAK,GACjFtmB,KAAK0hB,KAAKmD,aAAe7kB,KAAKskB,aAAa0B,eAAeK,UAAUC,MAAM,KAAK,GAC/EtmB,KAAK0hB,KAAKgB,kBAAoB1iB,KAAKskB,aAAa0B,eAAeO,YAAYD,MAAM,KAAK,GACtFtmB,KAAK0hB,KAAKoD,gBAAkB9kB,KAAKskB,aAAa0B,eAAeO,YAAYD,MAAM,KAAK,GACpFtmB,KAAK0hB,KAAKqD,OAAS/kB,KAAKskB,aAAa0B,eAAeQ,aACpDxmB,KAAK0hB,KAAKyC,aAAenkB,KAAKskB,aAAa0B,eAAeS,OAAS,GACnF,MACc,IAAK,OACHzmB,KAAK0hB,KAAKgB,kBAAoB1iB,KAAKskB,aAAa0B,eAAeO,YAAYD,MAAM,KAAK,GACtFtmB,KAAK0hB,KAAKoD,gBAAkB9kB,KAAKskB,aAAa0B,eAAeO,YAAYD,MAAM,KAAK,GACpFtmB,KAAK0hB,KAAKqD,OAAS/kB,KAAKskB,aAAa0B,eAAeQ,aACpDxmB,KAAK0hB,KAAKyC,aAAenkB,KAAKskB,aAAa0B,eAAeU,OAAS,IAAM1mB,KAAKskB,aAAa0B,eAAeS,OAC1H,MACc,IAAK,MACHzmB,KAAK0hB,KAAKgB,kBAAoB1iB,KAAKskB,aAAa0B,eAAeW,SAASL,MAAM,KAAK,GACnFtmB,KAAK0hB,KAAKoD,gBAAkB9kB,KAAKskB,aAAa0B,eAAeW,SAASL,MAAM,KAAK,GACjFtmB,KAAK0hB,KAAKqD,OAAS/kB,KAAKskB,aAAa0B,eAAeQ,aACpDxmB,KAAK0hB,KAAKyC,aAAenkB,KAAKskB,aAAa0B,eAAeY,KAAO,IAAM5mB,KAAKskB,aAAa0B,eAAeU,OAAS,IAAM1mB,KAAKskB,aAAa0B,eAAeS,OACxK,MACc,IAAK,OACHzmB,KAAKukB,WAAavkB,KAAKskB,aAAa0B,eAAezB,WACnDvkB,KAAKukB,WAAW3S,SAAQ,SAAxC,GAC2B,KAALrC,EACF,EAApB,sBACA,OACoB,EAApB,sBACA,OACoB,EAApB,sBACA,OACoB,EAApB,sBACA,OACoB,EAApB,sBACA,OACoB,EAApB,sBACA,QACoB,EAApB,yBAGgBvP,KAAK0hB,KAAK0C,SAAWpkB,KAAKwkB,UAAUlgB,WACpCtE,KAAK0hB,KAAKyC,aAAenkB,KAAKskB,aAAa0B,eAAeY,KAAO,IAAM5mB,KAAKskB,aAAa0B,eAAeU,OAAS,IAAM1mB,KAAKskB,aAAa0B,eAAeS,OACxK,MACc,IAAK,QACHzmB,KAAKykB,YAAczkB,KAAKskB,aAAa0B,eAAevB,YACpDzkB,KAAK0hB,KAAKyC,aAAenkB,KAAKskB,aAAa0B,eAAeY,KAAO,IAAM5mB,KAAKskB,aAAa0B,eAAeU,OAAS,IAAM1mB,KAAKskB,aAAa0B,eAAeS,OACxJzmB,KAAKykB,YAAY7S,SAAQ,SAAzC,GACkB,EAAlB,0BAEgB5R,KAAK0hB,KAAKsD,QAAUhlB,KAAK0kB,WAAWpgB,WACpD,UAEA,cACY2C,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,GACrDnb,KAAK0hB,KAAKwC,YAAc,IAAInf,KAAK/E,KAAKskB,aAAa0B,eAAeI,YAC9E,aACYnf,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,GAErDnb,KAAKkiB,YAAaliB,KAAKskB,aAAa0B,eAAeG,QACnDnmB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKskB,aAAa0B,eAAeI,UAC7DpmB,KAAK0hB,KAAKkD,cAAgB5kB,KAAKskB,aAAa0B,eAAeG,QAAUnmB,KAAKskB,aAAa0B,eAAeG,QAAU,GAChHnmB,KAAK0hB,KAAKuD,KAAOjlB,KAAKskB,aAAa0B,eAAec,cAGpD7f,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,GACzC,WAARpR,EACF/J,KAAK0hB,KAAKwC,YAAc,GACpC,aACYlkB,KAAK0hB,KAAKW,cAAgB,GAC1BriB,KAAKkiB,YAAa,EAClBliB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKqlB,QAAQtgB,KAAKif,OAAOsB,OAAO,uBAC5DtlB,KAAK0hB,KAAKkD,cAAgB,IACtC,aACY5kB,KAAKkiB,YAAa,EAClBliB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKqlB,QAAQtgB,KAAKif,OAAOsB,OAAO,uBAC5DtlB,KAAK0hB,KAAKkD,cAAgB,GAC1B5kB,KAAK0hB,KAAKuD,KAAO,GACjBjlB,KAAKgjB,QAAU,GACfhjB,KAAKkjB,iBAAmB,SAI5Bjc,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,GACzC,WAARpR,EACF/J,KAAK0hB,KAAKwC,YAAc,GAClC,aACUlkB,KAAK0hB,KAAKW,cAAgB,GAC1BriB,KAAKkiB,YAAa,EAClBliB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKqlB,QAAQtgB,KAAKif,OAAOsB,OAAO,uBAC5DtlB,KAAK0hB,KAAKkD,cAAgB,IACpC,aACU5kB,KAAKkiB,YAAa,EAClBliB,KAAK0hB,KAAKiD,gBAAkB3kB,KAAKqlB,QAAQtgB,KAAKif,OAAOsB,OAAO,uBAC5DtlB,KAAK0hB,KAAKkD,cAAgB,GAC1B5kB,KAAK0hB,KAAKuD,KAAO,GACjBjlB,KAAKgjB,QAAU,GACfhjB,KAAKkjB,iBAAmB,KAI9BlB,UA9OJ,WA+OMhiB,KAAK+mB,UAAY5hB,KAAKnB,MAAMmB,KAAKT,UAAUuC,SAAS4f,qBAAqB,QAAQ,GAAG1L,YACpFlU,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,IAEvD6L,WAlPJ,WAmPM/f,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,IAEvD8G,SArPJ,WAsPMhb,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAYnb,KAAK+mB,WAE5DzE,oBAxPJ,SAwPA,OACM,OAAKnkB,EAGA6B,KAAK0hB,KAAKmD,aAGXtC,SAASviB,KAAK0hB,KAAKmD,cAAgBtC,SAASpkB,GACvC+K,EAAS,IAAIvJ,MAAM,0BAE1BuJ,IALOA,EAAS,IAAIvJ,MAAM,cAHnBuJ,EAAS,IAAIvJ,MAAM,eAW9B8iB,uBArQJ,SAqQA,OACM,OAAKtkB,EAGA6B,KAAK0hB,KAAKoD,gBAGV9kB,KAAK0hB,KAAKqD,OAGXxC,SAASviB,KAAK0hB,KAAKoD,kBAAoBvC,SAASpkB,GAC3C+K,EAAS,IAAIvJ,MAAM,wBAE1BuJ,IALOA,EAAS,IAAIvJ,MAAM,aAHnBuJ,EAAS,IAAIvJ,MAAM,cAHnBuJ,EAAS,IAAIvJ,MAAM,eAc9ByiB,oBArRJ,SAqRA,GACMpiB,KAAK2d,MAAMgI,SAASG,gBAElB9lB,KAAK0hB,KAAKqD,OADA,WAARhb,EACiB,KAC3B,sBAC2B,IAEA,GAET,QAARA,GAAyB,SAARA,GAA0B,UAARA,GACrC9C,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,GACrDnb,KAAK0hB,KAAKyC,aAAe,YACjC,YACQld,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,OACrDnb,KAAK0hB,KAAKyC,aAAe,UAEzBld,SAAS4f,qBAAqB,QAAQ,GAAG1L,UAAY,KACrDnb,KAAK0hB,KAAKyC,aAAe,MAEf,WAARpa,GACF/J,KAAK0hB,KAAKgB,kBAAoB,IAC9B1iB,KAAK0hB,KAAKoD,gBAAkB,MACpC,YACQ9kB,KAAK0hB,KAAKgB,kBAAoB,IAC9B1iB,KAAK0hB,KAAKoD,gBAAkB,MACpC,YACQ9kB,KAAK0hB,KAAKgB,kBAAoB,IAC9B1iB,KAAK0hB,KAAKoD,gBAAkB,OAGhC3C,iBAnTJ,SAmTA,GACUpY,IACF/J,KAAK0hB,KAAKkD,cAAgB,KAG9BjC,aAxTJ,SAwTA,GACM,MAAO,CAAC3iB,KAAKukB,WAAWnd,QAAQ1D,EAAKvF,QAAU,EAAI,MAAQ,KAE7D0kB,gBA3TJ,SA2TA,GACM,MAAO,CAAC7iB,KAAKykB,YAAYrd,QAAQ1D,EAAKvF,QAAU,EAAI,MAAQ,KAE9DykB,UA9TJ,SA8TA,GACM,IAAI5X,EAAQhL,KAAKukB,WAAWnd,QAAQ1D,EAAKvF,OACrC6M,EAAQ,GACVhL,KAAKwkB,UAAUpoB,KAAKsH,EAAKwK,OACzBlO,KAAKukB,WAAWnoB,KAAKsH,EAAKvF,SAE1B6B,KAAKwkB,UAAUxnB,OAAOgO,EAAO,GAC7BhL,KAAKukB,WAAWvnB,OAAOgO,EAAO,IAEhChL,KAAK0hB,KAAK0C,SAAWpkB,KAAKwkB,UAAUlgB,YAEtCwe,SAzUJ,SAyUA,GACM,IAAI9X,EAAQhL,KAAKykB,YAAYrd,QAAQ1D,EAAKvF,OACtC6M,EAAQ,GACVhL,KAAK0kB,WAAWtoB,KAAKsH,EAAKwK,OAC1BlO,KAAKykB,YAAYroB,KAAKsH,EAAKvF,SAE3B6B,KAAK0kB,WAAW1nB,OAAOgO,EAAO,GAC9BhL,KAAKykB,YAAYznB,OAAOgO,EAAO,IAEjChL,KAAK0hB,KAAKsD,QAAUhlB,KAAK0kB,WAAWpgB,YAEtC6e,MApVJ,SAoVA,cACM,IAAKnjB,KAAKkI,WAMR,OALAlI,KAAK2d,MAAMsJ,GAAUC,mBACrBlnB,KAAK8X,SAAS,CACZhR,KAAM,UACND,QAAS,SAIb2Z,GAAc,CAApB,0EACyB,MAAbxZ,EAAIpH,OACN,EAAV,SACU,EAAV,yBACA,WAAc,EAAd,YACY,EAAZ,oBACA,yBACY,EAAZ,sBACY,EAAZ,cACY,EAAZ,yEACY,EAAZ,sBACYmH,YAAW,WACT,EAAd,2BACA,IACA,yBACY,EAAZ,cACY,EAAZ,yEACY,EAAZ,sBACY,EAAZ,aACY,EAAZ,WACY,EAAZ,qBAEU,EAAV,gBACU,EAAV,UACYD,KAAM,UACND,QAAS,cAMjBiZ,WA5XJ,SA4XA,cACM9f,KAAK2d,MAAMsJ,GAAUjG,UAAS,SAApC,GACQ,IAAIC,EA8BF,OAAO,EA7BP,IAAV,eACc,EAAd,MACYZ,GAAY8G,EAAO,EAA/B,8BAC+B,MAAbngB,EAAIpH,OACN,EAAhB,UACkBkH,KAAM,UACND,QAAS,SAEX,EAAhB,oBACgBmC,EAAIgB,MAAM,aAAc,EAAxC,YACgB,EAAhB,SACgB,EAAhB,mBAKYsW,GAAe6G,EAAO,EAAlC,2CAC+B,MAAbngB,EAAIpH,OACN,EAAhB,UACkBkH,KAAM,UACND,QAAS,SAEX,EAAhB,SACgB,EAAhB,uBAUIugB,SAhaJ,WAiaM,IAAIC,EAAM,GAcV,GAVAA,EAAItB,aAAe/lB,KAAKyhB,YAUC,WAArBzhB,KAAKyhB,YACP4F,EAAIjB,UAAYpmB,KAAK0hB,KAAKwC,iBAClC,8BAIQ,OAHAmD,EAAIjB,UAAYpmB,KAAK0hB,KAAKiD,gBAC1B0C,EAAIlB,QAAUnmB,KAAK0hB,KAAKkD,cACxByC,EAAIpB,OAASjmB,KAAK0hB,KAAKW,cAAc/D,cAC7C,yBACU,IAAK,SACH+I,EAAIhB,UAAYrmB,KAAK0hB,KAAKc,eAAiB,IAAMxiB,KAAK0hB,KAAKmD,aAC3DwC,EAAId,YAAcvmB,KAAK0hB,KAAKgB,kBAAoB,IAAM1iB,KAAK0hB,KAAKoD,gBAChEuC,EAAIb,aAAexmB,KAAK0hB,KAAKqD,OAC7BsC,EAAIZ,OAASzmB,KAAK0hB,KAAKyC,aACnC,MACU,IAAK,OACHkD,EAAId,YAAcvmB,KAAK0hB,KAAKgB,kBAAoB,IAAM1iB,KAAK0hB,KAAKoD,gBAChEuC,EAAIb,aAAexmB,KAAK0hB,KAAKqD,OAC7BsC,EAAIX,OAAS1mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC/Ce,EAAIZ,OAASzmB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC3D,MACU,IAAK,MACHe,EAAIV,SAAW3mB,KAAK0hB,KAAKgB,kBAAoB,IAAM1iB,KAAK0hB,KAAKoD,gBAC7DuC,EAAIb,aAAexmB,KAAK0hB,KAAKqD,OAC7BsC,EAAIT,KAAO5mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC7Ce,EAAIX,OAAS1mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC/Ce,EAAIZ,OAASzmB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC3D,MACU,IAAK,OACHe,EAAI9C,WAAavkB,KAAKukB,WACtB8C,EAAIT,KAAO5mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC7Ce,EAAIX,OAAS1mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC/Ce,EAAIZ,OAASzmB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC3D,MACU,IAAK,QACHe,EAAI5C,YAAczkB,KAAKykB,YACvB4C,EAAIT,KAAO5mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC7Ce,EAAIX,OAAS1mB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC/Ce,EAAIZ,OAASzmB,KAAK0hB,KAAKyC,aAAamC,MAAM,KAAK,GAC3D,UAEA,4BACQe,EAAIjB,UAAYpmB,KAAK0hB,KAAKiD,gBAC1B0C,EAAIlB,QAAUnmB,KAAK0hB,KAAKkD,cAExByC,EAAIP,QAAU9mB,KAAK0hB,KAAKuD,MAE1B,OAAOoC,KCp4BiW,MCQ1W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAItnB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,UAAU,CAACM,YAAY,UAAUqL,MAAM,CAAGwb,UAAWvnB,EAAIwnB,gBAAiB3R,MAAO7V,EAAIynB,cAAgB,CAAEznB,EAAmB,gBAAEI,EAAG,OAAO,CAACM,YAAY,uBAAuBC,GAAG,CAAC,UAAYX,EAAI0nB,KAAK,UAAY1nB,EAAI2nB,UAAU,QAAU3nB,EAAI4nB,UAAU,CAACxnB,EAAG,OAAO,CAACM,YAAY,QAAQC,GAAG,CAAC,MAAQX,EAAI6nB,cAAc,CAAC7nB,EAAIe,GAAG,QAAQX,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI8nB,QAAQ,IAAM,MAAkC,IAA3B9nB,EAAI+nB,mBAA0B3nB,EAAG,OAAO,CAACM,YAAY,WAAW,CAACN,EAAG,IAAI,CAACM,YAAY,0BAA0BV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI+nB,uBAAuB/nB,EAAIuB,SAASnB,EAAG,OAAO,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACM,YAAY,QAAQC,GAAG,CAAC,MAAQX,EAAI6nB,cAAc,CAAC7nB,EAAIe,GAAG,QAAQX,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI8nB,QAAQ,IAAM,MAAkC,IAA3B9nB,EAAI+nB,mBAA0B3nB,EAAG,OAAO,CAACM,YAAY,WAAW,CAACN,EAAG,IAAI,CAACM,YAAY,0BAA0BV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAI+nB,uBAAuB/nB,EAAIuB,SAAUvB,EAAoB,iBAAEI,EAAG,MAAM,CAACkM,WAAW,CAAC,CAAC3O,KAAK,UAAU4O,QAAQ,YAAYnO,MAAO4B,EAAW,QAAE8K,WAAW,YAAY8B,IAAI,kBAAkBlM,YAAY,oBAAoB,CAACN,EAAG,UAAU,CAACE,MAAM,CAAC,KAAO,QAAQqK,MAAM,CAACvM,MAAO4B,EAAc,WAAEmJ,SAAS,SAAUyB,GAAM5K,EAAIgoB,WAAWpd,GAAKE,WAAW,eAAe,CAAC1K,EAAG,cAAc,CAACE,MAAM,CAAC,KAAO,GAAG,MAAQ,OAAO,KAAO,eAAe,CAACF,EAAG,aAAa,CAACwM,IAAI,gBAAgBtM,MAAM,CAAC,OAASN,EAAI6Q,WAAW,GAAGzQ,EAAG,cAAc,CAACE,MAAM,CAAC,KAAO,GAAG,MAAQ,OAAO,KAAO,iBAAiB,CAACF,EAAG,eAAe,CAACwM,IAAI,kBAAkBtM,MAAM,CAAC,OAASN,EAAI6Q,WAAW,IAAI,IAAI,GAAG7Q,EAAIuB,QAC9iD,GAAkB,GCDlB,GAAS,WAAa,IAAIvB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,KAAK,CAACkM,WAAW,CAAC,CAAC3O,KAAK,UAAU4O,QAAQ,YAAYnO,MAAO4B,EAAW,QAAE8K,WAAW,YAAY8B,IAAI,sBAAsBlM,YAAY,qBAAqB,CAA0C,IAAxCV,EAAIsR,OAAOC,MAAM0W,YAAYlsB,OAAcqE,EAAG,MAAMJ,EAAI+K,GAAI/K,EAAIsR,OAAOC,MAAiB,aAAE,SAAS5N,GAAM,OAAOvD,EAAG,KAAK,CAAC1B,IAAIiF,EAAKmO,MAAM,CAAC9R,EAAIe,GAAGf,EAAI6B,GAAG8B,EAAKqO,aAAY,GAAG5R,EAAG,WAAW,CAACM,YAAY,QAAQJ,MAAM,CAAC,aAAa,GAAG,YAAc,eAAe,IAC/f,GAAkB,GCDlB,GAAS,WAAa,IAAIN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,QAC/F,GAAkB,GCItB,IACE7E,KADF,WAEI,MAAO,CACLgS,SAAS,IAGbxD,QAAS,CAEPme,iBAFJ,SAEA,GACMC,EAAGC,SAAS,CACV3Y,IAAK0Y,EAAGE,aACRC,SAAU,cChB8V,MCO5W,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QCPf,IACE/sB,KADF,WAEI,MAAO,CACL0Y,KAAM,KAGVsU,QAAS,GACT1c,MAAO,CACL,2BADJ,SACA,cACA,iBACM5L,KAAK6L,WAAU,WACb,EAAR,mDAIE/B,QAAS,IC1BwW,MCQ/W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAI/J,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAAoE,IAAlEV,EAAIsR,OAAOC,MAAMgC,cAAcC,wBAAwBzX,OAAcqE,EAAG,WAAW,CAACkM,WAAW,CAAC,CAAC3O,KAAK,UAAU4O,QAAQ,YAAYnO,MAAO4B,EAAW,QAAE8K,WAAW,YAAY8B,IAAI,qBAAqB9K,YAAY,CAAC,MAAQ,QAAQxB,MAAM,CAAC,KAAON,EAAIsR,OAAOC,MAAMgC,cAAcC,wBAAwB,OAAS,GAAG,KAAO,QAAQ,OAAS,SAAS,CAACpT,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,YAAY,QAAQ,wBAAwB,MAAMF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,eAAe,MAAQ,OAAO,YAAY,QAAQyB,YAAY/B,EAAIgC,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,OAAO,CAACY,MAAM,CAAC,MAAQ,KAAQhB,EAAIiO,iBAAiB/L,EAAME,IAAI2O,cAAkB,QAAM3Q,EAAG,OAAO,CAACJ,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIiO,iBAAiB/L,EAAME,IAAI2O,cAAc5C,cAAc,MAAK,EAAM,cAAc/N,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,eAAe,MAAQ,OAAO,YAAY,QAAQ,wBAAwB,MAAMF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,YAAY,QAAQ,wBAAwB,MAAMF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,YAAY,MAAQ,OAAO,YAAY,QAAQ,wBAAwB,MAAMF,EAAG,kBAAkB,CAACE,MAAM,CAAC,KAAO,UAAU,MAAQ,KAAK,YAAY,QAAQyB,YAAY/B,EAAIgC,GAAG,CAAC,CAACtD,IAAI,UAAUuD,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,aAAa,CAACE,MAAM,CAAC,IAAM4B,EAAME,OAAOhC,EAAG,OAAO,CAACM,YAAY,WAAWoB,YAAY,CAAC,cAAc,QAAQnB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOZ,EAAIwoB,kBAAkBtmB,EAAME,QAAQ,CAACpC,EAAIe,GAAG,YAAY,MAAK,EAAM,eAAe,GAAGX,EAAG,WAAW,CAACM,YAAY,QAAQJ,MAAM,CAAC,aAAa,GAAG,YAAc,eAAe,IAC/sD,GAAkB,GCDlB,GAAS,WAAa,IAAIN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,OAAO,CAACA,EAAG,OAAO,CAACM,YAAY,WAAWC,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIiN,eAAgB,KAAQ,CAACjN,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUN,EAAIiN,cAAc,MAAQ,MAAM,mBAAmB,GAAG,wBAAuB,EAAM,iBAAiB,IAAItM,GAAG,CAAC,iBAAiB,SAASC,GAAQZ,EAAIiN,cAAcrM,KAAU,CAACR,EAAG,kBAAkB,CAACE,MAAM,CAAC,OAAS,EAAE,WAAa,CAAEmoB,SAAU,UAAW,CAACroB,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIoC,IAAIuY,eAAeva,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIiO,iBAAiBjO,EAAIoC,IAAI2O,cAAc5C,OAAO,OAAO/N,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIoC,IAAIsmB,iBAAiBtoB,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIoC,IAAIumB,eAAevoB,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACN,EAAIe,GAAG,IAAIf,EAAI6B,GAAG7B,EAAI4oB,mBAAmB5oB,EAAIoC,IAAIymB,mBAAmBzoB,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACM,YAAY,eAAe,CAACV,EAAIe,GAAGf,EAAI6B,GAAG7B,EAAIoC,IAAI0mB,iBAAiB,IAAI,IAAI,IACnoC,GAAkB,G,UCAf,SAASC,GAAeC,GAC7B,IAAKA,GAAMA,EAAK,EAAG,MAAO,GAE1B,IAAMC,EAAIzM,KAAK0M,MAAMF,EAAK,MAEpBG,EAAMH,EAAS,GAAJC,EAAS,GAAK,IACzBzrB,EAAIgf,KAAK4M,KAAKD,EAAM,KAC1B,OAAe,IAANF,EAAU,GAAMA,EAAI,MAASzrB,EAAI,KCe5C,QACEwS,MAAO,CACL5N,IAAK,CACH6N,UAAU,EACVlJ,KAAM/K,SAGVT,KAPF,WAQI,MAAO,CACL0R,eAAe,EACfgB,iBAAN,KAGExE,SAAU,CACRmf,mBADJ,WAEM,OAAO,SAAb,GACQ,OAAOG,GAAeC,MAI5B9U,WAAY,GACZnK,QAAS,CACPsf,OADJ,WAEMppB,KAAKgN,eAAgB,GAEvBqc,OAJJ,WAKMrpB,KAAKgN,eAAgB,GAEvBsc,WAPJ,WAQMtpB,KAAKgN,eAAgB,EACrBhN,KAAKupB,UAAS,kBCrD+V,MCQ/W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCiBf,IACEjuB,KADF,WAEI,MAAO,CACLkuB,UAAW,GACXC,qBAAqB,EACrBzb,iBAAN,KAGEsa,QAAS,GACTrU,WAAY,CACVyV,WAAJ,IAEE5f,QAAS,CACP6f,aADJ,WAEM3pB,KAAKypB,qBAAsB,GAG7BlB,kBALJ,SAKA,GACM,IAAKpmB,EAAI5G,SAAU,OAAOyE,KAAK8X,SAAS5R,MAAM,YAC9C8C,EAAIgB,MAAM,YAAa7H,EAAI5G,aCvDoV,MCQjX,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCef,IACED,KADF,WAEI,MAAO,CACLsuB,MAAM,EACN/B,QAAS,EAAf,QACMN,gBAAiB,kBACjBQ,WAAY,aACZnX,OAAQ,GACRtD,SAAS,EAETuc,UAAW,IAEXC,kBAAkB,IAGtBtgB,SAAU,CAERge,YAFJ,WAGM,MAAmD,KAA5CxnB,KAAKqR,OAAOC,MAAMwN,sBAC/B,sBACA,uBAGIiL,gBARJ,WAcM,OALI/pB,KAAK8pB,kBAA6C,oBAAzB9pB,KAAKunB,gBAChCtgB,SAAS+iB,KAAK7U,iBAAiB,UAAWnV,KAAKiqB,cAE/ChjB,SAAS+iB,KAAKE,oBAAoB,UAAWlqB,KAAKiqB,cAG1D,iEAGInC,mBAlBJ,WAmBM,IAAN,0DACM,IAAKxU,GAA0C,IAAzBA,EAAcxX,OAAc,OAAO,EACzD,IAAN,wBACQ,OAAO4H,EAAKoN,eAAiB9C,GAAiBI,KAAKjQ,SAErD,OAAOgsB,EAASruB,SAGpBmY,WAAY,CACVmW,WAAJ,GACIC,aAAJ,IAEEvgB,QAAS,CACPmgB,aADJ,WAEM,IAAN,uDACMjqB,KAAK6pB,UAAYS,EAAYC,aAC7BvqB,KAAK4pB,MAAO,GAEd,YANJ,WAMA,wJACA,mBAKA,uCACA,sCACA,qFAGA,sCACA,sCAVA,sBACA,uCAHA,8CAgBInC,KAtBJ,SAsBA,GAEM,GADA/nB,EAAE8qB,iBACgB,IAAd9qB,EAAE+qB,SAAkBzqB,KAAK4pB,KAA7B,CAEA,IAAN,8BACA,uDACMU,EAAYxe,MAAMC,OAAS/L,KAAK6pB,UAAYa,EAAW,OAEzDhD,UA9BJ,SA8BA,GACM1nB,KAAK4pB,MAAO,EACZ5pB,KAAK2qB,aAAejrB,EAAE+qB,SAExB9C,QAlCJ,WAmCM3nB,KAAK4pB,MAAO,EACZ,IAAN,uDACM5pB,KAAK6pB,UAAYS,EAAYC,gBCrH4U,MCQ3W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCYf,IACE7sB,KAAM,OACNuW,WAAY,CACV2W,QAAJ,GACIC,aAAJ,GACIC,cAAJ,GACIC,YAAJ,GACIC,eAAJ,GACIC,oBAAJ,GACIC,oBAAJ,GACIC,OAAJ,IAGExhB,QAbF,WAcI1C,SAAS+E,cAAc,mBAAmBmJ,iBAAiB,cAAenV,KAAKorB,uBAEjFthB,QAAS,CACPshB,qBADJ,SACA,GACMnf,EAAMue,mBAGVtY,cArBF,WAsBIjL,SAAS+E,cAAc,mBAAmBke,oBAAoB,cAAelqB,KAAKorB,wBCrDwP,MCQ1U,I,UAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIrrB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAAC0B,YAAY,CAAC,aAAa,SAAS,QAAU,YAAY,CAAC1B,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,KAAO,uBAAuBK,GAAG,CAAC,MAAQ,SAASC,GAAQZ,EAAIiN,eAAgB,KAAQ,CAACjN,EAAIe,GAAG,UAAUX,EAAG,YAAY,CAACE,MAAM,CAAC,kBAAiB,EAAK,wBAAuB,EAAM,wBAAuB,EAAM,QAAUN,EAAIiN,cAAc,MAAQ,SAAStM,GAAG,CAAC,iBAAiB,SAASC,GAAQZ,EAAIiN,cAAcrM,KAAU,CAACR,EAAG,MAAM,CAACM,YAAY,eAAeJ,MAAM,CAAC,KAAO,SAASmC,KAAK,SAAS,CAACrC,EAAG,OAAO,CAACM,YAAY,OAAO,CAACV,EAAIe,GAAG,YAAYX,EAAG,MAAM,CAACA,EAAG,UAAU,CAACwM,IAAI,WAAWlM,YAAY,gBAAgBJ,MAAM,CAAC,MAAQN,EAAI4lB,SAAS,MAAQ5lB,EAAI4hB,MAAM,cAAc,UAAU,CAACxhB,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACF,EAAG,WAAW,CAACuK,MAAM,CAACvM,MAAO4B,EAAI4lB,SAAa,KAAEzc,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI4lB,SAAU,OAAQhb,IAAME,WAAW,oBAAoB,GAAG1K,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,YAAc,OAAOqK,MAAM,CAACvM,MAAO4B,EAAI4lB,SAAiB,SAAEzc,SAAS,SAAUyB,GAAM5K,EAAIsf,KAAKtf,EAAI4lB,SAAU,WAAYhb,IAAME,WAAW,sBAAsB,CAAC1K,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,WAAWF,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,QAAQ,MAAQ,YAAY,IAAI,IAAI,IAAI,GAAGF,EAAG,OAAO,CAACM,YAAY,gBAAgBJ,MAAM,CAAC,KAAO,UAAUmC,KAAK,UAAU,CAACrC,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,QAAQK,GAAG,CAAC,MAAQX,EAAIsrB,YAAY,CAACtrB,EAAIe,GAAG,QAAQX,EAAG,YAAY,CAACkM,WAAW,CAAC,CAAC3O,KAAK,UAAU4O,QAAQ,oBAAoBe,IAAI,YAAYhN,MAAM,CAAC,KAAO,WAAWK,GAAG,CAAC,MAAQX,EAAI+f,aAAa,CAAC/f,EAAIe,GAAG,SAAS,MAAM,IACtpD,GAAkB,GCkCtB,IACEpD,KAAM,cACNpC,KAFF,WAGI,MAAO,CAEL0R,eAAe,EACf2Y,SAAU,CACRjoB,KAAM,GACN0L,SAAU,IAEZuY,MAAO,CACLjkB,KAAM,CACd,CAAU,UAAV,EAAU,QAAV,UAAU,QAAV,SAEQ0L,SAAU,CAClB,CAAU,UAAV,EAAU,QAAV,UAAU,QAAV,cAKEU,QAAS,CACPgW,WADJ,WACA,WACM9f,KAAK2d,MAAM,YAAYqD,SAA7B,4KACA,EADA,uBAEA,aAFA,SAGA,KAHA,OAGA,EAHA,OAIA,gBACA,WADA,EACA,WADA,EACA,OADA,EACA,WACA,gBACA,aACA,OACA,0EACA,aACA,WACA,WAZA,mGAmBIqK,UArBJ,WAsBMrrB,KAAKgN,eAAgB,EACrBhN,KAAK2d,MAAM,YAAYuJ,iBC9EwT,MCOjV,GAAY,eACd,GACA,GACA,IACA,EACA,KACA,KACA,MAIa,M,QCbfje,aAAIhD,IAAIqlB,QACR,IAAMC,GAAS,CACb,CACEC,KAAM,IACNC,SAAU3c,MAAQU,IAAM,eAAiB,SAE3C,CACEgc,KAAM,QACN9tB,KAAM,OACN8C,UAAWkrB,IAEb,CACEF,KAAM,eACN9tB,KAAM,cACN8C,UAAWuI,KAIT4P,GAAS,IAAI2S,OAAU,CAC3BK,SAAU,eACVJ,YAGa5S,MCzBf1P,aAAIhD,IAAI2lB,QAEO,WAAIA,OAAKC,MAAM,CAC5Bva,MAAO,CACLzQ,kBAAkB,EAClBie,sBAAuB,GACvBlO,OAAQ,GAER0C,cAAe,GAEf0U,YAAa,GAEbzW,cAAc,GAEhBua,UAAW,CACTlrB,uBADS,SACc0Q,EAAOvH,GAC5BuH,EAAMzQ,iBAAmBkJ,GAE3BiV,4BAJS,SAImB1N,EAAOvH,GAC7BuH,EAAMwN,wBAA0B/U,EAClCuH,EAAMwN,sBAAwB,GAG9BxN,EAAMwN,sBAAwB/U,GAGlCgiB,UAZS,SAYCza,EAAOvH,GACfuH,EAAMV,OAAS7G,GAEjBiiB,iBAfS,SAeQ1a,EAAOvH,GACtBuH,EAAMgC,cAAgBvJ,GAExBkiB,eAlBS,SAkBM3a,EAAOvH,GACpBuH,EAAM0W,YAAcje,GAEtBmiB,gBArBS,SAqBO5a,EAAOvH,GACrBuH,EAAMC,aAAexH,IAGzBoiB,QAAS,GAET9vB,QAAS,K,yECxCX4M,aAAImjB,UAAU,WAAY,CACxBC,SAAU,SAAUnE,EAAIoE,GACtB,IACItb,EADJ,kBAAwCsb,EAAQnuB,MAAhD,GAAK6D,EAAL,YAASiK,OAAT,MAAiB,QAAjB,SAA0BwZ,OAA1B,MAAiC,IAAjC,EAEAyC,EAAG/S,iBAAiBlJ,GAAO,WACzB+E,GAASoM,aAAapM,GACtBA,EAAQjK,YAAW,kBAAM/E,MAAMyjB,SAKrCxc,aAAImjB,UAAU,WAAY,CACxBC,SAAU,SAAUnE,EAAIoE,GACtB,IACItb,EAAOub,EADX,kBAAwCD,EAAQnuB,MAAhD,GAAK6D,EAAL,YAASiK,OAAT,MAAiB,QAAjB,SAA0BwZ,OAA1B,MAAiC,IAAjC,EAEAyC,EAAG/S,iBAAiBlJ,GAAO,WACzB,GAAI+E,EAEF,OADAoM,aAAamP,GACNA,EAAYxlB,YAAW,kBAAM/E,MAAMyjB,GAE5CzjB,IACAgP,EAAQjK,YAAW,kBAAMiK,EAAQ,OAAMyU,SCzB7C,IAAI,GAAS,WAAa,IAAI1lB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,WAAWJ,MAAM,CAAC,cAAc,SAAS,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,aAAc,SAAWN,EAAIrC,KAAM,KAAOqC,EAAIgW,YAC3N,GAAkB,GCMtB,IACErY,KAAM,UACNqS,MAAO,CACLrS,KAAM,CACJoJ,KAAM0lB,OACNxc,UAAU,GAEZ+F,MAAO,CACLjP,KAAM0lB,OACNvc,QAAS,MChBkU,MCQ7U,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCLfhH,aAAIzI,UAAU,WAAYisB,IAE1B,IAAMC,GAAiB3pB,UACvB2pB,GAAe7sB,OAAO+R,QAAQ8a,IAE9BzjB,aAAI3D,OAAOqnB,eAAgB,EAC3B1jB,aAAIhD,IAAI2mB,KACR3jB,aAAIjN,UAAUqpB,QAAUwH,KACxBA,KAAOC,OAAO,SACd,IAAI7jB,aAAI,CACN0P,UACAoU,SACAC,OAAQ,SAAAhE,GAAC,OAAIA,EAAEiE,MACdC,OAAO,S,qBC3BV7vB,EAAOD,QAAU,0oB,oCCAjB,W,oCCAA,W,qECAAC,EAAOD,QAAU,8kC,2DCAjB,W,qBCAA,IAAIiC,EAAM,CACT,oBAAqB,OACrB,eAAgB,QAIjB,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOtC,EAAoBuC,GAE5B,SAASC,EAAsBF,GAC9B,IAAItC,EAAoBW,EAAEyB,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAO9D,OAAO8D,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBpC,EAAOD,QAAUkC,EACjBA,EAAeE,GAAK,Q,yDCvBpB,W,yDCAA,W,kCCAA,W,gFCAA,qDAEIL,EAAS,IAAI,IAAa,CAC5B,GAAM,cACN,IAAO,oBACP,QAAW,gBACX,QAAW,mzEAEA,IAAOC,IAAID,GACT,gB,kCCTf,W,yDCAA,W,4CCAA,IAAIE,EAAM,CACT,qBAAsB,OACtB,yBAA0B,OAC1B,yBAA0B,OAC1B,uBAAwB,OACxB,6BAA8B,QAI/B,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOtC,EAAoBuC,GAE5B,SAASC,EAAsBF,GAC9B,IAAItC,EAAoBW,EAAEyB,EAAKE,GAAM,CACpC,IAAIG,EAAI,IAAIC,MAAM,uBAAyBJ,EAAM,KAEjD,MADAG,EAAEE,KAAO,mBACHF,EAEP,OAAOL,EAAIE,GAEZD,EAAeO,KAAO,WACrB,OAAO9D,OAAO8D,KAAKR,IAEpBC,EAAeQ,QAAUL,EACzBpC,EAAOD,QAAUkC,EACjBA,EAAeE,GAAK,Q,mBC1BpBnC,EAAOD,QAAU,snD,0CCAjBC,EAAOD,QAAU,89E,0CCAjBC,EAAOD,QAAU,szB,0CCAjBC,EAAOD,QAAU,suC,kCCAjB,W,mBCAAC,EAAOD,QAAU", "file": "js/app.bfc4b496.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./component-log.vue?vue&type=style&index=0&id=5631bc13&lang=scss&scoped=true&\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&lang=scss&\"", "import SpriteSymbol from \"../../../node_modules/svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-auto_layout\",\n  \"use\": \"icon-auto_layout-usage\",\n  \"viewBox\": \"0 0 1024 1024\",\n  \"content\": \"<symbol class=\\\"icon\\\" viewBox=\\\"0 0 1024 1024\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" id=\\\"icon-auto_layout\\\"><defs><style type=\\\"text/css\\\">@font-face { font-family: feedback-iconfont; src: url(\\\"//at.alicdn.com/t/font_1031158_u69w8yhxdu.woff2?t=1630033759944\\\") format(\\\"woff2\\\"), url(\\\"//at.alicdn.com/t/font_1031158_u69w8yhxdu.woff?t=1630033759944\\\") format(\\\"woff\\\"), url(\\\"//at.alicdn.com/t/font_1031158_u69w8yhxdu.ttf?t=1630033759944\\\") format(\\\"truetype\\\"); }\\n</style></defs><path d=\\\"M388.7 542.88c-16.57 0-30-13.43-30-30s13.43-30 30-30c52.3 0 94.85-42.55 94.85-94.85v-67.81c0-40.96 15.84-79.58 44.6-108.74 28.76-29.16 67.16-45.53 108.12-46.1l3.43-0.05c16.57-0.22 30.18 13.02 30.41 29.58 0.23 16.57-13.02 30.18-29.58 30.41l-3.43 0.05c-51.58 0.71-93.55 43.25-93.55 94.84v67.81c0 85.4-69.47 154.86-154.85 154.86z\\\" p-id=\\\"19453\\\" /><path d=\\\"M640.12 860.42h-0.42l-3.43-0.05c-40.96-0.56-79.36-16.93-108.12-46.09s-44.6-67.78-44.6-108.74v-67.8c0-52.3-42.55-94.85-94.85-94.85-16.57 0-30-13.43-30-30s13.43-30 30-30c85.38 0 154.85 69.47 154.85 154.85v67.8c0 51.59 41.96 94.13 93.55 94.84l3.43 0.05c16.57 0.23 29.81 13.84 29.59 30.41-0.24 16.42-13.62 29.58-30 29.58z\\\" p-id=\\\"19454\\\" /><path d=\\\"M640.11 542.88H388.7c-16.57 0-30-13.43-30-30s13.43-30 30-30h251.42c16.57 0 30 13.43 30 30-0.01 16.57-13.44 30-30.01 30z\\\" p-id=\\\"19455\\\" /><path d=\\\"M343.89 638.95H137.78c-38.6 0-70-31.4-70-70V456.81c0-38.6 31.4-70 70-70h206.11c38.6 0 70 31.4 70 70v112.13c0 38.6-31.4 70.01-70 70.01zM137.78 446.81c-5.51 0-10 4.49-10 10v112.13c0 5.51 4.49 10 10 10h206.11c5.51 0 10-4.49 10-10V456.81c0-5.51-4.49-10-10-10H137.78zM830.16 316.96h-93.98c-69.51 0-126.07-56.55-126.07-126.07S666.66 64.83 736.18 64.83h93.98c69.51 0 126.07 56.55 126.07 126.07-0.01 69.5-56.56 126.06-126.07 126.06z m-93.98-192.13c-36.43 0-66.07 29.64-66.07 66.07s29.64 66.07 66.07 66.07h93.98c36.43 0 66.07-29.64 66.07-66.07s-29.64-66.07-66.07-66.07h-93.98zM830.16 638.95h-93.98c-69.51 0-126.07-56.55-126.07-126.07 0-69.51 56.55-126.07 126.07-126.07h93.98c69.51 0 126.07 56.55 126.07 126.07-0.01 69.51-56.56 126.07-126.07 126.07z m-93.98-192.14c-36.43 0-66.07 29.64-66.07 66.07 0 36.43 29.64 66.07 66.07 66.07h93.98c36.43 0 66.07-29.64 66.07-66.07 0-36.43-29.64-66.07-66.07-66.07h-93.98z\\\" p-id=\\\"19456\\\" /><path d=\\\"M830.16 959.17h-93.98c-69.51 0-126.07-56.55-126.07-126.07s56.55-126.07 126.07-126.07h93.98c69.51 0 126.07 56.55 126.07 126.07s-56.56 126.07-126.07 126.07z m-93.98-192.13c-36.43 0-66.07 29.64-66.07 66.07s29.64 66.07 66.07 66.07h93.98c36.43 0 66.07-29.64 66.07-66.07s-29.64-66.07-66.07-66.07h-93.98z\\\" p-id=\\\"19457\\\" /></symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-bar.vue?vue&type=style&index=0&id=ba0de2ca&lang=scss&scoped=true&\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./nodeSelector.vue?vue&type=style&index=0&id=6742ba06&lang=scss&scoped=true&\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./contextMenu.vue?vue&type=style&index=0&id=07458d63&lang=scss&scoped=true&\"", "var map = {\n\t\"./af\": \"2bfb\",\n\t\"./af.js\": \"2bfb\",\n\t\"./ar\": \"8e73\",\n\t\"./ar-dz\": \"a356\",\n\t\"./ar-dz.js\": \"a356\",\n\t\"./ar-kw\": \"423e\",\n\t\"./ar-kw.js\": \"423e\",\n\t\"./ar-ly\": \"1cfd\",\n\t\"./ar-ly.js\": \"1cfd\",\n\t\"./ar-ma\": \"0a84\",\n\t\"./ar-ma.js\": \"0a84\",\n\t\"./ar-sa\": \"8230\",\n\t\"./ar-sa.js\": \"8230\",\n\t\"./ar-tn\": \"6d83\",\n\t\"./ar-tn.js\": \"6d83\",\n\t\"./ar.js\": \"8e73\",\n\t\"./az\": \"485c\",\n\t\"./az.js\": \"485c\",\n\t\"./be\": \"1fc1\",\n\t\"./be.js\": \"1fc1\",\n\t\"./bg\": \"84aa\",\n\t\"./bg.js\": \"84aa\",\n\t\"./bm\": \"a7fa\",\n\t\"./bm.js\": \"a7fa\",\n\t\"./bn\": \"9043\",\n\t\"./bn-bd\": \"9686\",\n\t\"./bn-bd.js\": \"9686\",\n\t\"./bn.js\": \"9043\",\n\t\"./bo\": \"d26a\",\n\t\"./bo.js\": \"d26a\",\n\t\"./br\": \"6887\",\n\t\"./br.js\": \"6887\",\n\t\"./bs\": \"2554\",\n\t\"./bs.js\": \"2554\",\n\t\"./ca\": \"d716\",\n\t\"./ca.js\": \"d716\",\n\t\"./cs\": \"3c0d\",\n\t\"./cs.js\": \"3c0d\",\n\t\"./cv\": \"03ec\",\n\t\"./cv.js\": \"03ec\",\n\t\"./cy\": \"9797\",\n\t\"./cy.js\": \"9797\",\n\t\"./da\": \"0f14\",\n\t\"./da.js\": \"0f14\",\n\t\"./de\": \"b469\",\n\t\"./de-at\": \"b3eb\",\n\t\"./de-at.js\": \"b3eb\",\n\t\"./de-ch\": \"bb71\",\n\t\"./de-ch.js\": \"bb71\",\n\t\"./de.js\": \"b469\",\n\t\"./dv\": \"598a\",\n\t\"./dv.js\": \"598a\",\n\t\"./el\": \"8d47\",\n\t\"./el.js\": \"8d47\",\n\t\"./en-au\": \"0e6b\",\n\t\"./en-au.js\": \"0e6b\",\n\t\"./en-ca\": \"3886\",\n\t\"./en-ca.js\": \"3886\",\n\t\"./en-gb\": \"39a6\",\n\t\"./en-gb.js\": \"39a6\",\n\t\"./en-ie\": \"e1d3b\",\n\t\"./en-ie.js\": \"e1d3b\",\n\t\"./en-il\": \"7333\",\n\t\"./en-il.js\": \"7333\",\n\t\"./en-in\": \"ec2e\",\n\t\"./en-in.js\": \"ec2e\",\n\t\"./en-nz\": \"6f50\",\n\t\"./en-nz.js\": \"6f50\",\n\t\"./en-sg\": \"b7e9\",\n\t\"./en-sg.js\": \"b7e9\",\n\t\"./eo\": \"65db\",\n\t\"./eo.js\": \"65db\",\n\t\"./es\": \"898b\",\n\t\"./es-do\": \"0a3c\",\n\t\"./es-do.js\": \"0a3c\",\n\t\"./es-mx\": \"b5b7\",\n\t\"./es-mx.js\": \"b5b7\",\n\t\"./es-us\": \"55c9\",\n\t\"./es-us.js\": \"55c9\",\n\t\"./es.js\": \"898b\",\n\t\"./et\": \"ec18\",\n\t\"./et.js\": \"ec18\",\n\t\"./eu\": \"0ff2\",\n\t\"./eu.js\": \"0ff2\",\n\t\"./fa\": \"8df4\",\n\t\"./fa.js\": \"8df4\",\n\t\"./fi\": \"81e9\",\n\t\"./fi.js\": \"81e9\",\n\t\"./fil\": \"d69a\",\n\t\"./fil.js\": \"d69a\",\n\t\"./fo\": \"0721\",\n\t\"./fo.js\": \"0721\",\n\t\"./fr\": \"9f26\",\n\t\"./fr-ca\": \"d9f8\",\n\t\"./fr-ca.js\": \"d9f8\",\n\t\"./fr-ch\": \"0e49\",\n\t\"./fr-ch.js\": \"0e49\",\n\t\"./fr.js\": \"9f26\",\n\t\"./fy\": \"7118\",\n\t\"./fy.js\": \"7118\",\n\t\"./ga\": \"5120\",\n\t\"./ga.js\": \"5120\",\n\t\"./gd\": \"f6b4\",\n\t\"./gd.js\": \"f6b4\",\n\t\"./gl\": \"8840\",\n\t\"./gl.js\": \"8840\",\n\t\"./gom-deva\": \"aaf2\",\n\t\"./gom-deva.js\": \"aaf2\",\n\t\"./gom-latn\": \"0caa\",\n\t\"./gom-latn.js\": \"0caa\",\n\t\"./gu\": \"e0c5\",\n\t\"./gu.js\": \"e0c5\",\n\t\"./he\": \"c7aa\",\n\t\"./he.js\": \"c7aa\",\n\t\"./hi\": \"dc4d\",\n\t\"./hi.js\": \"dc4d\",\n\t\"./hr\": \"4ba9\",\n\t\"./hr.js\": \"4ba9\",\n\t\"./hu\": \"5b14\",\n\t\"./hu.js\": \"5b14\",\n\t\"./hy-am\": \"d6b6\",\n\t\"./hy-am.js\": \"d6b6\",\n\t\"./id\": \"5038\",\n\t\"./id.js\": \"5038\",\n\t\"./is\": \"0558\",\n\t\"./is.js\": \"0558\",\n\t\"./it\": \"6e98\",\n\t\"./it-ch\": \"6f12\",\n\t\"./it-ch.js\": \"6f12\",\n\t\"./it.js\": \"6e98\",\n\t\"./ja\": \"079e\",\n\t\"./ja.js\": \"079e\",\n\t\"./jv\": \"b540\",\n\t\"./jv.js\": \"b540\",\n\t\"./ka\": \"201b\",\n\t\"./ka.js\": \"201b\",\n\t\"./kk\": \"6d79\",\n\t\"./kk.js\": \"6d79\",\n\t\"./km\": \"e81d\",\n\t\"./km.js\": \"e81d\",\n\t\"./kn\": \"3e92\",\n\t\"./kn.js\": \"3e92\",\n\t\"./ko\": \"22f8\",\n\t\"./ko.js\": \"22f8\",\n\t\"./ku\": \"2421\",\n\t\"./ku.js\": \"2421\",\n\t\"./ky\": \"9609\",\n\t\"./ky.js\": \"9609\",\n\t\"./lb\": \"440c\",\n\t\"./lb.js\": \"440c\",\n\t\"./lo\": \"b29d\",\n\t\"./lo.js\": \"b29d\",\n\t\"./lt\": \"26f9\",\n\t\"./lt.js\": \"26f9\",\n\t\"./lv\": \"b97c\",\n\t\"./lv.js\": \"b97c\",\n\t\"./me\": \"293c\",\n\t\"./me.js\": \"293c\",\n\t\"./mi\": \"688b\",\n\t\"./mi.js\": \"688b\",\n\t\"./mk\": \"6909\",\n\t\"./mk.js\": \"6909\",\n\t\"./ml\": \"02fb\",\n\t\"./ml.js\": \"02fb\",\n\t\"./mn\": \"958b\",\n\t\"./mn.js\": \"958b\",\n\t\"./mr\": \"39bd\",\n\t\"./mr.js\": \"39bd\",\n\t\"./ms\": \"ebe4\",\n\t\"./ms-my\": \"6403\",\n\t\"./ms-my.js\": \"6403\",\n\t\"./ms.js\": \"ebe4\",\n\t\"./mt\": \"1b45\",\n\t\"./mt.js\": \"1b45\",\n\t\"./my\": \"8689\",\n\t\"./my.js\": \"8689\",\n\t\"./nb\": \"6ce3\",\n\t\"./nb.js\": \"6ce3\",\n\t\"./ne\": \"3a39\",\n\t\"./ne.js\": \"3a39\",\n\t\"./nl\": \"facd\",\n\t\"./nl-be\": \"db29\",\n\t\"./nl-be.js\": \"db29\",\n\t\"./nl.js\": \"facd\",\n\t\"./nn\": \"b84c\",\n\t\"./nn.js\": \"b84c\",\n\t\"./oc-lnc\": \"167b\",\n\t\"./oc-lnc.js\": \"167b\",\n\t\"./pa-in\": \"f3ff\",\n\t\"./pa-in.js\": \"f3ff\",\n\t\"./pl\": \"8d57\",\n\t\"./pl.js\": \"8d57\",\n\t\"./pt\": \"f260\",\n\t\"./pt-br\": \"d2d4\",\n\t\"./pt-br.js\": \"d2d4\",\n\t\"./pt.js\": \"f260\",\n\t\"./ro\": \"972c\",\n\t\"./ro.js\": \"972c\",\n\t\"./ru\": \"957c\",\n\t\"./ru.js\": \"957c\",\n\t\"./sd\": \"6784\",\n\t\"./sd.js\": \"6784\",\n\t\"./se\": \"ffff\",\n\t\"./se.js\": \"ffff\",\n\t\"./si\": \"eda5\",\n\t\"./si.js\": \"eda5\",\n\t\"./sk\": \"7be6\",\n\t\"./sk.js\": \"7be6\",\n\t\"./sl\": \"8155\",\n\t\"./sl.js\": \"8155\",\n\t\"./sq\": \"c8f3\",\n\t\"./sq.js\": \"c8f3\",\n\t\"./sr\": \"cf1e\",\n\t\"./sr-cyrl\": \"13e9\",\n\t\"./sr-cyrl.js\": \"13e9\",\n\t\"./sr.js\": \"cf1e\",\n\t\"./ss\": \"52bd\",\n\t\"./ss.js\": \"52bd\",\n\t\"./sv\": \"5fbd\",\n\t\"./sv.js\": \"5fbd\",\n\t\"./sw\": \"74dc\",\n\t\"./sw.js\": \"74dc\",\n\t\"./ta\": \"3de5\",\n\t\"./ta.js\": \"3de5\",\n\t\"./te\": \"5cbb\",\n\t\"./te.js\": \"5cbb\",\n\t\"./tet\": \"576c\",\n\t\"./tet.js\": \"576c\",\n\t\"./tg\": \"3b1b\",\n\t\"./tg.js\": \"3b1b\",\n\t\"./th\": \"10e8\",\n\t\"./th.js\": \"10e8\",\n\t\"./tk\": \"5aff\",\n\t\"./tk.js\": \"5aff\",\n\t\"./tl-ph\": \"0f38\",\n\t\"./tl-ph.js\": \"0f38\",\n\t\"./tlh\": \"cf75\",\n\t\"./tlh.js\": \"cf75\",\n\t\"./tr\": \"0e81\",\n\t\"./tr.js\": \"0e81\",\n\t\"./tzl\": \"cf51\",\n\t\"./tzl.js\": \"cf51\",\n\t\"./tzm\": \"c109\",\n\t\"./tzm-latn\": \"b53d\",\n\t\"./tzm-latn.js\": \"b53d\",\n\t\"./tzm.js\": \"c109\",\n\t\"./ug-cn\": \"6117\",\n\t\"./ug-cn.js\": \"6117\",\n\t\"./uk\": \"ada2\",\n\t\"./uk.js\": \"ada2\",\n\t\"./ur\": \"5294\",\n\t\"./ur.js\": \"5294\",\n\t\"./uz\": \"2e8c\",\n\t\"./uz-latn\": \"010e\",\n\t\"./uz-latn.js\": \"010e\",\n\t\"./uz.js\": \"2e8c\",\n\t\"./vi\": \"2921\",\n\t\"./vi.js\": \"2921\",\n\t\"./x-pseudo\": \"fd7e\",\n\t\"./x-pseudo.js\": \"fd7e\",\n\t\"./yo\": \"7f33\",\n\t\"./yo.js\": \"7f33\",\n\t\"./zh-cn\": \"5c3a\",\n\t\"./zh-cn.js\": \"5c3a\",\n\t\"./zh-hk\": \"49ab\",\n\t\"./zh-hk.js\": \"49ab\",\n\t\"./zh-mo\": \"3a6c\",\n\t\"./zh-mo.js\": \"3a6c\",\n\t\"./zh-tw\": \"90ea\",\n\t\"./zh-tw.js\": \"90ea\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"4678\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-bar.vue?vue&type=style&index=0&id=bee2de5a&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=b066cb5a&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"home\"},[_c('menuBar'),_c('div',{staticClass:\"flex-wrapper\",attrs:{\"id\":\"canvas-wrapper\"}},[_c('nodeSelector'),_c('canvasToolbox'),_c('graphCanvas'),_c('rightConfigBar'),_c('rightConfigResource'),_c('rightConfigDispatch'),_c('logBar')],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"menu-bar\"},[_c('div',{staticClass:\"node-selector-toggle\",on:{\"click\":function($event){return _vm.nodeSelectorHideToggle(!_vm.nodeSelectorHide)}}},[_c('span',{staticClass:\"selector-btn-text\"},[_vm._v(\"组件库\")]),_c('em',{staticClass:\"el-icon-arrow-up selector-btn-icon\",class:{ 'selector-hide': _vm.nodeSelectorHide }})]),_c('div',{staticClass:\"menu-buttons\"},[_c('a',{staticClass:\"menu-btn\",on:{\"click\":_vm.refresh}},[_c('em',{staticClass:\"menu-icon menu-icon-refresh\"}),_vm._v(\" \"),_c('span',[_vm._v(\"刷新\")])]),_c('a',{staticClass:\"menu-btn\",on:{\"click\":_vm.processValid}},[_c('em',{staticClass:\"menu-icon menu-icon-valid\"}),_vm._v(\" \"),_c('span',[_vm._v(\"流程校验\")])]),_c('a',{staticClass:\"menu-btn\",on:{\"click\":function($event){return _vm.handleSave()}}},[_c('em',{staticClass:\"menu-icon menu-icon-draft\"}),_vm._v(\" \"),_c('span',[_vm._v(\"保存草稿\")])]),_c('a',{staticClass:\"menu-btn\",on:{\"click\":_vm.handleExecute}},[_c('em',{staticClass:\"menu-icon menu-icon-run\"}),_vm._v(\" \"),_c('span',[_vm._v(\"立即运行\")])]),(_vm.workflowStatus != 'SCHEDULING' && _vm.workflowStatus != 'SCHEDULING_WAIT')?[_c('a',{staticClass:\"menu-btn\",on:{\"click\":_vm.handleSchedule}},[_c('em',{staticClass:\"menu-icon menu-icon-scheduling\"}),_c('span',[_vm._v(\"执行调度\")])])]:_vm._e(),(_vm.workflowStatus == 'SCHEDULING' || _vm.workflowStatus == 'SCHEDULING_WAIT')?[_c('a',{staticClass:\"menu-btn\",on:{\"click\":_vm.handleFreeze}},[_c('em',{staticClass:\"menu-icon menu-icon-scheduling\"}),_vm._v(\" \"),_c('span',[_vm._v(\"停止调度\")])])]:_vm._e()],2),_c('el-dialog',{attrs:{\"title\":\"流程校验\",\"custom-class\":\"dialog-form\",\"visible\":_vm.validDialog,\"width\":\"648px\",\"close-on-click-modal\":false,\"close-on-press-escape\":false},on:{\"update:visible\":function($event){_vm.validDialog=$event},\"close\":_vm.validDialogClose}},[_c('div',[_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"未连线组件\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.unConnectModules.length ? 'el-icon-warning' : 'el-icon-success'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.unConnectModules.length ? _vm.validOption.unConnectModules.length + '项问题' : '通过验证'))])])]),(_vm.validOption.unConnectModules.length)?_c('div',[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"max-height\":\"240\",\"data\":_vm.validOption.unConnectModules,\"header-cell-style\":{ 'background-color': '#F5F7FA', 'color': '#909399' }}},[_c('el-table-column',{attrs:{\"label\":\"节点名称\",\"prop\":\"name\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handlePosition(scope.row)}}},[_vm._v(\"定位\")])]}}],null,false,3782961849)})],1)],1):_vm._e()]),_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"未配置组件\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.unConfigModules.length ? 'el-icon-warning' : 'el-icon-success'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.unConfigModules.length ? _vm.validOption.unConfigModules.length + '项问题' : '通过验证'))])])]),(_vm.validOption.unConfigModules.length)?_c('div',[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"max-height\":\"240\",\"data\":_vm.validOption.unConfigModules,\"header-cell-style\":{ 'background-color': '#F5F7FA', 'color': '#909399' }}},[_c('el-table-column',{attrs:{\"label\":\"节点名称\",\"prop\":\"name\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.handlePosition(scope.row)}}},[_vm._v(\"定位\")])]}}],null,false,3782961849)})],1)],1):_vm._e()]),_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_c('span',[_vm._v(\"唯一流程校验\")]),_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"dark\",\"content\":\"流程中只允许出现一条完整的流程，不允许出现一条以上无交集的子流程\",\"placement\":\"top\"}},[_c('em',{staticClass:\"el-icon-info\"})])],1),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.trackUnique ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.trackUnique ? '通过验证' : '未通过验证'))])])])]),_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"首节点为开始节点\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.startNodeIsValid ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.startNodeIsValid ? '通过验证' : '未通过验证'))])])])]),_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"尾节点为结束节点\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.finishNodeIsValid ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.finishNodeIsValid ? '通过验证' : '未通过验证'))])])])])]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.validDialog = false}}},[_vm._v(\"关 闭\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"流程监测结果\",\"custom-class\":\"dialog-form\",\"visible\":_vm.monitorDialog,\"width\":\"648px\",\"close-on-click-modal\":false,\"close-on-press-escape\":false},on:{\"update:visible\":function($event){_vm.monitorDialog=$event}}},[_c('div',[_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"流程配置校验\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.processConfigVaild ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.processConfigVaild ? '通过验证' : '未通过验证'))])])]),(!_vm.processConfigVaild)?_c('div',{staticStyle:{\"white-space\":\"normal\"}},[_vm._v(\" 检测到当前流程未配置完整，请先将流程配置完整。\"),_c('br'),_vm._v(\"点击流程控制区【流程校验】可查看详细问题。 \")]):_vm._e()]),_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"资源配置校验\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.executeParamConfigured ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.executeParamConfigured ? '通过验证' : '未通过验证'))])])]),(!_vm.validOption.executeParamConfigured)?_c('div',{staticStyle:{\"white-space\":\"normal\"}},[_vm._v(\" 检测到当前流程未配置运行资源，请先到控制台右侧【资源配置】配置流程运行资源。 \")]):_vm._e()]),(!_vm.isNow)?_c('div',{staticClass:\"itemStyle\"},[_c('div',{staticClass:\"headStyle\"},[_c('div',{staticClass:\"validTitle\"},[_vm._v(\"调度配置校验\")]),_c('div',{staticClass:\"headRight\"},[_c('em',{class:_vm.validOption.scheduleConfigured ? 'el-icon-success' : 'el-icon-warning'}),_vm._v(\" \"),_c('span',[_vm._v(_vm._s(_vm.validOption.scheduleConfigured ? '通过验证' : '未通过验证'))])])]),(!_vm.validOption.scheduleConfigured)?_c('div',{staticStyle:{\"white-space\":\"normal\"}},[_vm._v(\" 检测到当前流程未配置调度信息，请先到控制台右侧【调度配置】配置流程调度信息 \")]):_vm._e()]):_vm._e()]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.monitorDialog = false}}},[_vm._v(\"关 闭\")])],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "/**\r\n * 敏感接口请求参数、响应数据加密\r\n */\r\n const CryptoJS = require('crypto-js');\r\n\r\n\r\n const EncryptionDecrypt = {\r\n   // 加密配置项\r\n   option: {\r\n     mode: CryptoJS.mode.ECB,\r\n     padding: CryptoJS.pad.Pkcs7\r\n   },\r\n \r\n   // 敏感接口黑名单\r\n   sensitiveAPIs: [\r\n     '/resources'\r\n   ],\r\n \r\n \r\n   // 检测接口是否需要加密\r\n   checkAPI(url) {\r\n     return EncryptionDecrypt.sensitiveAPIs.some(item => url.includes(item))\r\n   },\r\n \r\n   // 获取加密Key\r\n   getKey(timestamp) {\r\n     return CryptoJS.enc.Utf8.parse('000' + timestamp);\r\n   },\r\n \r\n   // 加密处理\r\n   encryptionHandle(sensitiveValue, timestamp) {\r\n     const ciphertext = CryptoJS.AES.encrypt(sensitiveValue, EncryptionDecrypt.getKey(timestamp), EncryptionDecrypt.option)\r\n     return ciphertext.toString();\r\n   },\r\n \r\n   // 解密处理\r\n   decryptionHandle(sensitiveValue, timestamp) {\r\n     const bytes = CryptoJS.AES.decrypt(sensitiveValue, EncryptionDecrypt.getKey(timestamp), EncryptionDecrypt.option)\r\n     return CryptoJS.enc.Utf8.stringify(bytes).toString()\r\n   },\r\n \r\n   // 加密 data: 加密数据对象， options请求参数\r\n   encryption(options) {\r\n     const { params, data, url } = options;\r\n     if((!params && !data) || !this.checkAPI(url)) return\r\n     if(!options.headers) options.headers = {'Content-Type': 'application/json; charset=utf-8'};\r\n     options.headers.timestamp = new Date().getTime()\r\n     // get请求不加密入参\r\n     if(options.method === 'get') return\r\n     const encrypteData = this.encryptionHandle(JSON.stringify(data), options.headers.timestamp)\r\n     if(encrypteData) params ? options.params = encrypteData: options.data = encrypteData\r\n     // console.log('加密：', url, encrypteData);\r\n   },\r\n \r\n   // 解密 data: 加密数据对象， timestamp请求头时间戳\r\n   decryption(response) {\r\n     const { data, config } = response\r\n     const timestamp = config?.headers?.timestamp\r\n     if(!timestamp || !this.checkAPI(config.url) || !data?.result) return\r\n     const decrypteData = this.decryptionHandle(data.result, timestamp)\r\n     if(decrypteData) response.data.data = JSON.parse(decrypteData)\r\n     // console.log('解密：', config.url, JSON.parse(decrypteData));\r\n   },\r\n }\r\n \r\n export default EncryptionDecrypt", "import axios from 'axios'\r\nimport EncryptionDecrypt from '@/libs/encryption-decrypt'\r\n// import store from '../store'\r\nimport {\r\n    // MessageBox,\r\n    Message\r\n} from 'element-ui'\r\n// 创建axios实例\r\nconst service = axios.create({\r\n    // baseURL: process.env.VUE_APP_URL, // api 的 base_url\r\n    // baseURL: window.base_url,\r\n    timeout: 10000 // 请求超时时间\r\n})\r\n// console.log(\"token:\"+dataOsToken)\r\nservice.defaults.headers = {\r\n    // 'Content-Type': 'application/x-www-form-urlencoded'\r\n    'Content-Type': 'application/json; charset=utf-8',\r\n    accessToken: window.dataOsToken,\r\n    applicationCode: window.applicationCode\r\n}\r\n\r\n// request拦截器\r\nservice.interceptors.request.use(\r\n    config => {\r\n        // 敏感字段加密处理\r\n        EncryptionDecrypt.encryption(config)\r\n        config.headers?.timestamp || new Date().getTime()\r\n        // if(localStorage.accessToken){\r\n        //   config.headers['Authorization'] = localStorage.accessToken\r\n        // }\r\n        // if (store.getters.token) {\r\n        //   config.headers['X-Token'] = store.getters.token\r\n        // }\r\n        // config.headers['X-Token'] = 'a2619fc5cb4b4cde9f25911873f37536'\r\n        return config\r\n    },\r\n    error => {\r\n        console.log(error)\r\n        Promise.reject(error)\r\n    }\r\n)\r\n\r\nvar showModal = true // 控制弹窗显示\r\n\r\nfunction showMessage(msg) {\r\n    if (showModal) {\r\n        showModal = false\r\n        Message({\r\n            showClose: true,\r\n            dangerouslyUseHTMLString: true,\r\n            message: '<p style=\"max-height:350px; word-wrap: break-word;word-break: break-all;overflow: hidden;\">' + msg + '</p>',\r\n            type: 'error'\r\n        })\r\n        setTimeout(function () {\r\n            showModal = true\r\n        }, 1000)\r\n        // MessageBox.alert(msg, '提示', {\r\n        //     type: \"warning\",\r\n        //     dangerouslyUseHTMLString: true,\r\n        //     message: '<pre style=\"max-height:350px;overflow-y: auto;\">' + msg + '</pre>',\r\n        //     customClass: 'customClass',\r\n        //     callback: () => {\r\n        //         // 清除失效token\r\n        //         removeToken();\r\n        //         //跳转登录页\r\n        //         router.push({\r\n        //             path: '/login',\r\n        //             query: {\r\n        //                 redirect: location.hostname\r\n        //             }\r\n        //         })\r\n        //         showModal = true\r\n        //     }\r\n        // });\r\n    }\r\n}\r\n\r\nservice.interceptors.response.use(\r\n    response => {\r\n        // 敏感字段加密处理\r\n        if(response.data) EncryptionDecrypt.decryption(response)\r\n        // ...请求成功后的后续操作\r\n        const res = response.data\r\n        if (!res.code) {\r\n            return res\r\n        } else {\r\n            if (res.code == 11030113) {\r\n                showMessage(\"登录信息已过期,请重新登录\")\r\n            }\r\n            if (res.code !== 200) {\r\n\r\n                if (document.querySelectorAll('.el-message--success').length > 0) {\r\n                    return false\r\n                }\r\n                showMessage(res.message)\r\n            }\r\n            return res\r\n        }\r\n    },\r\n    err => {\r\n        if (err.message.indexOf('timeout') != -1) {\r\n            showMessage(\"请求超时，请稍后重试\")\r\n        } else {\r\n            if (err.response || err.message) {\r\n                if (document.querySelectorAll('.el-message--success').length > 0) {\r\n                    return false\r\n                }\r\n                showMessage(err.response.data.message || err.message)\r\n            }\r\n        }\r\n        return Promise.reject(err);\r\n    }\r\n)\r\n\r\nexport default service", "import feach from '../libs/feach'\r\n\r\nconst baseUrl = window.applicationServerPath\r\nconst dataOsUrl = window.dataos_urlDaasMeta\r\nconst jobUrl = window.executorgover\r\n\r\n\r\n\r\n// 流程完整性校验\r\nexport function integrityValidate(workflowId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/integrity_validate`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n// 获取流程运行状态\r\nexport function getStatus(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}/state`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n// 获取流程信息\r\nexport function getWorkflowInfo(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n\r\n// 保存流程/保存草稿\r\nexport function saveWorkflow(workflowId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/save_to_draft`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 执行流程\r\nexport function executeWorkflow(data) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${data.workflowId}/execute`,\r\n    method: 'post',\r\n    // data\r\n  })\r\n}\r\n\r\n// 执行调度\r\nexport function scheduleWorkflow(data) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${data.workflowId}/schedules/trigger`,\r\n    method: 'post',\r\n    // data\r\n  })\r\n}\r\n\r\n// 停止调度 \r\nexport function freezeWorkflow(data) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${data.workflowId}/schedules/${data.scheduleId}/stop`,\r\n    method: 'post',\r\n    // data\r\n  })\r\n}\r\n\r\n\r\n// 获取组件配置信息 \r\nexport function queryPreviousAndSelfParam(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/modules/function_param',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n//\r\nexport function updateFunctionParam(data, moduleId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/modules/${moduleId}/function_param/update`,\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function updateFunctionParamClear(moduleId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/modules/${moduleId}/function_param/update`,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 组件信息\r\nexport function getApplication(params) {\r\n  return feach({\r\n    // url: 'http://testbuild.youedata.cc/shujuzhili/fanso-governance-task' + '/api/v2/application/queryApplicationListAllInfo',\r\n    url: baseUrl + `/api/v2/modules/group_by_category`,\r\n    method: 'get',\r\n    params,\r\n  })\r\n}\r\n\r\n// 查询流程配置\r\nexport function getWorkflow(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.id}/configs`,\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 删除连线信息\r\nexport function deleteConnInfo(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/line/delete',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 删除工作流中的组件\r\nexport function deleteNode(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/modules/${params.moduleId}/delete`,\r\n    method: 'post',\r\n    // params\r\n  })\r\n}\r\n\r\n// 更新组件自定义名称\r\nexport function updateName(data, moduleId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/modules/${moduleId}/update`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 拉取组件(新增)\r\nexport function nodeAdd(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/modules',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 新增连线信息\r\nexport function edgeAdd(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/line',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新组件位置信息\r\nexport function updatePosition(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/modules/position/update',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新组件位置信息\r\nexport function nodeCopy(params, data) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/modules/${params.id}/copy`,\r\n    method: 'post',\r\n    // params,\r\n    data\r\n  })\r\n}\r\n\r\n// 更新组件位置信息\r\nexport function workflowAdd(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/workflows',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// 任务资源配置-新增/修改\r\nexport function updateTaskRe(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/updateTaskRe',\r\n    method: 'post',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 任务资源配置-保存/保存为草稿\r\nexport function saveTaskData(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/updateTaskRe9',\r\n    method: 'post',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 获取数据资源树第一层\r\nexport function getResourceTree() {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/mainMenu',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取数据中心应用子菜单\r\nexport function getAppSubMenu(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/appMenu',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心应用子菜单\r\nexport function getResourceTreeDb(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/dbMenu',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心文件子菜单\r\nexport function getResourceTreeFile(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/fileMenu',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n// 获取数据中心数据源列表\r\nexport function getResourceSourceList(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/sourceList',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心应用列表\r\nexport function getResourceAppList(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/appList',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心数据库列表\r\nexport function getResourceBaseList(params) {\r\n  return feach({\r\n    // /api/v2/daas/meta/dataCenter/baseList\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenterApi/baseList',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心数据库表列表\r\nexport function getResourceTableList(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/tableList',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取数据中心文件列表\r\nexport function getResourceFileList(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/dataCenter/fileList',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取表结构\r\nexport function getListMetaData(params) {\r\n  return feach({\r\n    url: dataOsUrl + '/api/v2/daas/meta/listMetaData',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\nexport function storage(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/storage/storage',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n// 任务资源配置-组件-新增/修改\r\nexport function reqUpdateTask(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/updateTaskReProcDef',\r\n    method: 'POST',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 任务资源配置-删除功能组件\r\nexport function reqDeleteTask(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/deleteTaskReProcDef',\r\n    method: 'POST',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 任务资源配置-更新连接信息\r\nexport function updataConnlist(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/updateConnlist',\r\n    method: 'POST',\r\n    data: params\r\n  })\r\n}\r\n\r\n// 任务资源配置-数据初始化\r\nexport function initTaskReProcDef(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskre/initTaskReProcDef',\r\n    method: 'POST',\r\n    data: params\r\n  })\r\n}\r\n\r\n// POST /api/v2/taskrunlog/findLog  控制台\r\nexport function findLog(data) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskruninglog/findLog',\r\n    method: 'POST',\r\n    data: data\r\n  })\r\n}\r\n\r\n// POST /api/v2/taskrunlog/findLogInfo\r\nexport function findTaskExecInfo(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskruninglog/findTaskExecInfo',\r\n    method: 'POST',\r\n    params\r\n  })\r\n}\r\n\r\n// 通过任务名称查询是否有该任务\r\nexport function getTaskByIdInfo(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/task/getTaskById',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 指数立即运行的状态\r\nexport function runImmediatelyStatus(params) {\r\n  return feach({\r\n    url: jobUrl + '/api/v2/runImmediately/runImmediately',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取各节点运行状态\r\nexport function getNodeRuningStatus(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/taskruninglog/infos',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取字段类型转换规则\r\nexport function getDictionary(params) {\r\n  return feach({\r\n    url: `${baseUrl}/api/v2/application/dictionary`,\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 任务资源配置-获取未保存的组件状态\r\nexport function getState(params) {\r\n  return feach({\r\n    url: `${baseUrl}/api/v2/taskre/getState`,\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// export function reqFindLog(params) {\r\n//     return feach({\r\n//         url: baseUrl + '/taskrunlog/findLog',\r\n//         method: 'POST',\r\n//         data: params\r\n//     })\r\n// }\r\n\r\n// // POST /api/v2/taskrunlog/findLogInfo\r\n// export function reqFindLogInfo(params) {\r\n//     return feach({\r\n//         url: baseUrl + '/taskrunlog/findLogInfo',\r\n//         method: 'POST',\r\n//         data: params\r\n//     })\r\n// }\r\n", "import Vue from 'vue'\r\nexport const bus = new Vue()", "<template>\r\n  <section class=\"menu-bar\">\r\n    <div class=\"node-selector-toggle\" @click=\"nodeSelectorHideToggle(!nodeSelectorHide)\">\r\n      <span class=\"selector-btn-text\">组件库</span>\r\n      <em class=\"el-icon-arrow-up selector-btn-icon\" :class=\"{ 'selector-hide': nodeSelectorHide }\"></em>\r\n    </div>\r\n    <div class=\"menu-buttons\">\r\n      <a class=\"menu-btn\" @click=\"refresh\"><em class=\"menu-icon menu-icon-refresh\"></em> <span>刷新</span></a>\r\n      <a class=\"menu-btn\" @click=\"processValid\"><em class=\"menu-icon menu-icon-valid\"></em> <span>流程校验</span></a>\r\n      <a class=\"menu-btn\" @click=\"handleSave()\"><em class=\"menu-icon menu-icon-draft\"></em> <span>保存草稿</span></a>\r\n      <a class=\"menu-btn\" @click=\"handleExecute\"><em class=\"menu-icon menu-icon-run\"></em> <span>立即运行</span></a>\r\n\r\n      <!-- \r\n        流程状态：\r\n        NEW(\"新建\"),\r\n        DRAFT(\"草稿\"),\r\n        SCHEDULING_WAIT(\"等待调度\"),\r\n        SCHEDULING(\"调度中\"),\r\n        SCHEDULING_COMPLETE(\"调度完成\"),\r\n        SCHEDULING_STOP(\"停止调度\"),\r\n        DELETED(\"删除\"),\r\n        -->\r\n      <!-- <template>\r\n        <a class=\"menu-btn\" @click=\"handleSchedule\"><em class=\"menu-icon menu-icon-scheduling\"></em> <span>执行调度</span></a>\r\n      </template> -->\r\n      <template v-if=\"workflowStatus != 'SCHEDULING' && workflowStatus != 'SCHEDULING_WAIT'\">\r\n        <a class=\"menu-btn\" @click=\"handleSchedule\"><em class=\"menu-icon menu-icon-scheduling\"></em>\r\n          <span>执行调度</span></a>\r\n      </template>\r\n\r\n      <template v-if=\"workflowStatus == 'SCHEDULING' || workflowStatus == 'SCHEDULING_WAIT'\">\r\n        <a class=\"menu-btn\" @click=\"handleFreeze\"><em class=\"menu-icon menu-icon-scheduling\"></em> <span>停止调度</span></a>\r\n      </template>\r\n\r\n    </div>\r\n    <!-- <div class=\"workflow-buttons\">\r\n      <el-button icon=\"el-icon-document-checked\" size=\"mini\" >保存</el-button>\r\n      <el-button type=\"primary\" icon=\"el-icon-video-play\" size=\"mini\">执行</el-button>\r\n    </div> -->\r\n\r\n    <!-- 流程校验弹框 -->\r\n    <el-dialog title=\"流程校验\" custom-class=\"dialog-form\" :visible.sync=\"validDialog\" width=\"648px\"\r\n      :close-on-click-modal=\"false\" :close-on-press-escape=\"false\" @close=\"validDialogClose\">\r\n      <div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">未连线组件</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.unConnectModules.length ? 'el-icon-warning' : 'el-icon-success'\" /> <span>{{\r\n                  validOption.unConnectModules.length ? validOption.unConnectModules.length + '项问题' : '通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n          <div v-if=\"validOption.unConnectModules.length\">\r\n            <el-table max-height=\"240\" :data=\"validOption.unConnectModules\" style=\"width: 100%\"\r\n              :header-cell-style=\"{ 'background-color': '#F5F7FA', 'color': '#909399' }\">\r\n              <el-table-column label=\"节点名称\" prop=\"name\" />\r\n              <el-table-column label=\"操作\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button @click=\"handlePosition(scope.row)\" type=\"text\" size=\"small\">定位</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">未配置组件</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.unConfigModules.length ? 'el-icon-warning' : 'el-icon-success'\" /> <span>{{\r\n                  validOption.unConfigModules.length ? validOption.unConfigModules.length + '项问题' : '通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n          <div v-if=\"validOption.unConfigModules.length\">\r\n            <el-table max-height=\"240\" :data=\"validOption.unConfigModules\" style=\"width: 100%\"\r\n              :header-cell-style=\"{ 'background-color': '#F5F7FA', 'color': '#909399' }\">\r\n              <el-table-column label=\"节点名称\" prop=\"name\" />\r\n              <el-table-column label=\"操作\" width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button @click=\"handlePosition(scope.row)\" type=\"text\" size=\"small\">定位</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">\r\n              <span>唯一流程校验</span>\r\n              <el-tooltip class=\"item\" effect=\"dark\" content=\"流程中只允许出现一条完整的流程，不允许出现一条以上无交集的子流程\" placement=\"top\">\r\n                <em class=\"el-icon-info\" />\r\n              </el-tooltip>\r\n            </div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.trackUnique ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{\r\n                  validOption.trackUnique ? '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">首节点为开始节点</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.startNodeIsValid ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{\r\n                  validOption.startNodeIsValid ? '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">尾节点为结束节点</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.finishNodeIsValid ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{\r\n                  validOption.finishNodeIsValid ? '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"validDialog = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <!-- 流程监测结果弹框 -->\r\n    <el-dialog title=\"流程监测结果\" custom-class=\"dialog-form\" :visible.sync=\"monitorDialog\" width=\"648px\"\r\n      :close-on-click-modal=\"false\" :close-on-press-escape=\"false\">\r\n      <div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">流程配置校验</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"processConfigVaild ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{ processConfigVaild ?\r\n                  '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n          <div v-if=\"!processConfigVaild\" style=\"white-space: normal;\">\r\n            检测到当前流程未配置完整，请先将流程配置完整。<br>点击流程控制区【流程校验】可查看详细问题。\r\n          </div>\r\n        </div>\r\n        <div class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">资源配置校验</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.executeParamConfigured ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{\r\n                  validOption.executeParamConfigured ? '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n          <div v-if=\"!validOption.executeParamConfigured\" style=\"white-space: normal;\">\r\n            检测到当前流程未配置运行资源，请先到控制台右侧【资源配置】配置流程运行资源。\r\n          </div>\r\n        </div>\r\n        <div v-if=\"!isNow\" class=\"itemStyle\">\r\n          <div class=\"headStyle\">\r\n            <div class=\"validTitle\">调度配置校验</div>\r\n            <div class=\"headRight\">\r\n              <em :class=\"validOption.scheduleConfigured ? 'el-icon-success' : 'el-icon-warning'\" /> <span>{{\r\n                  validOption.scheduleConfigured ? '通过验证' : '未通过验证'\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n          <div v-if=\"!validOption.scheduleConfigured\" style=\"white-space: normal;\">\r\n            检测到当前流程未配置调度信息，请先到控制台右侧【调度配置】配置流程调度信息\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"monitorDialog = false\">关 闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </section>\r\n\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  saveWorkflow,\r\n  executeWorkflow,\r\n  scheduleWorkflow, freezeWorkflow, getStatus, getWorkflowInfo, integrityValidate\r\n} from '@/api/https.js'\r\nimport { mapState, mapMutations } from 'vuex'\r\nimport { bus } from \"@/libs/bus\";\r\nexport default {\r\n  name: 'menuBar',\r\n  data() {\r\n    const checkName = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请输入任务名称'));\r\n      } else {\r\n        if (!/^[A-Za-z0-9_\\u4e00-\\u9fa5]{1,30}$/.test(value)) {\r\n          callback(new Error('支持1-30个字符以内的中文、英文、数字、下划线'));\r\n        } else {\r\n          callback()\r\n        }\r\n      }\r\n    }\r\n    return {\r\n      isNow: false,\r\n      processConfigVaild: false,\r\n      validDialog: false,\r\n      monitorDialog: false,\r\n      workflowId: '',\r\n      scheduleId: '',\r\n      category: '', // 任务类型\r\n      validOption: {\r\n        unConnectModules: [],\r\n        unConfigModules: []\r\n      },\r\n      workflowForm: {\r\n        name: '',\r\n        desc: ''\r\n      },\r\n      workflowFormRules: {\r\n        name: [\r\n          { validator: checkName, trigger: 'blur' }\r\n        ],\r\n      },\r\n      workflowStatus: ''\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState(['nodeSelectorHide'])\r\n  },\r\n  created() {\r\n    // this.workflowId = this.$route.query.workflowId\r\n    this.workflowId = window.GetQueryValue('workflowId')\r\n    this.category = window.GetQueryValue('category')\r\n    this.getWorkflowInfo()\r\n    this.getStatus()\r\n  },\r\n  mounted() {\r\n\r\n    bus.$on(\"scheduleId\", this.getScheduleId);\r\n  },\r\n  methods: {\r\n    getScheduleId(val) {\r\n      this.scheduleId = val\r\n    },\r\n    // 获取流程信息\r\n    async getWorkflowInfo() {\r\n      const res = await getWorkflowInfo({ workflowId: this.workflowId })\r\n      if (res.code == 200) {\r\n        const data = res.result\r\n        this.category = res.result.category\r\n        this.workflowForm = {\r\n          name: data.name,\r\n          desc: data.desc\r\n        }\r\n      }\r\n    },\r\n\r\n    //获取流程运行状态\r\n    async getStatus() {\r\n      const res = await getStatus({ workflowId: this.workflowId })\r\n      if (res.code == 200) {\r\n        this.workflowStatus = res.result.status\r\n      }\r\n    },\r\n\r\n    //立即执行\r\n    async handleExecute() {\r\n      integrityValidate(this.workflowId).then(async (res) => {\r\n        if (res.code === 200) {\r\n          // console.log(res)\r\n          this.processConfigVaild = res.result.startNodeIsValid && res.result.finishNodeIsValid && res.result.trackUnique && res.result.unConfigModules.length === 0 && res.result.unConnectModules.length === 0\r\n          // this.\r\n          if (this.processConfigVaild && res.result.executeParamConfigured) {\r\n            const res = await executeWorkflow({ workflowId: this.workflowId })\r\n            if (res.code == 200) {\r\n              this.$message({\r\n                type: 'success',\r\n                message: '流程开始执行'\r\n              })\r\n              bus.$emit('getComponentLogsBus')\r\n            }\r\n          } else {\r\n            this.validOption = res.result\r\n            this.monitorDialog = true\r\n            this.isNow = true\r\n          }\r\n        }\r\n      })\r\n\r\n\r\n      // if (res.code == 451) {\r\n      //   var arr = res.message.split('[')[1].split(']')[0].split(',')\r\n      //   console.log(arr)\r\n      //   bus.$emit('highlight', arr)\r\n      // }\r\n    },\r\n    // 定位\r\n    handlePosition(row) {\r\n      bus.$emit('highlight', row.id)\r\n      this.validDialog = false\r\n    },\r\n    //执行调度\r\n    async handleSchedule() {\r\n      integrityValidate(this.workflowId).then(async (res) => {\r\n        if (res.code === 200) {\r\n          this.processConfigVaild = res.result.startNodeIsValid && res.result.finishNodeIsValid && res.result.trackUnique && res.result.unConfigModules.length === 0 && res.result.unConnectModules.length === 0\r\n          // this.\r\n          if (this.processConfigVaild && res.result.executeParamConfigured && res.result.scheduleConfigured) {\r\n            const res = await scheduleWorkflow({ workflowId: this.workflowId })\r\n            if (res.code == 200) {\r\n              this.getStatus()\r\n              this.$message({\r\n                type: 'success',\r\n                message: '流程调度成功'\r\n              })\r\n              bus.$emit('getComponentLogsBus')\r\n            }\r\n          } else {\r\n            this.validOption = res.result\r\n            this.monitorDialog = true\r\n            this.isNow = false\r\n          }\r\n        }\r\n      })\r\n\r\n    },\r\n\r\n    //停止调度\r\n    async handleFreeze() {\r\n      const res = await freezeWorkflow({ workflowId: this.workflowId, scheduleId: this.scheduleId })\r\n      if (res.code == 200) {\r\n        this.getStatus()\r\n        this.$message({\r\n          type: 'success',\r\n          message: '停止调度成功'\r\n        })\r\n      }\r\n    },\r\n    handleSave() {\r\n      this.$confirm(`是否确认将${this.workflowForm.name}流程保存为草稿状态，草稿状态下流程调度配置将失效`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        saveWorkflow(this.workflowId).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功'\r\n            })\r\n          }\r\n        })\r\n      }).catch(() => { });\r\n    },\r\n    processValid() {\r\n      integrityValidate(this.workflowId).then((res) => {\r\n        if (res.code === 200) {\r\n          this.validOption = res.result\r\n          this.validDialog = true\r\n        }\r\n      })\r\n    },\r\n    centerContent() {\r\n      bus.$emit('centerContent')\r\n    },\r\n    refresh() {\r\n      bus.$emit('refresh')\r\n    },\r\n    fullScreen() {\r\n      bus.$emit('fullScreen')\r\n    },\r\n    toRedo() {\r\n      alert(1)\r\n      bus.$emit('toRedo')\r\n    },\r\n    toUndo() {\r\n      alert(2)\r\n      bus.$emit('toUndo')\r\n    },\r\n    validDialogClose() {\r\n\r\n    },\r\n    ...mapMutations(['nodeSelectorHideToggle'])\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-icon-warning {\r\n  font-size: 16px;\r\n  color: #FF5656;\r\n  margin-right: 16px;\r\n  display: inline-block;\r\n}\r\n\r\n.el-icon-success {\r\n  font-size: 16px;\r\n  color: #16C469;\r\n  margin-right: 16px;\r\n  display: inline-block;\r\n}\r\n\r\n.el-icon-info {\r\n  font-size: 16px;\r\n  color: #909399;\r\n  margin-left: 16px;\r\n  display: inline-block;\r\n}\r\n\r\n.validTitle {\r\n  font-size: 14px;\r\n  font-family: PingFangSC-Medium, PingFang SC;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.itemStyle {\r\n  padding: 10px;\r\n  border: 1px solid #DCDFE6;\r\n}\r\n\r\n.headStyle {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 48px;\r\n}\r\n\r\n.headRight {\r\n  min-width: 120px;\r\n  text-align: left;\r\n}\r\n\r\n.itemStyle+.itemStyle {\r\n  margin-top: 16px;\r\n}\r\n\r\n.menu-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 48px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e2e2e2;\r\n  white-space: nowrap;\r\n  user-select: none;\r\n}\r\n\r\n.node-selector-toggle {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 232px;\r\n  border-right: 1px solid #e2e2e2;\r\n  color: #777;\r\n  padding: 0;\r\n  height: 48px;\r\n  cursor: pointer;\r\n\r\n  .selector-btn-text {\r\n    display: inline-block;\r\n    width: 100px;\r\n    text-align: center;\r\n  }\r\n\r\n  .selector-btn-icon {\r\n    transition: transform .3s;\r\n  }\r\n\r\n  .selector-hide {\r\n    transform: rotate(180deg);\r\n  }\r\n\r\n  &:hover {\r\n    color: #6882da;\r\n  }\r\n}\r\n\r\n.menu-buttons {\r\n  display: inline-flex;\r\n  flex-grow: 1;\r\n  padding: 5px;\r\n}\r\n\r\n.menu-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  float: left;\r\n  height: 30px;\r\n  line-height: 30px;\r\n  padding: 0 8px;\r\n  margin: 0 6px 0 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  border-radius: 2px;\r\n\r\n  .menu-icon {\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    background-size: 100%;\r\n  }\r\n\r\n  .menu-icon-refresh {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  .menu-icon-save {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  .menu-icon-draft {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  .menu-icon-valid {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  .menu-icon-run {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  .menu-icon-scheduling {\r\n    background-image: url('~@/assets/images/menu_icons/<EMAIL>');\r\n  }\r\n\r\n  span {\r\n    margin-left: 5px;\r\n  }\r\n\r\n  &:hover {\r\n    background: #f2f5fc;\r\n    text-decoration: none;\r\n  }\r\n}\r\n\r\n.workflow-buttons {\r\n  margin-right: 20px;\r\n}\r\n\r\n.el-dialog.dialog-form {\r\n  background-color: #fff !important;\r\n  box-shadow: 0px 8px 24px 0px rgba(26, 72, 120, 0.1) !important;\r\n\r\n  .el-dialog__header {\r\n    background: #fff;\r\n    border-bottom: 1px solid #EBEEF5;\r\n\r\n    .el-dialog__title {\r\n      font-size: 16px;\r\n      font-weight: 400;\r\n      color: #303133;\r\n    }\r\n\r\n    .el-dialog__headerbtn .el-dialog__close {\r\n      color: #909399;\r\n      ;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 32px 24px;\r\n    margin: 0;\r\n\r\n    .el-form-item {\r\n      margin-bottom: 13px;\r\n    }\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    height: auto;\r\n    padding: 0 24px 25px 0;\r\n    text-align: right;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./menuBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./menuBar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./menuBar.vue?vue&type=template&id=06ff5d30&\"\nimport script from \"./menuBar.vue?vue&type=script&lang=js&\"\nexport * from \"./menuBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./menuBar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"node-selector\",class:{ 'node-selector-hide': _vm.nodeSelectorHide }},[_c('div',{staticClass:\"node-search-wrap\"},[_c('el-input',{attrs:{\"prefix-icon\":\"el-icon-search\",\"size\":\"mini\",\"placeholder\":\"请输入组件关键字\",\"clearable\":\"\"},model:{value:(_vm.keyword),callback:function ($$v) {_vm.keyword=$$v},expression:\"keyword\"}})],1),_c('div',{staticClass:\"node-wrap\"},_vm._l((_vm.componentsData),function(items,index){return _c('div',{key:index,staticClass:\"node-group\"},[_c('h4',{on:{\"click\":function($event){return _vm.shrink(index)}}},[_vm._v(\" \"+_vm._s(items.category)+\" \"),_c('em',{staticClass:\"el-icon-caret-top\",class:'icon' + index})]),_c('ul',[_vm._l((items.modules),function(item){return [_c('el-tooltip',{key:item.identifier,attrs:{\"popper-class\":\"tool-tip-box\",\"effect\":\"dark\",\"open-delay\":500,\"placement\":\"right\",\"enterable\":false}},[_c('div',{staticClass:\"tool-tip-content\",attrs:{\"slot\":\"content\"},slot:\"content\"},[_vm._v(\" \"+_vm._s((\"组件名称:\" + (item.displayName)))),_c('br'),_vm._v(\" \"+_vm._s(item.moduleDescription ? (\"组件描述:\" + (item.moduleDescription)) : '组件描述:暂无描述')+\" \")]),_c('li',{key:item.identifier,on:{\"mousedown\":function($event){return _vm.dragNode($event, item)}}},[_c('img',{staticClass:\"node-icon\",attrs:{\"src\":item.img,\"alt\":\"\"}}),_c('span',{staticClass:\"node-label\"},[_vm._v(_vm._s(item.displayName))])])])]})],2)])}),0)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <section class=\"node-selector\" :class=\"{ 'node-selector-hide': nodeSelectorHide }\">\r\n    <div class=\"node-search-wrap\">\r\n      <el-input prefix-icon=\"el-icon-search\" size=\"mini\" placeholder=\"请输入组件关键字\" v-model=\"keyword\" clearable></el-input>\r\n    </div>\r\n\r\n    <div class=\"node-wrap\">\r\n      <div class=\"node-group\" v-for=\"(items, index) in componentsData\" :key=\"index\">\r\n        <h4 @click=\"shrink(index)\">\r\n          {{ items.category }}\r\n          <em :class=\"'icon' + index\" class=\"el-icon-caret-top\"></em>\r\n        </h4>\r\n        <ul>\r\n          <template v-for=\"item in items.modules\">\r\n            <el-tooltip popper-class=\"tool-tip-box\" effect=\"dark\" :open-delay=\"500\" placement=\"right\" :enterable=\"false\"\r\n              :key=\"item.identifier\">\r\n              <div slot=\"content\" class=\"tool-tip-content\">\r\n                {{ `组件名称:${item.displayName}` }}<br>\r\n                {{ item.moduleDescription ? `组件描述:${item.moduleDescription}` : '组件描述:暂无描述' }}\r\n              </div>\r\n              <li :key=\"item.identifier\" @mousedown=\"dragNode($event, item)\">\r\n                <img class=\"node-icon\" :src=\"item.img\" alt=\"\">\r\n                <!-- <span\r\n                  class=\"node-icon\"\r\n                  :style=\"{backgroundImage: `url(./icon/${item.startFlag || item.endFlag ? 1 : 2}.svg)`}\"\r\n                ></span> -->\r\n                <span class=\"node-label\">{{ item.displayName }}</span>\r\n              </li>\r\n            </el-tooltip>\r\n          </template>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from \"vuex\";\r\nimport { bus } from \"@/libs/bus\";\r\nimport { getApplication } from \"@/api/https.js\";\r\n\r\nexport default {\r\n  name: \"nodeSelector\",\r\n  data() {\r\n    return {\r\n      componentsList: [],\r\n      keyword: \"\",\r\n    };\r\n  },\r\n  computed: {\r\n    componentsData() {\r\n      const data = JSON.parse(JSON.stringify(this.componentsList));\r\n      if (this.keyword.trim() === \"\") {\r\n        return data;\r\n      } else {\r\n        return data.filter(items => {\r\n          const modules = items.modules;\r\n          if (modules.length) {\r\n            const itemList = modules.filter(item => {\r\n              return item.name.includes(this.keyword);\r\n            });\r\n            items.modules = itemList;\r\n          }\r\n          return items.modules.length;\r\n        });\r\n      }\r\n    },\r\n    ...mapState([\"nodeSelectorHide\"])\r\n  },\r\n  watch: {\r\n    componentsData() {\r\n      this.$nextTick(() => {\r\n        this.componentsData.forEach((item, index) => {\r\n          document.querySelectorAll('.node-group')[index].style.height = 'auto'\r\n          document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-top`\r\n        })\r\n      })\r\n    }\r\n  },\r\n  async created() {\r\n    console.log(window.GetQueryValue('category'))\r\n    const res = await getApplication({ workflowCategory: window.GetQueryValue('category') });\r\n    if (res.code == 200) {\r\n      res.result.forEach(x => {\r\n        x.modules.forEach(y => {\r\n          y.name = y.displayName\r\n        })\r\n      })\r\n      this.componentsList = res.result;\r\n    }\r\n    bus.$emit('componentsList', this.componentsList);\r\n  },\r\n  methods: {\r\n    dragNode(event, item) {\r\n      bus.$emit(\"dragNode\", event, item);\r\n      // console.log(item, \"item\");\r\n    },\r\n    shrink(index) {\r\n      if (document.querySelectorAll('.node-group')[index].offsetHeight > 24) {\r\n        document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-bottom`\r\n        document.querySelectorAll('.node-group')[index].style.height = '24px'\r\n      } else {\r\n        document.querySelectorAll('.node-group')[index].style.height = 'auto'\r\n        document.querySelector(`.icon${index}`).className = `icon${index} el-icon-caret-top`\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.node-selector {\r\n  position: absolute;\r\n  z-index: 4;\r\n  top: 0;\r\n  left: 0;\r\n  width: 232px;\r\n  height: 100%;\r\n  opacity: 1;\r\n  background: #fff;\r\n  transition: all 0.4s;\r\n  border-right: 1px solid #e2e2e2;\r\n  user-select: none;\r\n  overflow: hidden;\r\n\r\n  .node-wrap {\r\n    height: calc(100% - 43px);\r\n    overflow: auto;\r\n    // border-top: 1px solid #e2e2e2;\r\n    // box-sizing: border-box;\r\n  }\r\n\r\n  .node-search-wrap {\r\n    display: flex;\r\n    align-items: center;\r\n    background-color: #f2f5fc;\r\n    border-bottom: 1px solid #e2e2e2;\r\n    height: 43px;\r\n    overflow: hidden;\r\n    padding: 0 7px;\r\n  }\r\n\r\n  .node-search {\r\n    width: auto;\r\n    margin: 7px;\r\n  }\r\n}\r\n\r\n.node-selector-hide {\r\n  opacity: 0;\r\n  height: 0;\r\n}\r\n\r\n.node-group {\r\n  overflow: hidden;\r\n  height: auto;\r\n  transition: all 0.3s;\r\n  margin-bottom: 8px;\r\n\r\n  h4 {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 25px;\r\n    line-height: 25px;\r\n    font-size: 12px;\r\n    background: #f2f5fc;\r\n    padding: 0 16px;\r\n    color: #333;\r\n    cursor: pointer;\r\n  }\r\n\r\n  ul {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  li {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-content: center;\r\n    width: 96px;\r\n    height: 80px;\r\n    margin: 8px 8px 0px 8px;\r\n    background: #fff;\r\n    cursor: pointer;\r\n    position: relative;\r\n\r\n    &:hover {\r\n      background: #f7f7f7;\r\n    }\r\n\r\n    .node-icon {\r\n      display: block;\r\n      width: 32px;\r\n      height: 32px;\r\n      margin: 0 auto;\r\n      background-size: 32px;\r\n      background-repeat: no-repeat;\r\n      background-position: 50%;\r\n      // background-image: url(\"/icon/2.svg\");\r\n    }\r\n\r\n    .node-label {\r\n      display: block;\r\n      height: 32px;\r\n      font-size: 12px;\r\n      line-height: 32px;\r\n      color: #333;\r\n      text-align: center;\r\n      overflow: hidden;\r\n      white-space: nowrap;\r\n      text-overflow: ellipsis\r\n    }\r\n  }\r\n}\r\n\r\n.tool-tip-box {\r\n  background-color: #464c59 !important;\r\n}\r\n\r\n.tool-tip-box[x-placement^=\"right\"] .popper__arrow {\r\n  border-color: transparent !important;\r\n  border-right-color: #464c59 !important;\r\n}\r\n\r\n.tool-tip-box[x-placement^=\"right\"] .popper__arrow::after {\r\n  border-color: transparent !important;\r\n  border-right-color: #464c59 !important;\r\n}\r\n\r\n.tool-tip-box[x-placement^=\"left\"] .popper__arrow {\r\n  border-color: transparent !important;\r\n  border-left-color: #464c59 !important;\r\n}\r\n\r\n.tool-tip-box[x-placement^=\"left\"] .popper__arrow::after {\r\n  border-color: transparent !important;\r\n  border-left-color: #464c59 !important;\r\n}\r\n\r\n.tool-tip-content {\r\n  max-width: 200px;\r\n  line-height: 25px;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./nodeSelector.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./nodeSelector.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./nodeSelector.vue?vue&type=template&id=6742ba06&scoped=true&\"\nimport script from \"./nodeSelector.vue?vue&type=script&lang=js&\"\nexport * from \"./nodeSelector.vue?vue&type=script&lang=js&\"\nimport style0 from \"./nodeSelector.vue?vue&type=style&index=0&id=6742ba06&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6742ba06\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"canvas-toolbox\"},[_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":_vm.isFullScreen ? '退出全屏' : '全屏',\"placement\":\"top\",\"enterable\":false}},[_c('em',{staticClass:\" toolbox-btn\",class:_vm.isFullScreen ? 'exitFull' : 'el-icon-full-screen',on:{\"click\":_vm.fullScreen}})]),_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":\"居中\",\"placement\":\"top\",\"enterable\":false}},[_c('em',{staticClass:\"el-icon-aim toolbox-btn\",on:{\"click\":_vm.centerContent}})]),_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":\"放大\",\"placement\":\"top\",\"enterable\":false}},[_c('em',{staticClass:\"el-icon-zoom-in toolbox-btn\",on:{\"click\":function($event){return _vm.scale(0.2)}}})]),_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":\"缩小\",\"placement\":\"top\",\"enterable\":false}},[_c('em',{staticClass:\"el-icon-zoom-out toolbox-btn\",on:{\"click\":function($event){return _vm.scale(-0.2)}}})]),_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":\"自动布局\",\"placement\":\"top\",\"enterable\":false}},[_c('svg-icon',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:([_vm.autoLayout, \"click\", 300]),expression:\"[autoLayout, `click`, 300]\"}],staticClass:\"toolbox-btn\",attrs:{\"name\":\"auto_layout\",\"color\":\"#999999\"}})],1),_c('el-tooltip',{staticClass:\"box-item\",attrs:{\"effect\":\"dark\",\"content\":\"开/关网格\",\"placement\":\"top\",\"enterable\":false}},[_c('em',{staticClass:\"el-icon-s-grid toolbox-btn\",on:{\"click\":_vm.gridToggle}})])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <section class=\"canvas-toolbox\">\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" :content=\"isFullScreen ? '退出全屏' : '全屏'\" placement=\"top\"\r\n      :enterable=\"false\">\r\n      <em :class=\"isFullScreen ? 'exitFull' : 'el-icon-full-screen'\" class=\" toolbox-btn\" @click=\"fullScreen\"></em>\r\n    </el-tooltip>\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" content=\"居中\" placement=\"top\" :enterable=\"false\">\r\n      <em class=\"el-icon-aim toolbox-btn\" @click=\"centerContent\"></em>\r\n    </el-tooltip>\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" content=\"放大\" placement=\"top\" :enterable=\"false\">\r\n      <em class=\"el-icon-zoom-in toolbox-btn\" @click=\"scale(0.2)\">\r\n      </em>\r\n    </el-tooltip>\r\n\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" content=\"缩小\" placement=\"top\" :enterable=\"false\">\r\n      <em class=\"el-icon-zoom-out toolbox-btn\" @click=\"scale(-0.2)\">\r\n      </em>\r\n    </el-tooltip>\r\n\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" content=\"自动布局\" placement=\"top\" :enterable=\"false\">\r\n      <svg-icon class=\"toolbox-btn\" name=\"auto_layout\" color=\"#999999\" v-throttle=\"[autoLayout, `click`, 300]\" />\r\n    </el-tooltip>\r\n\r\n    <el-tooltip class=\"box-item\" effect=\"dark\" content=\"开/关网格\" placement=\"top\" :enterable=\"false\">\r\n      <em class=\"el-icon-s-grid toolbox-btn\" @click=\"gridToggle\">\r\n      </em>\r\n    </el-tooltip>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport { bus } from '@/libs/bus'\r\nexport default {\r\n  name: 'canvasToolbox',\r\n  data() {\r\n    return {\r\n      isFullScreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n\r\n    bus.$on(\"fullScreenChange\", this.fullScreenChange);\r\n  },\r\n  methods: {\r\n    fullScreenChange(val) {\r\n      this.isFullScreen = val\r\n    },\r\n    scale(level) {\r\n      bus.$emit('scale', level)\r\n    },\r\n    fullScreen() {\r\n      bus.$emit('fullScreen')\r\n    },\r\n    centerContent() {\r\n      bus.$emit('centerContent')\r\n    },\r\n    gridToggle() {\r\n      bus.$emit('gridToggle')\r\n    },\r\n    autoLayout() {\r\n      bus.$emit('autoLayout')\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.canvas-toolbox {\r\n  display: flex;\r\n  align-items: center;\r\n  position: absolute;\r\n  padding: 0 5px;\r\n  top: 12px;\r\n  right: 50px;\r\n  height: 44px;\r\n  z-index: 2;\r\n  background: hsla(0, 0%, 100%, .8);\r\n\r\n  .toolbox-btn {\r\n    font-size: 18px;\r\n    color: #999999;\r\n    cursor: pointer;\r\n    margin: 0 5px;\r\n  }\r\n\r\n  .exitFull {\r\n    width: 18px;\r\n    height: 18px;\r\n    background: url('../assets/images/exitFull.png') no-repeat;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./canvasToolbox.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./canvasToolbox.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./canvasToolbox.vue?vue&type=template&id=04aeb8f0&\"\nimport script from \"./canvasToolbox.vue?vue&type=script&lang=js&\"\nexport * from \"./canvasToolbox.vue?vue&type=script&lang=js&\"\nimport style0 from \"./canvasToolbox.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('section',{staticClass:\"theCanvas\",style:(_vm.nodeSelectorHide ? 'margin-left: 0;' : 'margin-left: 232px;'),attrs:{\"id\":\"container\"}}),_c('contextMenu',{ref:\"contextMenuRef\",attrs:{\"position\":_vm.contextMenuPosition,\"visible\":_vm.contextMenuVisible,\"currentNode\":_vm.currentNode},on:{\"contextMenuEvent\":_vm.contextMenuEvent}}),(_vm.dialogVisible)?_c('el-dialog',{attrs:{\"append-to-body\":true,\"close-on-click-modal\":false,\"modal-append-to-body\":false,\"visible\":_vm.dialogVisible,\"width\":_vm.dialogWidth,\"custom-class\":\"task-config\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('div',{staticClass:\"header-title\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('span',{staticClass:\"tit\"},[_vm._v(_vm._s(_vm.templateTitle))]),(_vm.templateTitle == '选取数据')?_c('span',{staticStyle:{\"font-size\":\"14px\"}},[_c('img',{staticStyle:{\"position\":\"relative\",\"top\":\"4px\",\"margin\":\"0 12px 0 60px\"},attrs:{\"height\":\"20px\",\"src\":require(\"../assets/images/warning.png\")}}),_vm._v(\"目前仅支持选取已落地式接入的结构化数据 \")]):_vm._e()]),_c('iframe',{ref:\"myApplication\",staticClass:\"frame-layout\",attrs:{\"src\":_vm.componentUrl,\"frameborder\":\"0\",\"height\":\"100%\",\"marginheight\":\"0\",\"marginwidth\":\"0\",\"width\":\"100%\"},on:{\"load\":_vm.loadandpostmessage}}),(_vm.dialogWidth != '30%')?_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"info\"},on:{\"click\":function($event){_vm.dialogVisible = false}}},[_vm._v(\"取消\")]),_c('el-button',{directives:[{name:\"popover\",rawName:\"v-popover:popover\",arg:\"popover\"}],attrs:{\"disabled\":_vm.loading,\"type\":\"primary\"},on:{\"click\":_vm.onConfirmation}},[_vm._v(\"确认\")]),_c('el-tooltip',{key:100,staticClass:\"item\",attrs:{\"popper-class\":\"toolItemBig\",\"effect\":\"light\",\"enterable\":true,\"placement\":\"right-end\"}},[_c('div',{attrs:{\"slot\":\"content\"},slot:\"content\"},[(_vm.desc.function)?_c('div',[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"查看功能说明\")]),_c('div',{staticClass:\"poper-content\"},[_c('div',{staticClass:\"function\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"功能：\")]),_c('p',{staticClass:\"popper-body\"},[_vm._v(_vm._s(_vm.desc.function))])]),(_vm.desc.input.length > 0)?_c('div',{staticClass:\"popper-item input\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"输入：\")]),_vm._l((_vm.desc.input),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2):_vm._e(),(_vm.desc.output.length > 0)?_c('div',{staticClass:\"popper-item output\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"输出：\")]),_vm._l((_vm.desc.output),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2):_vm._e(),_c('div',{staticClass:\"popper-item ability\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"组件能力：\")]),_vm._l((_vm.desc.ability),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2)])]):_c('p',{staticClass:\"popper-body\"},[_vm._v(\"暂无内容，待更新\")])]),_c('div',{staticClass:\"checkButton\"},[_c('span',{ref:\"showDesc\",staticClass:\"text\"},[_c('em',{staticClass:\"el-icon-info\"},[_vm._v(\" 查看功能说明\")])])])])],1):_vm._e(),(_vm.dialogWidth == '30%')?_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"disabled\":_vm.loading,\"type\":\"primary\"},on:{\"click\":_vm.onConfirmation}},[_vm._v(\"确认\")]),_c('el-tooltip',{key:100,staticClass:\"item\",attrs:{\"popper-class\":\"toolItemBig\",\"effect\":\"light\",\"enterable\":true,\"placement\":\"right-end\"}},[_c('div',{attrs:{\"slot\":\"content\"},slot:\"content\"},[(_vm.desc.function)?_c('div',[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"查看功能说明\")]),_c('div',{staticClass:\"poper-content\"},[_c('div',{staticClass:\"function\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"功能：\")]),_c('p',{staticClass:\"popper-body\"},[_vm._v(_vm._s(_vm.desc.function))])]),(_vm.desc.input.length > 0)?_c('div',{staticClass:\"popper-item input\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"输入：\")]),_vm._l((_vm.desc.input),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2):_vm._e(),(_vm.desc.output.length > 0)?_c('div',{staticClass:\"popper-item output\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"输出：\")]),_vm._l((_vm.desc.output),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2):_vm._e(),_c('div',{staticClass:\"popper-item ability\"},[_c('h3',{staticClass:\"popper-title\"},[_vm._v(\"组件能力：\")]),_vm._l((_vm.desc.ability),function(item,index){return _c('p',{key:index,staticClass:\"popper-body\"},[_vm._v(_vm._s(item))])})],2)])]):_c('p',{staticClass:\"popper-body\"},[_vm._v(\"暂无内容，待更新\")])]),_c('div',{staticClass:\"checkButton\"},[_c('span',{ref:\"showDesc\",staticClass:\"text\"},[_c('em',{staticClass:\"el-icon-info\"},[_vm._v(\" 查看功能说明\")])])])])],1):_vm._e()]):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"node-shape-box\"},[_c('div',{staticClass:\"node-shape\"},[_c('div',{staticClass:\"sharp\"},[_c('div',{staticClass:\"sharpb\"},[_c('img',{staticClass:\"node-shape-icon\",attrs:{\"src\":_vm.shapeData.img}}),(_vm.shapeData.runStatus !== undefined && _vm.shapeData.runStatus !== 3)?_c('img',{staticClass:\"statu\",class:{ loading: _vm.shapeData.runStatus === 0 },attrs:{\"src\":require((\"../assets/images/components/icon-\" + (_vm.shapeData.runStatus === 0 ? 'inOperation' : _vm.shapeData.runStatus === 1 ? 'runSuccessfully' : _vm.shapeData.runStatus === 2 ? 'runFailed' : _vm.shapeData.runStatus === 4 ? 'noConfigure' : 'error') + \".png\")),\"alt\":\"\"}}):_vm._e()])])]),_c('div',{staticClass:\"node-label\"},[(_vm.shapeData.executeStatus === 'UNSETING')?_c('el-tooltip',{attrs:{\"content\":\"组件未配置，请双击配置\",\"placement\":\"top\",\"effect\":\"light\"}},[_c('i',{class:_vm.getIcon})]):(_vm.shapeData.executeStatus === 'NOTRUNNING')?_c('svg-icon',{staticStyle:{\"font-size\":\"16px\"},attrs:{\"name\":\"funnel\",\"color\":\"#666\"}}):_c('i',{class:_vm.getIcon}),_vm._v(\" \"+_vm._s(_vm.shapeData.name)+\" \")],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// 常量js\r\n// 组件状态\r\nexport const componentMapping = {\r\n  EXECUTING: {\r\n    label: '执行中',\r\n    icon: 'loading',\r\n    value: 'EXECUTING'\r\n  },\r\n  FAIL: {\r\n    label: '失败',\r\n    icon: 'error',\r\n    value: 'FAIL'\r\n  },\r\n  COMPLETED: {\r\n    label: '成功',\r\n    icon: 'success',\r\n    value: 'COMPLETED'\r\n  },\r\n  UNSETING: {\r\n    label: '未配置',\r\n    icon: 'warning',\r\n    value: 'UNSETING'\r\n  },\r\n  NOTRUNNING: {\r\n    label: '未运行',\r\n    icon: 'funnel',\r\n    value: 'NOTRUNNING'\r\n  }\r\n}\r\n// 流程状态\r\nexport const processStatusMapping = {\r\n  SUBMITTING: {\r\n    label: '提交中',\r\n    value: 'SUBMITTING'\r\n  },\r\n  UN_EXECUTE: {\r\n    label: '待执行',\r\n    value: 'UN_EXECUTE'\r\n  },\r\n  EXECUTING: {\r\n    label: '执行中',\r\n    value: 'EXECUTING'\r\n  },\r\n  STOP_EXECUTE: {\r\n    label: '停止执行',\r\n    value: 'STOP_EXECUTE'\r\n  },\r\n  COMPLETED: {\r\n    label: '执行完成',\r\n    value: 'COMPLETED'\r\n  },\r\n  IGNORE: {\r\n    label: '忽略',\r\n    value: 'IGNORE'\r\n  },\r\n  FAIL: {\r\n    label: '执行失败',\r\n    value: 'FAIL'\r\n  },\r\n}", "<template>\r\n  <div class=\"node-shape-box\">\r\n    <div class=\"node-shape\">\r\n      <div class=\"sharp\">\r\n        <div class=\"sharpb\">\r\n          <img class=\"node-shape-icon\" :src=\"shapeData.img\" />\r\n          <img :class=\"{ loading: shapeData.runStatus === 0 }\"\r\n            :src=\"require(`../assets/images/components/icon-${shapeData.runStatus === 0 ? 'inOperation' : shapeData.runStatus === 1 ? 'runSuccessfully' : shapeData.runStatus === 2 ? 'runFailed' : shapeData.runStatus === 4 ? 'noConfigure' : 'error'}.png`)\"\r\n            alt class=\"statu\" v-if=\"shapeData.runStatus !== undefined && shapeData.runStatus !== 3\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"node-label\">\r\n      <el-tooltip v-if=\"shapeData.executeStatus === 'UNSETING'\" content=\"组件未配置，请双击配置\" placement=\"top\" effect=\"light\">\r\n        <i :class=\"getIcon\"></i>\r\n      </el-tooltip>\r\n      <svg-icon v-else-if=\"shapeData.executeStatus === 'NOTRUNNING'\" name=\"funnel\" color=\"#666\"\r\n        style=\"font-size: 16px\" />\r\n      <i v-else :class=\"getIcon\"></i>\r\n      {{ shapeData.name }}\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { componentMapping } from '@/libs/types.js'\r\nexport default {\r\n  name: 'myShape',\r\n  inject: ['getGraph', 'getNode'],\r\n  data() {\r\n    return {\r\n      shapeData: {},\r\n    };\r\n  },\r\n  computed: {\r\n    getIcon() {\r\n      if (!this.shapeData.executeStatus) return ''\r\n      const icon = componentMapping[this.shapeData.executeStatus].icon\r\n      return `el-icon-${icon} c-${icon}`\r\n    }\r\n  },\r\n  mounted() {\r\n    const self = this;\r\n    const node = this.getNode();\r\n    const graph = this.getGraph()\r\n    self.shapeData = node.getData();\r\n    // 监听数据改变事件\r\n    node.on('change:data', ({ current }) => {\r\n      const edges = graph.getIncomingEdges(node)\r\n      const { executeStatus } = node.getData()\r\n      self.shapeData = current;\r\n      edges?.forEach((edge) => {\r\n        if (executeStatus === 'EXECUTING') {\r\n          edge.attr('line/strokeDasharray', 5)\r\n          edge.attr('line/style/animation', 'running-line 30s infinite linear')\r\n        } else {\r\n          edge.attr('line/strokeDasharray', '')\r\n          edge.attr('line/style/animation', '')\r\n        }\r\n      })\r\n    });\r\n  },\r\n  methods: {\r\n    // add() {\r\n    //   const node = this.getNode();\r\n    //   const { num } = node.getData();\r\n    //   node.setData({\r\n    //     num: num + 1,\r\n    //   });\r\n    // },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.statu {\r\n  width: 13px;\r\n  height: 14px;\r\n  position: absolute;\r\n  bottom: 17px;\r\n  left: 41px;\r\n}\r\n\r\n.node-label {\r\n  min-width: 120px;\r\n  position: absolute;\r\n  text-align: center;\r\n  left: -32px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n\r\n  .c-loading {\r\n    color: #fff;\r\n    background: #276eb7;\r\n    border-radius: 50%;\r\n    box-sizing: border-box;\r\n    padding: 1px;\r\n    font-size: 10px;\r\n  }\r\n\r\n  i {\r\n    margin-right: 4px !important;\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shape.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shape.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shape.vue?vue&type=template&id=2227f8da&scoped=true&\"\nimport script from \"./shape.vue?vue&type=script&lang=js&\"\nexport * from \"./shape.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shape.vue?vue&type=style&index=0&id=2227f8da&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2227f8da\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{style:({ left: _vm.position.x + (_vm.nodeSelectorHide ? 0 : 232) + 'px', top: _vm.position.y + 'px', display: _vm.visible ? 'block' : 'none' }),attrs:{\"id\":\"context-menu\"}},[_c('ul',[_c('li',{staticClass:\"menu-item\",on:{\"click\":_vm.config}},[_c('em',{staticClass:\"el-icon-setting\"}),_vm._v(\" 配置 \")]),_c('li',{staticClass:\"menu-item\",on:{\"click\":_vm.deleteNode}},[_c('em',{staticClass:\"el-icon-delete\"}),_vm._v(\" 删除 \")]),_c('li',{staticClass:\"menu-item\",on:{\"click\":_vm.copy}},[_c('em',{staticClass:\"el-icon-document-copy\"}),_vm._v(\" 复制 \")]),_c('li',{staticClass:\"menu-item\",on:{\"click\":_vm.handleRename}},[_c('em',{staticClass:\"el-icon-edit-outline\"}),_vm._v(\" 重命名 \")]),_c('li',{staticClass:\"menu-item\",on:{\"click\":function($event){return _vm.clearFunctionParam('')}}},[_c('em',{staticClass:\"el-icon-takeaway-box\"}),_vm._v(\" 清空配置 \")])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div id=\"context-menu\"\r\n    :style=\"{ left: position.x + (nodeSelectorHide ? 0 : 232) + 'px', top: position.y + 'px', display: visible ? 'block' : 'none' }\">\r\n    <ul>\r\n      <li class=\"menu-item\" @click=\"config\">\r\n        <em class=\"el-icon-setting\"></em> 配置\r\n      </li>\r\n      <li class=\"menu-item\" @click=\"deleteNode\">\r\n        <em class=\"el-icon-delete\"></em> 删除\r\n      </li>\r\n      <li class=\"menu-item\" @click=\"copy\">\r\n        <em class=\"el-icon-document-copy\"></em> 复制\r\n      </li>\r\n      <li class=\"menu-item\" @click=\"handleRename\">\r\n        <em class=\"el-icon-edit-outline\"></em> 重命名\r\n      </li>\r\n      <li class=\"menu-item\" @click=\"clearFunctionParam('')\">\r\n        <em class=\"el-icon-takeaway-box\"></em> 清空配置\r\n      </li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { bus } from \"@/libs/bus\";\r\nimport { deleteNode, updateName, nodeCopy, updateFunctionParamClear } from \"@/api/https.js\";\r\n\r\nexport default {\r\n  name: \"contextMenu\",\r\n  props: {\r\n    position: {\r\n      //右键菜单的坐标\r\n      required: true,\r\n      type: null,\r\n      default: () => ({ x: 0, y: 0 })\r\n    },\r\n    visible: {\r\n      // 右键菜单是否显示\r\n      required: true,\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    currentNode: {\r\n      // 当前右键选中的组件\r\n      required: true,\r\n      type: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState([\"nodeSelectorHide\"])\r\n  },\r\n  methods: {\r\n    // 配置\r\n    config() {\r\n      this.$emit(\"contextMenuEvent\", \"config\");\r\n    },\r\n    // 删除组件\r\n    async deleteNode() {\r\n      this.$emit(\"contextMenuEvent\");\r\n      await this.$confirm('删除此节点会同时删除相关连接线并清空配置，是否继续？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        const params = {\r\n          moduleId: this.currentNode.id\r\n        };\r\n        const res = await deleteNode(params);\r\n        if (res.code == 200) {\r\n          this.$emit(\"contextMenuEvent\", \"deleteNode\");\r\n        } else {\r\n          this.$emit(\"contextMenuEvent\", \"getWorkflow\");\r\n        }\r\n      }).catch(() => {\r\n      })\r\n    },\r\n\r\n    // 重命名\r\n    handleRename() {\r\n      this.$emit(\"contextMenuEvent\");\r\n      this.$prompt(\"请输入名称\", \"名称修改\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        closeOnClickModal: false,\r\n        closeOnPressEscape: false,\r\n        inputPattern: /^[\\u4e00-\\u9fa5_a-zA-Z0-9_]{2,16}$/,\r\n        inputErrorMessage: \"2-16个中英文、数字、下划线\",\r\n        inputValue: this.currentNode.attrs.label.text\r\n      })\r\n        .then(async ({ value }) => {\r\n          const params = {\r\n            // id: this.currentNode.id,\r\n            name: value\r\n          }\r\n          const res = await updateName(params, this.currentNode.id)\r\n          if (res.code == 200) {\r\n            // this.currentNode.attr(\"label/text\", value);\r\n            bus.$emit('refresh')\r\n          } else {\r\n            this.$emit(\"contextMenuEvent\", \"getWorkflow\");\r\n          }\r\n        })\r\n        .catch(() => {\r\n\r\n        });\r\n    },\r\n\r\n    // 复制组件\r\n    async copy() {\r\n      const nodeHeight = 85;\r\n      const params = {\r\n        id: this.currentNode.id\r\n      }\r\n      const { x, y } = this.currentNode.position()\r\n      const data = {\r\n        x: x,\r\n        y: y + nodeHeight\r\n      }\r\n      await nodeCopy(params, data)\r\n      this.$emit(\"contextMenuEvent\", \"getWorkflow\");\r\n    },\r\n\r\n    // 清空组件参数配置\r\n    async clearFunctionParam(id) {\r\n      // const params = {\r\n      //   // id: this.currentNode.id,\r\n      //   functionParam: null\r\n      // }\r\n      const res = await updateFunctionParamClear(id || this.currentNode.id)\r\n      if (res.code == 200) {\r\n        this.$emit(\"contextMenuEvent\", 'clearFunctionParam')\r\n        if (id) return\r\n        this.$message({\r\n          showClose: true,\r\n          message: '清空成功',\r\n          type: 'success'\r\n        })\r\n      } else {\r\n        this.$emit(\"contextMenuEvent\", \"getWorkflow\");\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n#context-menu {\r\n  position: absolute;\r\n  z-index: 3;\r\n  left: 0;\r\n  top: 0;\r\n  width: 140px;\r\n  background-color: #fff;\r\n  box-sizing: border-box;\r\n  border: 1px solid #dadada;\r\n  box-shadow: 0 0 10px 0px #dadada;\r\n  user-select: none;\r\n\r\n  .menu-item {\r\n    height: 35px;\r\n    line-height: 35px;\r\n    cursor: pointer;\r\n    padding-left: 15px;\r\n\r\n    &:hover {\r\n      background-color: #efefef;\r\n      color: #329ffb;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./contextMenu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./contextMenu.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./contextMenu.vue?vue&type=template&id=07458d63&scoped=true&\"\nimport script from \"./contextMenu.vue?vue&type=script&lang=js&\"\nexport * from \"./contextMenu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./contextMenu.vue?vue&type=style&index=0&id=07458d63&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"07458d63\",\n  null\n  \n)\n\nexport default component.exports", "import feach from '../libs/feach'\r\n\r\nconst baseUrl = window.applicationServerPath\r\n\r\n// 获取taskId\r\nexport function getTaskIdApi(workflowId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/latest_task_id`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 获取流程日志\r\nexport function getProcessLogsApi(taskId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/tasks/${taskId}/execute_logs`,\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 获取组件日志\r\nexport function getCompnentsLogsApi(taskId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/tasks/${taskId}/module_logs`,\r\n    method: 'get',\r\n  })\r\n}\r\n", "// 日志mixin---定时获取taskId、组件日志、流程日志\r\n// 1.开启定时器获取taskId--存在（保存变量）--获取组件日志接口、流程日志接口（流程日志面板打开才调用）\r\n// 2.组件日志接口若返回结束标识--taskId不变---停止调用获取组件日志接口、流程日志接口\r\n//                          --taskId变---继续调用获取组件日志接口、流程日志接口\r\nimport { getTaskIdApi, getCompnentsLogsApi, getProcessLogsApi } from '@/api/log.js';\r\nimport { setInterval } from 'core-js';\r\nimport { bus } from \"@/libs/bus\";\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { processStatusMapping } from '@/libs/types.js'\r\nexport default {\r\n  data() {\r\n    return {\r\n      executeState: false, // 流程状态（false未完成）\r\n      taskIdIsChanged: false, // taskId是否发生变化（false未变化）\r\n      timer: null,\r\n      firstGetProcessLogs: true // 初始化获取流程日志（true未初始化获取）\r\n    }\r\n  },\r\n  created() {\r\n    this.handle()\r\n    this.timer = setInterval(() => {\r\n      this.handle()\r\n    }, 10000);\r\n  },\r\n  mounted() {\r\n    bus.$on('getComponentLogsBus', () => {\r\n      this.executeState = false\r\n      this.handle()\r\n    })\r\n  },\r\n  watch: {\r\n    '$store.state.logBarIsOpen'(val) {\r\n      if (val) this.handle();\r\n    }\r\n  },\r\n  methods: {\r\n    // 开始执行\r\n    async handle() {\r\n      await this.getTaskId();\r\n      const taskId = this.$store.state.taskId\r\n      if (!taskId) return;\r\n      const logBarIsOpen = this.$store.state.logBarIsOpen\r\n      if (!this.executeState || (this.executeState && this.taskIdIsChanged)) {\r\n        this.getComponentsLogs(taskId);\r\n        if (logBarIsOpen || this.firstGetProcessLogs) this.getProcessLogs(taskId);\r\n      }\r\n    },\r\n    // 获取TaskId\r\n    async getTaskId() {\r\n      if (window.GetQueryValue('workflowId')) {\r\n        const { result } = await getTaskIdApi(\r\n          window.GetQueryValue('workflowId')\r\n        )\r\n        this.taskIdIsChanged = this.$store.state.taskId === result ? false : true\r\n        if (result) this.$store.commit('setTaskId', result);\r\n      }\r\n    },\r\n    // 获取组件日志信息\r\n    async getComponentsLogs(taskId) {\r\n      const { result } = await getCompnentsLogsApi(taskId)\r\n      this.$store.commit('setComponentLogs', result)\r\n      this.executeState = [\r\n        processStatusMapping.STOP_EXECUTE.value,\r\n        processStatusMapping.COMPLETED.value,\r\n        processStatusMapping.FAIL.value\r\n      ].indexOf(result.executeState) !== -1\r\n    },\r\n    // 获取流程日志信息\r\n    async getProcessLogs(taskId) {\r\n      this.firstGetProcessLogs = false;\r\n      const { result } = await getProcessLogsApi(taskId)\r\n      result.taskExecuteLogList.forEach((item) => {\r\n        item.uuId = uuidv4();\r\n        item.ouput = `${item.recordDatetime} | ${item.level} | ${item.msg}   ${item.extendMsg || ''\r\n          }`;\r\n      });\r\n      this.$store.commit('setProcessLogs', result.taskExecuteLogList)\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer)\r\n    bus.$off('getComponentLogsBus')\r\n  }\r\n};", "import { DagreLayout } from '@antv/layout'\r\nimport { updatePosition } from \"@/api/https.js\";\r\n\r\nexport default {\r\n  methods: {\r\n    // layout方式渲染画布\r\n    renderLayoutGraph(nodes, edges) {\r\n      this.isDagreLayout = false\r\n      const dagreLayout = new DagreLayout({\r\n        type: 'dagre',\r\n        rankdir: 'LR',\r\n        ranksep: 50,\r\n        nodesep: 25,\r\n      })\r\n      const model = dagreLayout.layout({\r\n        nodes,\r\n        edges\r\n      })\r\n      this.graph.fromJSON(model);\r\n\r\n      // 保存自动布局后位置信息\r\n      const layoutModelPositions = model.nodes.map(item => {\r\n        return {\r\n          id: item.id,\r\n          x: item.x,\r\n          y: item.y\r\n        }\r\n      })\r\n      this.updateModelPosition(layoutModelPositions)\r\n    },\r\n\r\n    // 更新组件位置信息\r\n    async updateModelPosition(modelPositions) {\r\n      const res = await updatePosition(modelPositions)\r\n      if (res.code != 200) {\r\n        this.getWorkflow()\r\n      }\r\n    },\r\n\r\n    // 根据组件日志返回的组件状态绑定到画布\r\n    bindComponentStatus() {\r\n      const cells = this.graph.getCells()\r\n      if (!cells || cells.length === 0) return\r\n      const { moduleExecuteLogDTOList, executeState } = this.$store.state.componentLogs\r\n      cells.forEach((item) => {\r\n        if (!item.data) return\r\n        const logData = moduleExecuteLogDTOList.filter(logItem => logItem.moduleId === item.id)[0]\r\n        let moduleStatus = item.data.executeStatus\r\n        if (logData) {\r\n          moduleStatus = moduleStatus === 'UNSETING' ? 'UNSETING' : logData.executeState\r\n        } else {\r\n          // 任务状态为”提交中“、”待执行“、”执行中“时，组件状态默认”未运行“\r\n          if (['SUBMITTING', 'UN_EXECUTE', 'EXECUTING'].includes(executeState)) moduleStatus = 'NOTRUNNING'\r\n        }\r\n        item.prop('data', { ...item.data, executeStatus: moduleStatus })\r\n      })\r\n    },\r\n\r\n    // 入参父组件id矫正（修复：更改父组件后，入参id还是旧父组件id问题）\r\n    // 逻辑：入参[index].id = parentJson[index].id\r\n    correctParentId(data) {\r\n      if (!data || data.length <= 0) return\r\n      const parentJson = this.nodeData.parentJson\r\n      data.forEach((item, index) => {\r\n        if (parentJson[index]) {\r\n          item.id = JSON.parse(parentJson[index])[0].id\r\n        }\r\n      })\r\n    }\r\n  },\r\n};", "<template>\r\n  <div>\r\n    <section id=\"container\" class=\"theCanvas\" :style=\"nodeSelectorHide ? 'margin-left: 0;' : 'margin-left: 232px;'\">\r\n    </section>\r\n    <contextMenu :position=\"contextMenuPosition\" :visible=\"contextMenuVisible\" :currentNode=\"currentNode\"\r\n      @contextMenuEvent=\"contextMenuEvent\" ref=\"contextMenuRef\" />\r\n    <!--弹窗组件-->\r\n    <el-dialog :append-to-body=\"true\" :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :visible.sync=\"dialogVisible\" :width=\"dialogWidth\" custom-class=\"task-config\" v-if=\"dialogVisible\">\r\n      <div class=\"header-title\" slot=\"title\">\r\n        <span class=\"tit\">{{ templateTitle }}</span>\r\n        <span style=\"font-size:14px\" v-if=\"templateTitle == '选取数据'\">\r\n          <img height=\"20px\" src=\"../assets/images/warning.png\"\r\n            style=\"position:relative;top:4px;margin:0 12px 0 60px\" />目前仅支持选取已落地式接入的结构化数据\r\n        </span>\r\n      </div>\r\n      <iframe :src=\"componentUrl\" @load=\"loadandpostmessage\" class=\"frame-layout\" frameborder=\"0\" height=\"100%\"\r\n        marginheight=\"0\" marginwidth=\"0\" ref=\"myApplication\" width=\"100%\"></iframe>\r\n      <span class=\"dialog-footer\" slot=\"footer\" v-if=\"dialogWidth != '30%'\">\r\n        <el-button @click=\"dialogVisible = false\" type=\"info\">取消</el-button>\r\n        <el-button :disabled=\"loading\" @click=\"onConfirmation\" type=\"primary\" v-popover:popover>确认</el-button>\r\n        <el-tooltip class=\"item\" popper-class=\"toolItemBig\" effect=\"light\" :enterable=\"true\" placement=\"right-end\"\r\n          :key=\"100\">\r\n          <div slot=\"content\">\r\n            <div v-if=\"desc.function\">\r\n              <h3 class=\"popper-title\">查看功能说明</h3>\r\n              <div class=\"poper-content\">\r\n                <div class=\"function\">\r\n                  <h3 class=\"popper-title\">功能：</h3>\r\n                  <p class=\"popper-body\">{{ desc.function }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item input\" v-if=\"desc.input.length > 0\">\r\n                  <h3 class=\"popper-title\">输入：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.input\">{{ item }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item output\" v-if=\"desc.output.length > 0\">\r\n                  <h3 class=\"popper-title\">输出：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.output\">{{ item }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item ability\">\r\n                  <h3 class=\"popper-title\">组件能力：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.ability\">{{ item }}</p>\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n            <p v-else class=\"popper-body\">暂无内容，待更新</p>\r\n          </div>\r\n          <div class=\"checkButton\">\r\n\r\n            <span class=\"text\" ref=\"showDesc\"><em class=\"el-icon-info\"> 查看功能说明</em></span>\r\n          </div>\r\n        </el-tooltip>\r\n      </span>\r\n      <span class=\"dialog-footer\" slot=\"footer\" v-if=\"dialogWidth == '30%'\">\r\n        <el-button :disabled=\"loading\" @click=\"onConfirmation\" type=\"primary\">确认</el-button>\r\n        <el-tooltip class=\"item\" popper-class=\"toolItemBig\" effect=\"light\" :enterable=\"true\" placement=\"right-end\"\r\n          :key=\"100\">\r\n          <div slot=\"content\">\r\n            <div v-if=\"desc.function\">\r\n\r\n              <h3 class=\"popper-title\">查看功能说明</h3>\r\n              <div class=\"poper-content\">\r\n                <div class=\"function\">\r\n                  <h3 class=\"popper-title\">功能：</h3>\r\n                  <p class=\"popper-body\">{{ desc.function }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item input\" v-if=\"desc.input.length > 0\">\r\n                  <h3 class=\"popper-title\">输入：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.input\">{{ item }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item output\" v-if=\"desc.output.length > 0\">\r\n                  <h3 class=\"popper-title\">输出：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.output\">{{ item }}</p>\r\n                </div>\r\n\r\n                <div class=\"popper-item ability\">\r\n                  <h3 class=\"popper-title\">组件能力：</h3>\r\n                  <p :key=\"index\" class=\"popper-body\" v-for=\"(item, index) in desc.ability\">{{ item }}</p>\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n            <p v-else class=\"popper-body\">暂无内容，待更新</p>\r\n          </div>\r\n          <div class=\"checkButton\">\r\n\r\n            <span class=\"text\" ref=\"showDesc\"><em class=\"el-icon-info\"> 查看功能说明</em></span>\r\n          </div>\r\n        </el-tooltip>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { mapMutations, mapState } from 'vuex'\r\nimport { Graph, Addon, Shape, Path } from \"@antv/x6\";\r\nconst { Dnd } = Addon;\r\nimport \"@antv/x6-vue-shape\";\r\nimport { bus } from \"@/libs/bus\";\r\nimport { getWorkflow, deleteConnInfo, nodeAdd, edgeAdd, queryPreviousAndSelfParam, updateFunctionParam } from \"@/api/https.js\";\r\nimport myShape from \"./shape.vue\";\r\nimport contextMenu from \"./contextMenu.vue\";\r\nimport logs from '@/mixin/logs.js'\r\nimport graphMixin from '@/mixin/graph.js'\r\nexport default {\r\n  name: \"graphCanvas\",\r\n  mixins: [logs, graphMixin],\r\n  components: {\r\n    contextMenu\r\n  },\r\n  data() {\r\n    Graph.registerConnector(\r\n      'algo-connector',\r\n      (s, e) => {\r\n        const offset = 4\r\n        const deltaX = Math.abs(e.x - s.x)\r\n        const control = Math.floor((deltaX / 3) * 2)\r\n\r\n        const v1 = { x: s.x + offset + control, y: s.y }\r\n        const v2 = { x: e.x - offset - control, y: e.y }\r\n\r\n        return Path.normalize(\r\n          `M ${s.x} ${s.y}\r\n          L ${s.x + offset} ${s.y}\r\n          C ${v1.x} ${v1.y} ${v2.x} ${v2.y} ${e.x - offset} ${e.y}\r\n          L ${e.x} ${e.y}\r\n          `,\r\n        )\r\n      },\r\n      true,\r\n    )\r\n    return {\r\n      lastTargetCell: '',\r\n      isFullScreen: false,\r\n      navList: [],\r\n      codeList: [],\r\n      dialogVisible: false,\r\n      dialogWidth: '85%',\r\n      templateTitle: '',\r\n      componentUrl: '',\r\n      loading: true,\r\n      desc: {\r\n        function: '',\r\n        input: [],\r\n        output: [],\r\n        ability: []\r\n      },\r\n      showGrid: false,\r\n      knob: null,\r\n      container: null,\r\n      headHeight: 74, // 页面顶部任务名称和菜单栏高度\r\n      graph: null, // 画布实例\r\n      dnd: null, // 拖拽插件实例\r\n      resizeTimer: null,\r\n      contextMenuPosition: { // 右键鼠标位置\r\n        x: 0,\r\n        y: 0\r\n      },\r\n      contextMenuVisible: false, // 是否显示右键菜单\r\n      nodeData: {}, // 节点传递数据\r\n      updateStatus: false, // 更新节点时 是否给出提示\r\n      modList: [], // 组件信息\r\n      connList: [], // 连线信息\r\n      notInitModList: [], // 未配置的组件id列表\r\n      workflowId: \"09c3831852194974a72db1b81ba04154\", // 流程id\r\n\r\n      currentNode: {}, // 当前右键选中的元素数据\r\n      // Graph是否自动布局\r\n      isDagreLayout: false,\r\n    };\r\n  },\r\n  computed: {\r\n    ...mapState([\"nodeSelectorHide\"])\r\n  },\r\n  watch: {\r\n    'nodeSelectorHide'(val) {\r\n      if (val) {\r\n        this.graph.resize(window.innerWidth)\r\n      } else {\r\n        this.graph.resize(window.innerWidth - 232)\r\n      }\r\n    },\r\n    '$store.state.componentLogs'() {\r\n      this.bindComponentStatus()\r\n    }\r\n  },\r\n  created() {\r\n    this.workflowId = window.GetQueryValue('workflowId')\r\n    // this.workflowId = this.$route.query.workflowId\r\n  },\r\n  mounted() {\r\n    window.addEventListener('resize', () => {\r\n      if (!this.checkFull()) {\r\n        this.isFullScreen = false\r\n      } else {\r\n        this.isFullScreen = true\r\n      }\r\n      bus.$emit('fullScreenChange', this.isFullScreen)\r\n    })\r\n    window.onmessage = e => {\r\n      // debugger\r\n      // console.log(e,\"接收的数据\")\r\n      // 提交配置\r\n      if (e.data && e.data.name == 'confirmConfig') {\r\n        if (e.data.type === 'hongfu') {\r\n          this.isHongfu = true\r\n        } else {\r\n          this.isHongfu = false\r\n        }\r\n        // this.data.nodeList.find(n => n.id === e.data.id).runStatus = 3\r\n        this.configSubmit(e.data)\r\n        // const resData = JSON.parse(e.data.data)\r\n        // if (resData[0].dataType) {\r\n        //   // 接入选组组件\r\n        //   const allConnections = this.jsPlumb.getConnections()\r\n        //   const sourceNodeData = this.data.nodeList.find(\r\n        //     n => n.code === 'chooseShareData' || n.code === 'chooseShareDataSpark'\r\n        //   )\r\n        //   this.dataType = resData[0].dataType\r\n        //   sourceNodeData.dataType = resData[0].dataType\r\n        //   if (sourceNodeData.connList.length > 0) {\r\n        //     const targetNodeData = this.data.nodeList.find(n => n.id === sourceNodeData.connList[0].targetNodeId)\r\n        //     if ((resData[0].dataType === 'file' && targetNodeData.code !== 'resourceAnalysis') || (resData[0].dataType === 'dataBase' && targetNodeData.code === 'resourceAnalysis')) {\r\n        //       this.$message.error(resData[0].dataType === 'file' ? '数据服务接入组件选取excel文件后只能连接Excel解析组件！' : '数据服务接入组件选取表数据后不能连接Excel解析组件！')\r\n        //       allConnections.forEach(item => {\r\n        //         if (\r\n        //           item.sourceId === sourceNodeData.connList[0].sourceNodeId &&\r\n        //           item.targetId === sourceNodeData.connList[0].targetNodeId\r\n        //         ) {\r\n        //           this.jsPlumb.deleteConnection(item)\r\n        //         }\r\n        //       })\r\n        //     }\r\n        //   }\r\n        // }\r\n        if (e.data.component == 'storeupdata') {\r\n          this.storeData = e.data.data // 这里保存下data，俊杰说确认配置成功后，需要调一个存储的接口，接口中需要这些数据\r\n          // console.log(this.storeData)\r\n        }\r\n      }\r\n      // 接入新数据->跳转\r\n      if (e.data.name == 'openData') {\r\n        window.parent.postMessage(\r\n          {\r\n            name: 'toData',\r\n            data: '',\r\n            component: ''\r\n          },\r\n          '*'\r\n        )\r\n      }\r\n      // 获取组件帮助配置文案\r\n      if (e.data.name === 'desc') {\r\n\r\n        this.desc = e.data.data\r\n      }\r\n    }\r\n\r\n\r\n    bus.$on('componentsList', this.componentsList);\r\n    bus.$on(\"dragNode\", this.dragNode);\r\n    bus.$on(\"centerContent\", this.centerContent);\r\n    bus.$on(\"scale\", this.scale);\r\n    bus.$on(\"gridToggle\", this.gridToggle);\r\n    bus.$on(\"autoLayout\", this.autoLayout);\r\n    bus.$on(\"refresh\", this.refresh);\r\n    bus.$on(\"fullScreen\", this.fullScreen);\r\n\r\n    bus.$on(\"toRedo\", this.toRedo);\r\n    bus.$on(\"toUndo\", this.toUndo);\r\n\r\n    bus.$on(\"highlight\", this.highlight);\r\n    window.addEventListener(\"resize\", this.onResize, false);\r\n    this.$nextTick(() => {\r\n      this.initCanvas()\r\n      this.initAddon()\r\n      // this.getWorkflow()\r\n    })\r\n  },\r\n\r\n  methods: {\r\n    componentsList(data) {\r\n      window.componentsUrl = []\r\n      // const that = this\r\n      this.navList = data\r\n      data.forEach(function (item) {\r\n        // that.codeList.push({\r\n        //   img: item.img,\r\n        //   code: item.code,\r\n        //   name: item.name,\r\n        //   color: item.color,\r\n        //   status: 0\r\n        // })\r\n        item.modules.forEach((v) => {\r\n          window.componentsUrl.push(v)\r\n        })\r\n      })\r\n      this.getWorkflow()\r\n    },\r\n    initCanvas() {\r\n      const self = this\r\n      const container = document.getElementById(\"container\");\r\n      this.container = container;\r\n      this.graph = new Graph({\r\n        // selecting: true,\r\n        container: container,\r\n        width: window.innerWidth - 232,\r\n        height: window.innerHeight - 74,\r\n        background: {\r\n          color: '#fff', // 设置画布背景颜色\r\n        },\r\n        panning: true,\r\n        // scroller: true,\r\n        history: {\r\n          enabled: true, //历史记录\r\n          ignoreChange: true //ignoreChange 是否忽略属性变化\r\n        },\r\n        snapline: true,\r\n        selecting: {\r\n          enabled: true,\r\n          // enabled: true,\r\n          // multiple: true,\r\n          // // rubberband: true,\r\n          // movable: true,\r\n          // showNodeSelectionBox: true,\r\n        },\r\n        highlighting: {\r\n          magnetAvailable: {\r\n            name: \"stroke\",\r\n            args: {\r\n              attrs: {\r\n                fill: \"#fff\",\r\n                stroke: \"#47C769\"\r\n              }\r\n            }\r\n          },\r\n          magnetAdsorbed: {\r\n            name: \"stroke\",\r\n            args: {\r\n              attrs: {\r\n                fill: \"#fff\",\r\n                stroke: \"#31d0c6\"\r\n              }\r\n            }\r\n          }\r\n        },\r\n        connecting: {\r\n          allowPort: true, //是否允许边链接到链接桩\r\n          allowEdge: false, //是否允许边链接到另一个边\r\n          allowNode: false, //是否允许边链接到节点（非节点上的链接桩)\r\n          allowLoop: false, //是否允许创建循环连线，即边的起始节点和终止节点为同一节点\r\n          allowMulti: false, //是否允许在相同的起始节点和终止之间创建多条边\r\n          allowBlank: false, //是否允许连接到画布空白位置的点\r\n          highlight: true, //拖动边时，是否高亮显示所有可用的连接桩或节点\r\n          connector: 'algo-connector',\r\n          sourceAnchor: 'right',\r\n          targetAnchor: 'left',\r\n          connectionPoint: 'anchor',\r\n          // 点击链接桩时，根据此函数返回值来判断是否新增边, 函数返回 true 可以新增，返回false不可以新增\r\n          // validateMagnet({ cell }) {\r\n          //   // \"maxConnections\" => 多输入标识: 1.单输入；2.多输入\r\n          //   // \"maxOutput\" => 多输出标识: 1.单输出；2.多输出\r\n\r\n          //   const { id, maxOutput, name } = cell.getData();\r\n          //   if (parseInt(maxOutput) === 2) {\r\n          //     return true\r\n          //   }\r\n\r\n          //   const edges = self.graph.getEdges();\r\n          //   for(let edge of edges) {\r\n          //     const { cell: sourceId } = edge.source\r\n          //     if (sourceId === id) {\r\n          //        self.$message({\r\n          //           message: `组件${name} 不支持多输出！`,\r\n          //           type: 'warning'\r\n          //         });\r\n          //        return false\r\n          //     }\r\n          //   }\r\n\r\n          //   return true\r\n          // },\r\n          validateConnection({ targetMagnet }) {\r\n            if (targetMagnet.getAttribute(\"port-group\") !== \"in\") {\r\n              return false;\r\n            }\r\n            return true;\r\n          },\r\n\r\n          // 新增连线\r\n          validateEdge({ edge }) {\r\n            let validateEdgeStatus = true;\r\n            const nodeId = edge.target.cell;\r\n            const targetCell = self.graph.getCellById(nodeId);\r\n            // \"maxConnections\" => 多输入标识: 1.单输入；2.多输入\r\n            // \"maxOutput\" => 多输出标识: 1.单输出；2.多输出\r\n            const { id, maxConnections, name } = targetCell.getData();\r\n\r\n            if (maxConnections === 1) {\r\n              const edges = self.graph.getEdges();\r\n              let num = 0;\r\n              for (let i = 0; i < edges.length; i++) {\r\n                const { cell: targetId } = edges[i].target\r\n                if (targetId === id) {\r\n                  num++\r\n                }\r\n                if (num > 1) {\r\n                  self.$message({\r\n                    message: `组件${name} 不支持多输入！`,\r\n                    type: 'warning'\r\n                  });\r\n                  validateEdgeStatus = false\r\n                  return validateEdgeStatus\r\n                }\r\n              }\r\n            }\r\n            edge.attr(\"line/strokeDasharray\", 0);\r\n            const params = {\r\n              from: edge.source.cell,\r\n              to: edge.target.cell,\r\n            }\r\n\r\n            edgeAdd(params).then((res) => {\r\n              if (res.code == 200) {\r\n                var sourceCell = self.graph.findViewByCell(edge.source.cell)\r\n                sourceCell.removeClass('tag-highlight')\r\n                // validateEdgeStatus = true\r\n                // return validateEdgeStatus;\r\n              } else {\r\n                self.getWorkflow()\r\n              }\r\n            })\r\n\r\n            return validateEdgeStatus\r\n          },\r\n\r\n          // 自动吸附\r\n          snap: {\r\n            radius: 50\r\n          },\r\n          createEdge() {\r\n            return new Shape.Edge({\r\n              router: {\r\n                name: 'manhattan',\r\n                args: {\r\n                  step: 30,\r\n                },\r\n              },\r\n              anchor: 'center',\r\n              connectionPoint: 'anchor',\r\n              allowBlank: false,\r\n              snap: {\r\n                radius: 20,\r\n              },\r\n              attrs: {\r\n                line: {\r\n                  stroke: \"#33aa98\",\r\n                  strokeWidth: 1.5,\r\n                  strokeDasharray: 5,\r\n                  targetMarker: {\r\n                    name: \"classic\",\r\n                    width: 7,\r\n                    height: 7\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        },\r\n        grid: {\r\n          size: 10, // 网格大小 10px\r\n          visible: false, // 渲染网格背景\r\n          type: 'mesh'\r\n        },\r\n        mousewheel: {\r\n          enabled: true,\r\n          // modifiers: \"ctrl\",\r\n          factor: 1.1,\r\n          maxScale: 1.8,\r\n          minScale: 0.5\r\n        }\r\n      });\r\n      // 设置默认缩放级别\r\n      this.graph.zoom('0.8')\r\n\r\n      Graph.registerNode(\"shape\", {\r\n        inherit: \"vue-shape\",\r\n        x: 200,\r\n        y: 150,\r\n        width: 50,\r\n        height: 50,\r\n        label: \"aaa\",\r\n        component: {\r\n          template: `<my-shape />`,\r\n          components: {\r\n            myShape\r\n          }\r\n        },\r\n      });\r\n\r\n      this.graph.on(\"blank:mousedown\", () => {\r\n        this.rightConfigActiveNameChange('')\r\n      })\r\n      this.graph.on('blank:click', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n      this.graph.on('cell:click', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n      this.graph.on('cell:change:*', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n      this.graph.on('scale', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n      this.graph.on('resize', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n      this.graph.on('translate', () => {\r\n        this.contextMenuVisible = false;\r\n      })\r\n\r\n      this.graph.on(\"node:contextmenu\", ({ e, node }) => {\r\n        if (this.lastTargetCell) {\r\n          this.lastTargetCell.removeClass('tag-highlight')\r\n        }\r\n        // console.log(node.getPosition(), 'node')\r\n        // const nodeData = node.getData()\r\n        // console.log(node.getData(), \"contextmenu\");\r\n        // const { num } = node.getData();\r\n        // node.setData({\r\n        //   num: num + 1\r\n        // });\r\n        this.currentNode = node\r\n        const p = this.graph.clientToGraph(e.clientX, e.clientY)\r\n        this.contextMenuPosition = p;\r\n        this.contextMenuVisible = true;\r\n      });\r\n\r\n      this.graph.on('node:dblclick', async ({ cell }) => {\r\n        this.dbOrrightConfig(cell)\r\n      });\r\n      this.graph.on(\"edge:dblclick\", ({ cell }) => {\r\n        this.graph.removeEdge(cell)\r\n        this.removeEdge(cell)\r\n      });\r\n\r\n      // 组件拖动改变位置时更新位置信息\r\n      this.graph.on(\"node:moved\", async ({ node }) => {\r\n        const { x, y } = node.position()\r\n        const params = [{\r\n          id: node.id,\r\n          x,\r\n          y\r\n        }]\r\n        this.updateModelPosition(params)\r\n      });\r\n\r\n\r\n\r\n      // 控制连接桩显示/隐藏\r\n      this.graph.on('node:mouseenter', () => {\r\n        // debugger\r\n        // const container = document.getElementById(\"container\")\r\n        const ports = container.querySelectorAll('.x6-port-body')\r\n        this.showPorts(ports, true)\r\n      })\r\n      this.graph.on('node:mouseleave', () => {\r\n        // const container = document.getElementById(\"container\")\r\n        const ports = container.querySelectorAll('.x6-port-body')\r\n        this.showPorts(ports, false)\r\n      })\r\n    },\r\n    showPorts(ports, show) {\r\n      for (let i = 0, len = ports.length; i < len; i = i + 1) {\r\n        ports[i].style.visibility = show ? 'visible' : 'hidden'\r\n      }\r\n    },\r\n    highlight(id) {\r\n      if (this.lastTargetCell) {\r\n        this.lastTargetCell.removeClass('tag-highlight')\r\n      }\r\n      var targetCell = this.graph.findViewByCell(id)\r\n      this.lastTargetCell = targetCell\r\n\r\n      targetCell.addClass('tag-highlight')\r\n      // targetCell.highlight(targetCell.container, {\r\n      //   highlighter: {\r\n      //     name: 'className'\r\n      //   },\r\n      // })\r\n      this.graph.matrix(null)\r\n      this.graph.centerCell(targetCell)\r\n    },\r\n    initAddon() {\r\n      const self = this;\r\n      this.dnd = new Dnd({\r\n        target: this.graph,\r\n        scaled: false,\r\n        animation: true,\r\n\r\n        // 新增组件\r\n        validateNode(node) {\r\n          const { x, y } = node.position()\r\n          // if (x < 232) return false; // 拖动组件未离开左侧菜单区域时，不允许添加\r\n          const {\r\n            // applicationCode, // 组件分类\r\n            code, // 组件code\r\n            identifier, // 组件id\r\n            instanceCode = '', // 实例code\r\n            name: moduleName, // 组件名称\r\n            maxOutputs, // 是否支持多输出， 1：单输出，2：多输出\r\n            maxConnections, // 是否支持多输入， 1：单输入，2：多输入\r\n            startFlag, // 是否是输入组件，1：是，0：否\r\n            endFlag, // 是否是输出组件， 1：是，0：否\r\n            webUrl, // 后端地址（只到后端项目地址）\r\n          } = node.getData();\r\n          const data = {\r\n            // applicationCode,\r\n            code,\r\n            identifier,\r\n            instanceCode,\r\n            moduleName,\r\n            maxOutputs,\r\n            maxConnections,\r\n            startFlag,\r\n            endFlag,\r\n            webUrl,\r\n            workflowId: self.workflowId, // 流程id\r\n            x,\r\n            y,\r\n          }\r\n          const params = {\r\n            code,\r\n            identifier,\r\n            moduleName,\r\n            workflowId: self.workflowId, // 流程id\r\n            x,\r\n            y,\r\n          }\r\n          node.data = data\r\n          return new Promise((resolve, reject) => {\r\n            nodeAdd(params).then((res) => {\r\n              if (res.code == 200) {\r\n                node.id = res.result?.id\r\n                node.data.id = res.result?.id\r\n                node.data.executeStatus = 'UNSETING'\r\n                resolve(true)\r\n              } else {\r\n                self.getWorkflow()\r\n                reject(false)\r\n              }\r\n            })\r\n          })\r\n        },\r\n        getDragNode: node => {\r\n          const data = node.data;\r\n          return this.graph.createNode({\r\n            width: 85,\r\n            height: 70,\r\n            shape: \"html\",\r\n            data,\r\n            html: () => {\r\n              const wrap = document.createElement(\"div\");\r\n              wrap.className = \"drag-node-item\";\r\n              wrap.innerHTML = `\r\n                <img class=\"node-icon\" src=\"${data.img}\" />\r\n                <span class=\"node-label\">${data.name}</span>\r\n                `;\r\n              return wrap;\r\n            }\r\n          });\r\n          // return node.clone({ keepId: true })\r\n        },\r\n        getDropNode: node => {\r\n          // const { width, height } = node.size()\r\n          // // 返回一个新的节点作为实际放置到画布上的节点\r\n          // return node.clone().size(width * 3, height * 3)\r\n          //return node.clone({ keepId: true })\r\n          const { x, y } = node.getPosition()\r\n          return this.graph.createNode({\r\n            width: 60,\r\n            height: 67,\r\n            // label: \"接入组件接入组件\",\r\n            // attrs: {\r\n            //   label: {\r\n            //     text: node.data.name,\r\n            //     fontSize: 14,\r\n            //     refY: 70 // x 轴偏移，类似 css 中的 margin-left\r\n            //     //textAnchor: 'center', // 左对齐\r\n            //   }\r\n            // },\r\n            shape: \"shape\",\r\n            x,\r\n            y,\r\n            data: node.data,\r\n            ports: {\r\n              groups: {\r\n                // 输入链接桩群组定义\r\n                in: {\r\n                  // position: {\r\n                  //   name: 'absolute',//核心\r\n                  //   args: { x: 30, y: 60 }\r\n                  // },\r\n                  position: \"left\",\r\n                  attrs: {\r\n                    circle: {\r\n                      r: 7,\r\n                      //magnet: true,\r\n                      magnet: \"passive\",\r\n                      stroke: \"#5e96fe\",\r\n                      strokeWidth: 1,\r\n                      fill: \"#fff\"\r\n                      // style: {\r\n                      //   visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示\r\n                      // },\r\n                    },\r\n                    line: {\r\n                      stroke: \"#722ed1\"\r\n                    }\r\n                  }\r\n                },\r\n                // 输出链接桩群组定义\r\n                out: {\r\n                  position: \"right\",\r\n                  label: {\r\n                    position: \"bottom\" // 标签位置\r\n                  },\r\n                  attrs: {\r\n                    // text: { text: 'out1' },\r\n                    circle: {\r\n                      r: 7,\r\n                      magnet: true,\r\n                      stroke: \"#47C769\",\r\n                      strokeWidth: 1,\r\n                      fill: \"#fff\"\r\n                    },\r\n                    line: {\r\n                      stroke: \"#722ed1\"\r\n                    }\r\n                  },\r\n                  zIndex: 1\r\n                }\r\n              },\r\n              items: this.createPorts(node.data)\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    autoLayout() {\r\n      this.isDagreLayout = true;\r\n      this.getWorkflow()\r\n    },\r\n\r\n    //右键菜单事件监听\r\n    contextMenuEvent(key) {\r\n      this.contextMenuVisible = false\r\n      // if (key === 'deleteNode' || key === 'updateName' || key === 'nodeCopy') {\r\n      //   this.getWorkflow()\r\n      // }\r\n      if (key === 'deleteNode') {\r\n        const nodeId = this.currentNode?.id\r\n        if (nodeId) {\r\n          this.graph.removeNode(nodeId)\r\n        }\r\n      }\r\n      if (key === 'getWorkflow') {\r\n        this.getWorkflow()\r\n      }\r\n      if (key === 'config') {\r\n        this.dbOrrightConfig(this.currentNode)\r\n      }\r\n\r\n      if (key === 'clearFunctionParam') {\r\n        this.getWorkflow()\r\n      }\r\n    },\r\n\r\n    //查询流程配置\r\n    async getWorkflow() {\r\n      const res = await getWorkflow({\r\n        id: this.workflowId\r\n      });\r\n      if (res.code == 200) {\r\n        const { modList, connList, notInitModList } = res.result;\r\n        this.modList = modList;\r\n        // identifier\r\n        var navList = []\r\n        this.navList.forEach(k => {\r\n          if (k.modules && k.modules.length > 0) {\r\n            navList.push(...k.modules)\r\n          }\r\n        })\r\n        this.modList.forEach(x => {\r\n          for (var i = 0; i < navList.length; i++) {\r\n            if (x.identifier == navList[i].identifier) {\r\n              // x.applicationCode = navList[i].applicationCode\r\n              x.maxOutputs = navList[i].maxOutputs\r\n              x.maxConnections = navList[i].maxConnections\r\n              x.startFlag = navList[i].startFlag\r\n              x.endFlag = navList[i].endFlag\r\n              x.webUrl = navList[i].webUrl\r\n              x.img = navList[i].img\r\n              x.executeStatus = notInitModList.includes(x.id) ? 'UNSETING' : ''\r\n              break;\r\n            }\r\n          }\r\n        })\r\n        this.connList = connList;\r\n        this.notInitModList = notInitModList;\r\n        bus.$emit('getComponentLogsBus')\r\n\r\n        this.renderNode();\r\n      }\r\n    },\r\n\r\n    //初始化画布数据\r\n    renderNode() {\r\n      let nodes = [];\r\n      this.modList.forEach(item => {\r\n        const node = {\r\n          shape: \"shape\",\r\n          id: item.id, // String，可选，节点的唯一标识\r\n          x: item.x, // Number，必选，节点位置的 x 值\r\n          y: item.y, // Number，必选，节点位置的 y 值\r\n          width: 60, // Number，可选，节点大小的 width 值\r\n          height: 67, // Number，可选，节点大小的 height 值\r\n          // label: item.name, // String，节点标签\r\n          // attrs: {\r\n          //   label: {\r\n          //     text: item.name,\r\n          //     fontSize: 14,\r\n          //     refY: 75 // x 轴偏移，类似 css 中的 margin-left\r\n          //     //textAnchor: 'center', // 左对齐\r\n          //   }\r\n          // },\r\n          data: item,\r\n          ports: {\r\n            groups: {\r\n              // 输入链接桩群组定义\r\n              in: {\r\n                // position: {\r\n                //   name: 'absolute',//核心\r\n                //   args: { x: 30, y: 60 }\r\n                // },\r\n                position: \"left\",\r\n                attrs: {\r\n                  circle: {\r\n                    r: 7,\r\n                    //magnet: true,\r\n                    magnet: \"passive\",\r\n                    stroke: \"#5e96fe\",\r\n                    strokeWidth: 1,\r\n                    fill: \"#fff\",\r\n                    style: {\r\n                      visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示\r\n                    },\r\n                  },\r\n                  line: {\r\n                    stroke: \"#722ed1\"\r\n                  }\r\n                }\r\n              },\r\n              // 输出链接桩群组定义\r\n              out: {\r\n                position: \"right\",\r\n                label: {\r\n                  position: \"bottom\" // 标签位置\r\n                },\r\n                attrs: {\r\n                  // text: { text: 'out1' },\r\n                  circle: {\r\n                    r: 7,\r\n                    magnet: true,\r\n                    stroke: \"#47C769\",\r\n                    strokeWidth: 1,\r\n                    fill: \"#fff\",\r\n                    style: {\r\n                      visibility: 'hidden',//默认隐藏，不显示。定位到节点， 才展示\r\n                    },\r\n                  },\r\n                  line: {\r\n                    stroke: \"#722ed1\"\r\n                  }\r\n                },\r\n                zIndex: 1\r\n              }\r\n            },\r\n            items: this.createPorts(item)\r\n          }\r\n        };\r\n        nodes.push(node);\r\n      });\r\n\r\n      let edges = [];\r\n      this.connList.forEach(item => {\r\n        const edge = {\r\n          source: {\r\n            cell: item.from,\r\n            port: \"portOut\"\r\n          },\r\n          target: {\r\n            cell: item.to,\r\n            port: \"portIn\"\r\n          },\r\n          // vertices: [{ x: 500, y: 200 }],\r\n          router: {\r\n            name: 'manhattan',\r\n            args: {\r\n              step: 30,\r\n            },\r\n          },\r\n          anchor: 'center',\r\n          connectionPoint: 'anchor',\r\n          allowBlank: false,\r\n          snap: {\r\n            radius: 20,\r\n          },\r\n          attrs: {\r\n            line: {\r\n              stroke: \"#33aa98\",\r\n              strokeWidth: 1.5,\r\n              strokeDasharray: 0,\r\n              targetMarker: {\r\n                name: \"classic\",\r\n                width: 7,\r\n                height: 7,\r\n              }\r\n            }\r\n          },\r\n          zIndex: 0,\r\n        };\r\n        edges.push(edge);\r\n      });\r\n\r\n      if (this.isDagreLayout) return this.renderLayoutGraph(nodes, edges)\r\n\r\n      this.graph.fromJSON({\r\n        nodes,\r\n        edges\r\n      });\r\n    },\r\n\r\n    // 删除连线\r\n    async removeEdge({ source, target }) {\r\n      const params = {\r\n        to: target.cell,\r\n        from: source.cell\r\n      }\r\n      const res = await deleteConnInfo(params)\r\n      if (res.code != 200) {\r\n        this.getWorkflow()\r\n      }\r\n    },\r\n\r\n    dragNode(MouseEvent, data) {\r\n      //const target = MouseEvent.currentTarget\r\n      const node = this.graph.createNode({\r\n        data\r\n      });\r\n      this.dnd.start(node, MouseEvent);\r\n    },\r\n\r\n    // 根据组件类型生成链接桩\r\n    createPorts(componentInfo) {\r\n      const portsItems = [];\r\n\r\n      if (componentInfo.startFlag) {\r\n        // 接入组件\r\n        portsItems.push({\r\n          id: \"portOut\",\r\n          group: \"out\",\r\n          zIndex: 10\r\n        });\r\n      } else if (componentInfo.endFlag) {\r\n        // 结束组件\r\n        portsItems.push({\r\n          id: \"portIn\",\r\n          group: \"in\",\r\n          zIndex: 10\r\n        });\r\n      } else {\r\n        // 其他组件\r\n        portsItems.push(\r\n          {\r\n            id: \"portIn\",\r\n            group: \"in\",\r\n            zIndex: 10\r\n          },\r\n          {\r\n            id: \"portOut\",\r\n            group: \"out\",\r\n            zIndex: 10\r\n          }\r\n        );\r\n      }\r\n      return portsItems;\r\n    },\r\n\r\n    centerContent() { // 画布居中对齐\r\n      this.graph.centerContent();\r\n    },\r\n\r\n    // 画布缩放\r\n    scale(level) {\r\n      const zoom = this.graph.zoom(); // 获取缩放级别\r\n      if (Math.sign(level) === 1 && zoom > 1.8) return;\r\n      if (Math.sign(level) === -1 && zoom < 0.5) return;\r\n      this.graph.zoom(level);\r\n    },\r\n\r\n    // 开/关网格\r\n    gridToggle() {\r\n      this.showGrid = !this.showGrid\r\n      this.showGrid ? this.graph.showGrid() : this.graph.hideGrid()\r\n    },\r\n\r\n    // 刷新画布\r\n    refresh() {\r\n      this.getWorkflow()\r\n    },\r\n    // 判断是否为退出全屏\r\n    checkFull() {\r\n      var isFull = document.fullscreen || document.webkitIsFullScreen || document.msFullscreenEnabled;\r\n      //to fix : false || undefined == undefined\r\n      if (isFull === undefined) { isFull = false; }\r\n      return isFull;\r\n    },\r\n    // 全屏显示\r\n    fullScreen() {\r\n      const docElm = document.documentElement\r\n      if (this.isFullScreen) {\r\n        document.exitFullscreen()\r\n      } else {\r\n        docElm.requestFullscreen()\r\n      }\r\n      // this.isFullScreen = !this.isFullScreen\r\n    },\r\n\r\n    //撤销操作\r\n    toUndo() {\r\n      this.graph.undo();\r\n    },\r\n\r\n    //重做\r\n    toRedo() {\r\n      this.graph.redo();\r\n    },\r\n\r\n    onResize() {\r\n      if (this.resizeTimer) return;\r\n      this.resizeTimer = setTimeout(() => {\r\n        this.graph.resize(\r\n          window.innerWidth,\r\n          window.innerHeight - this.headHeight\r\n        );\r\n        clearTimeout(this.resizeTimer);\r\n        this.resizeTimer = null;\r\n      }, 200);\r\n    },\r\n    // 加载iframe\r\n    loadandpostmessage(event) {\r\n      this.loading = false\r\n      var iframeUrl = ''\r\n      if (this.componentUrl.indexOf('http') > -1 || this.componentUrl.indexOf('https') > -1) {\r\n        iframeUrl = this.componentUrl\r\n      } else {\r\n        iframeUrl = window.location.origin + this.componentUrl\r\n      }\r\n      event.target.contentWindow.postMessage(\r\n        {\r\n          name: 'setIframeData',\r\n          data: {\r\n            id: this.componentId,\r\n            taskId: this.taskId,\r\n            token: window.dataOsToken,\r\n            hongfu: this.isHongfu, // 鸿富组件特殊传值\r\n            nodeData: this.nodeData, // 节点传递数据 \r\n            transformList: this.transformList // 字段类型转换规则\r\n          },\r\n          replay: false\r\n        },\r\n        iframeUrl\r\n      )\r\n    },\r\n    // 提交配置\r\n    configSubmit(msg) {\r\n      // let data = {}\r\n      console.log(JSON.parse(msg.data), '传递的data')\r\n      // data.functionParam = msg.data\r\n      // data.id = msg.id\r\n      this.dialogVisible = false // 关闭弹窗\r\n      this.loading = true // 初始化loading\r\n      this.updateStatus = true\r\n      this.updateTask(JSON.parse(msg.data), msg.id)\r\n    },\r\n    // 节点更新\r\n    async updateTask(data, id) {\r\n      // 入参父组件id矫正（修复更改父组件后，入参id还是旧父组件id问题）\r\n      this.correctParentId(data)\r\n\r\n      const res = await updateFunctionParam(data, id)\r\n      if (res.code === 200) {\r\n        this.$message({\r\n          showClose: true,\r\n          message: '保存成功',\r\n          type: 'success'\r\n        })\r\n        this.getWorkflow()\r\n      }\r\n\r\n\r\n      // console.log(res,\"节点更新\")\r\n      // if (this.updateStatus) {\r\n      //   // 需要提示状态的时候\r\n      //   if (res.code == 200) {\r\n      //     res.data.forEach(item => {\r\n      //       this.data.nodeList.find(n => n.id === item.id).runStatus = 4\r\n      //     })\r\n      //     this.$message({\r\n      //       showClose: true,\r\n      //       message: res.message,\r\n      //       type: 'success'\r\n      //     })\r\n      //   } else {\r\n      //     this.$message({\r\n      //       showClose: true,\r\n      //       message: res.message,\r\n      //       type: 'error'\r\n      //     });\r\n      //   }\r\n      //   this.updateStatus = false\r\n      // }\r\n    },\r\n    // 确认配置\r\n    onConfirmation() {\r\n      // debugger\r\n      // console.log(this.$store.state.tableData)\r\n      var iframeUrl = ''\r\n      if (this.componentUrl.indexOf('http') > -1 || this.componentUrl.indexOf('https') > -1) {\r\n        iframeUrl = this.componentUrl\r\n      } else {\r\n        iframeUrl = window.location.origin + this.componentUrl\r\n      }\r\n      this.$refs.myApplication.contentWindow.postMessage(\r\n        {\r\n          name: 'saveConfig',\r\n          data: '',\r\n          replay: false\r\n        },\r\n        iframeUrl\r\n      )\r\n    },\r\n    isEqual(a, b) {\r\n      //如果a和b本来就全等\r\n      if (a === b) {\r\n        //判断是否为0和-0\r\n        return a !== 0 || 1 / a === 1 / b;\r\n      }\r\n      //判断是否为null和undefined\r\n      if (a == null || b == null) {\r\n        return a === b;\r\n      }\r\n      //接下来判断a和b的数据类型\r\n      var classNameA = toString.call(a),\r\n        classNameB = toString.call(b);\r\n      //如果数据类型不相等，则返回false\r\n      if (classNameA !== classNameB) {\r\n        return false;\r\n      }\r\n      //如果数据类型相等，再根据不同数据类型分别判断\r\n      switch (classNameA) {\r\n        case '[object RegExp]':\r\n        case '[object String]':\r\n          //进行字符串转换比较\r\n          return '' + a === '' + b;\r\n        case '[object Number]':\r\n          //进行数字转换比较,判断是否为NaN\r\n          if (+a !== +a) {\r\n            return +b !== +b;\r\n          }\r\n          //判断是否为0或-0\r\n          return +a === 0 ? 1 / +a === 1 / b : +a === +b;\r\n        case '[object Date]':\r\n        case '[object Boolean]':\r\n          return +a === +b;\r\n      }\r\n      //如果是对象类型\r\n      if (classNameA == '[object Object]') {\r\n        //获取a和b的属性长度\r\n        var propsA = Object.getOwnPropertyNames(a),\r\n          propsB = Object.getOwnPropertyNames(b);\r\n        if (propsA.length != propsB.length) {\r\n          return false;\r\n        }\r\n        for (var i = 0; i < propsA.length; i++) {\r\n          var propName = propsA[i];\r\n          //如果对应属性对应值不相等，则返回false\r\n          if (propName === 'type') {\r\n            if (a[propName].toUpperCase() !== b[propName].toUpperCase()) {\r\n              return false;\r\n            }\r\n          } else if (a[propName] !== b[propName] && propName !== 'primary') {\r\n            return false;\r\n          }\r\n        }\r\n        return true;\r\n      }\r\n      //如果是数组类型\r\n      if (classNameA == '[object Array]') {\r\n        if (a.toString() == b.toString()) {\r\n          return true;\r\n        }\r\n        return false;\r\n      }\r\n    },\r\n    // 双击或者右键点击配置\r\n    async dbOrrightConfig(node) {\r\n      var targetCell = this.graph.findViewByCell(node.data.id)\r\n      targetCell.removeClass('tag-highlight')\r\n      const item = node.data\r\n      // 点击配置后,请把组件id赋值给this.componentId\r\n      // 然后调用this.openUrl(item.name, this.dataCleanformatclean),第二个参数为组件对应的irameUrl\r\n      console.log(item, \"点击了配置按钮\");\r\n      let res = {}\r\n      let params = {\r\n        selfJson: null,\r\n        parentJson: []\r\n      }\r\n      let dialogStatus = false\r\n      // 判断是否为接入组件\r\n      if (!item.startFlag) {\r\n        // 非选取数据点击“配置” 1.先判断是否有连线 2.再判断是否有配置传递\r\n        // 判断连接状态\r\n        const edgesArr = this.graph.getIncomingEdges(node)\r\n        if (edgesArr) {\r\n          // 判断是否有配置传递\r\n          let parentIds = []\r\n          let selfId = []\r\n          let parentJsonArr = []\r\n          edgesArr.forEach(item => {\r\n            parentIds.push(item.source.cell)\r\n          })\r\n          selfId = edgesArr[0].target.cell\r\n          res = await queryPreviousAndSelfParam({ ids: parentIds.toString() + ',' + selfId })\r\n          res.result.forEach(item => {\r\n            if (item.moduleId === selfId) {\r\n              params.selfJson = item.functionParam\r\n            }\r\n            parentIds.forEach(x => {\r\n              if (x === item.moduleId && item.functionParam) {\r\n                var arr = JSON.parse(item.functionParam)\r\n                arr.forEach(i => {\r\n                  i.id = item.moduleId\r\n                  // 将functionParam里sourceFields = targetFields\r\n                  i.sourceFields = i.targetFields\r\n                })\r\n                params.parentJson.push(JSON.stringify(arr))\r\n                parentJsonArr.push(...arr)\r\n              }\r\n            })\r\n          })\r\n          debugger\r\n          if (params.selfJson && JSON.parse(params.selfJson).length !== parentJsonArr.length) { // 如果数量不一致则清空\r\n            params.selfJson = null\r\n            console.log('上游组件变更或上游组件数据变更,数据重置')\r\n          } else if (params.selfJson && JSON.parse(params.selfJson).length === parentJsonArr.length) { // 数量一致先判断组件是否被替换过\r\n            debugger\r\n            const parseSelfJson = JSON.parse(params.selfJson)\r\n            let isReplace = parseSelfJson.filter(x => parentJsonArr.some(y => y.id === x.id)).length === parseSelfJson.length ? false : true // 取交集判断是否替换过\r\n            if (isReplace) {  // 替换过就判断是否多输入\r\n              if (parseSelfJson.length === 1) { // 不是多输入判断字段是否一致，一致就不清空。\r\n                if (!(parseSelfJson[0].sourceFields.length === parentJsonArr[0].targetFields.length\r\n                  && parseSelfJson[0].sourceFields.every(a => parentJsonArr[0].targetFields.some(b => this.isEqual(a, b)))\r\n                  && parentJsonArr[0].targetFields.every(_b => parseSelfJson[0].sourceFields.some(_a => this.isEqual(_a, _b))))\r\n                ) {\r\n                  params.selfJson = null\r\n                }\r\n              } else {\r\n                params.selfJson = null\r\n              }\r\n            } else { // 没有替换过\r\n              parentJsonArr.sort((a, b) => { // 先排序\r\n                return parseSelfJson.findIndex(x => a.id === x.id) - parseSelfJson.findIndex(y => b.id === y.id)\r\n              })\r\n              var isFieldsChange = false // 判断组件中参数是否有改变\r\n              for (var i = 0; i < parseSelfJson.length; i++) {\r\n                for (var k = 0; k < parentJsonArr.length; k++) {\r\n                  // 如果父组件组件是文件则不进行比较\r\n                  if( parentJsonArr[k].dataType === 'file') break;\r\n                  if (parseSelfJson[i].id === parentJsonArr[k].id) {\r\n                    if (!(parseSelfJson[i].sourceFields.length === parentJsonArr[k].targetFields.length\r\n                      && parseSelfJson[i].sourceFields.every(a => parentJsonArr[k].targetFields.some(b => this.isEqual(a, b)))\r\n                      && parentJsonArr[k].targetFields.every(_b => parseSelfJson[i].sourceFields.some(_a => this.isEqual(_a, _b))))\r\n                    ) {\r\n                      // 调试\r\n                      // parseSelfJson[i].sourceFields.every(a => {\r\n                      //   const isSome = parentJsonArr[k].targetFields.some(b => this.isEqual(a, b))\r\n                      //   if(!isSome) {\r\n                      //     console.log(a);\r\n                      //     console.log(k);\r\n                      //   }\r\n                      //   return isSome\r\n                      // })\r\n                      isFieldsChange = true\r\n                      break;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n              if (isFieldsChange) { // 有改变，清空\r\n                params.selfJson = null\r\n              } else {\r\n                params.parentJson = parentJsonArr.map(x => JSON.stringify([x]))\r\n              }\r\n            }\r\n          }\r\n          if (params.parentJson.length == 0) {\r\n            this.$message.error('请先完成前置流程配置！')\r\n            return false\r\n          } else {\r\n            // 有连线且有配置-->可以弹窗\r\n            dialogStatus = true\r\n          }\r\n        } else {\r\n          // 提示 未连线\r\n          this.$message.error('请先进行流程连接！')\r\n          return\r\n        }\r\n      } else {\r\n        dialogStatus = true\r\n        res = await queryPreviousAndSelfParam({ ids: item.id })\r\n        params.parentJson = ''\r\n        params.selfJson = res.result[0].functionParam\r\n        // console.log(res,\"点击了选取的配置按钮\")\r\n      }\r\n      if (!dialogStatus) {\r\n        return false\r\n      }\r\n      this.loading = true\r\n      this.dialogWidth = '800'\r\n      if (\r\n        item.code === 'storageData' ||\r\n        item.code === 'storageClibData' ||\r\n        item.code === 'storageDataBySink'\r\n      ) {\r\n        this.dialogWidth = '30%'\r\n      }\r\n      if (res.code === 200) {\r\n        this.nodeData = params\r\n      }\r\n      // const url = window.componentsUrl.find(n => n.code === item.code).url\r\n      const url = item.webUrl\r\n      // const url = 'http://************:12029'\r\n      this.openUrl(item.name, item.id, url)\r\n      // this.openUrl(item.name, item.id, 'http://localhost:8081')\r\n      // switch (item.startFlag) {\r\n      //   case true:\r\n      //   // 接入组件\r\n      //   this.openUrl(item.name, item.id, 'http://************:12029')\r\n      //   break\r\n      //   case 'stringProcess':\r\n      //     // 字符串处理\r\n      //     this.openUrl(item.name, item.id, 'http://************:8610')\r\n      //     break\r\n      //   case 'renameFields':\r\n      //     // 字段重命名\r\n      //     this.openUrl(item.name, item.id, 'http://************:8613')\r\n      //     break\r\n      //   case 'chooseData':\r\n      //     // 选取组件\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12010/#')\r\n      //     break\r\n      //   case 'chooseShareData':\r\n      //     // 选取组件\r\n      //     this.openUrl(item.name, item.id, /* url */ 'http://************:12029' /* 'http://service.testbuild.youedata.cc/shujufuwujieru-1212/web-access-data-services' */)\r\n      //     break\r\n      //   case 'chooseShareDataSpark':\r\n      //     // 选取组件\r\n      //     this.openUrl(item.name, item.id, 'http://************:12029' /* 'http://service.testbuild.youedata.cc/shujufuwujieru-1212/web-access-data-services' */)\r\n      //     break\r\n      //   case 'nullProcess':\r\n      //     // 空值预处理组件\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12023' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanNullProcess' */\r\n      //     )\r\n      //     break\r\n      //   case 'dataRepeat':\r\n      //     // 数据去重组件\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12024')\r\n      //     break\r\n      //   case 'formatClean':\r\n      //     // 格式清洗组件\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12030' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFormatClean' */\r\n      //     )\r\n      //     break\r\n      //   case 'logicError':\r\n      //     // 逻辑错误清洗组件\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12026' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanLogicError' */\r\n      //     )\r\n      //     break\r\n      //   case 'fieldMerge':\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12022' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFieldMerge' */\r\n      //     )\r\n      //     break\r\n      //   case 'fieldSplit':\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12021' /* 'http://fanso.testbuild.youedata.cc/subassembly/cleanFieldSplit' */\r\n      //     )\r\n      //     break\r\n      //   case 'dataOrder':\r\n      //     this.openUrl(item.name, item.id, 'http://**********:8604')\r\n      //     break\r\n      //   case 'appStorage':\r\n      //     // 储存组件\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12060' /* 'http://fanso.testbuild.y   ouedata.cc/subassembly/appStoreupdata' */\r\n      //     )\r\n      //     break\r\n      //   case 'idcardFormat':\r\n      //     // 身份证组件\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12022')\r\n      //     break\r\n      //   case 'storageClibData':\r\n      //     // 储存组件\r\n      //     // this.dialogWidth = '30%'\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://**********:12060' /* 'http://fanso.testbuild.youedata.cc/subassembly/appStoreupdata' */\r\n      //     )\r\n      //     break\r\n      //   case 'storageDataBySink':\r\n      //     // 储存组件\r\n      //     // this.dialogWidth = '30%'\r\n      //     this.openUrl(\r\n      //       item.name,\r\n      //       item.id,\r\n      //       'http://************:12060' /* 'http://fanso.testbuild.youedata.cc/subassembly/appStoreupdata' */\r\n      //     )\r\n      //     break //\r\n      //   case 'outStorage':\r\n      //     // 外部存储\r\n      //     this.openUrl(item.name, item.id, 'http://************:12060')\r\n      //     break\r\n      //   case 'formatCleanClibArrow':\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12027')\r\n      //     break\r\n      //   case 'formatCleanClibFindchips':\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12028')\r\n      //     break\r\n      //   case 'resourceAnalysis':\r\n      //     // 文件解析\r\n      //     this.openUrl(item.name, item.id, 'http://**********:12030')\r\n      //     break\r\n      //   case 'multiTableJoin':\r\n      //     // 多表拼接\r\n      //     this.openUrl(item.nanme, item.id, 'http://************:8606')\r\n      //     break\r\n      //   case 'dataCalculate':\r\n      //     // 数据计算\r\n      //     this.openUrl(item.name, item.id, 'http://**********:8603')\r\n      //     break\r\n      //   case 'newFields':\r\n      //     // 新增字段\r\n      //     this.openUrl(item.name, item.id, 'http://************:8608')\r\n      //     break\r\n      //   case 'newFieldsSpark':\r\n      //     // 新增字段spark\r\n      //     this.openUrl(item.name, item.id, 'http://************:8608')\r\n      //     break\r\n      //   case 'dataUnion':\r\n      //     // 数据拼接\r\n      //     this.openUrl(item.name, item.id, 'http://************:8609')\r\n      //     break\r\n      //   case 'dataTypeConvert':\r\n      //     // 格式转换\r\n      //     this.openUrl(item.name, item.id, 'http://************:8607')\r\n      //     break\r\n      //   case '':\r\n      //     // 行列转换\r\n      //     this.openUrl(item.name, item.id, 'http://localhost:12399')\r\n      //     break\r\n      //   default:\r\n      //     this.$message.error('code没写入switch语句')\r\n      //     this.openUrl(item.name, item.id, url)\r\n      //     break\r\n      // }\r\n    },\r\n    // 关闭desc查看页面\r\n    close() {\r\n      this.$refs.showDesc.click()\r\n    },\r\n    // 打开页面\r\n    openUrl(title, id, url) {\r\n      // console.log(this.data.nodeList)\r\n      this.componentUrl =\r\n        url +\r\n        '/?accountToken=' +\r\n        window.dataOsToken +\r\n        '&accountId=' +\r\n        window.accountId +\r\n        '&categoryService=' +\r\n        '2'\r\n      this.templateTitle = title\r\n      this.componentId = id\r\n      this.dialogVisible = true\r\n    },\r\n    ...mapMutations(['rightConfigActiveNameChange']),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import \"../assets/style/elenentReset_dialog.scss\";\r\n\r\n.theCanvas {\r\n  transition: all 0.4s;\r\n}\r\n\r\n.node-shape-box {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.node-shape-name {\r\n  position: absolute;\r\n  bottom: 0px;\r\n  left: 0px;\r\n  width: 100%;\r\n  text-align: center;\r\n  font-weight: normal;\r\n  font-size: 12px;\r\n}\r\n\r\n.node-shape {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  transition: all .3s;\r\n\r\n  &:hover {\r\n    background-color: #b7e5d7;\r\n    box-shadow: 0 0 5px #dadada;\r\n  }\r\n}\r\n\r\n.sharp {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #DCDFE6;\r\n  width: calc(100% - 10px);\r\n  height: calc(100% - 10px);\r\n  clip-path: polygon(50% 0, 100% 25%, 100% 75%, 50% 100%, 0 75%, 0 25%);\r\n}\r\n\r\n.tag-highlight {\r\n  .sharp {\r\n    //  background-color: #276EB7;\r\n    // background: url('../assets/images/pic_line_shadow.png') no-repeat;\r\n    // clip-path: none;\r\n    // background-color: ;\r\n    // background: linear-gradient(90deg, #223D7C 0%, #276EB7 100%);\r\n    // box-shadow: 0px 0px 6px 0px #276EB7;\r\n    // border: 1px solid #276EB7;\r\n  }\r\n\r\n  .sharpb {\r\n    background: url('../assets/images/pic_line_shadow.png') no-repeat;\r\n    width: calc(100% - 0px);\r\n    background-size: cover;\r\n    clip-path: none;\r\n  }\r\n}\r\n\r\n.sharpb {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #fff;\r\n  width: calc(100% - 2px);\r\n  height: calc(100% - 2px);\r\n  clip-path: polygon(50% 0, 100% 25%, 100% 75%, 50% 100%, 0 75%, 0 25%);\r\n\r\n  .node-shape-icon {\r\n    width: 60%;\r\n    height: auto;\r\n  }\r\n}\r\n\r\n.drag-node-item {\r\n  width: 85px;\r\n  height: 70px;\r\n  background: #fff;\r\n  position: relative;\r\n  box-shadow: 0 0 10px 1px #dadada;\r\n  padding: 6px 5px 0;\r\n\r\n  .node-icon {\r\n    display: block;\r\n    width: 28px;\r\n    height: 28px;\r\n    margin: 0 auto;\r\n    background-size: 22px;\r\n    background-repeat: no-repeat;\r\n    background-position: 50%;\r\n  }\r\n\r\n  .node-label {\r\n    display: block;\r\n    height: 32px;\r\n    font-size: 12px;\r\n    line-height: 16px;\r\n    color: #333;\r\n    text-align: center;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./graphCanvas.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./graphCanvas.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./graphCanvas.vue?vue&type=template&id=1bc3ea05&\"\nimport script from \"./graphCanvas.vue?vue&type=script&lang=js&\"\nexport * from \"./graphCanvas.vue?vue&type=script&lang=js&\"\nimport style0 from \"./graphCanvas.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"right-config-bar\",style:({right: _vm.$store.state.rightConfigActiveName === '' ? 0 : '361px'})},[_c('a',{class:{active: _vm.$store.state.rightConfigActiveName === '资源配置'},on:{\"click\":function($event){return _vm.rightConfigActiveNameChange('资源配置')}}},[_vm._v(\"资源配置\")]),_c('a',{class:{active: _vm.$store.state.rightConfigActiveName === '调度配置'},on:{\"click\":function($event){return _vm.rightConfigActiveNameChange('调度配置')}}},[_vm._v(\"调度配置\")])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!-- 右侧流程配置导航 -->\r\n<template>\r\n  <section class=\"right-config-bar\" :style=\"{right: $store.state.rightConfigActiveName === '' ? 0 : '361px'}\">\r\n    <a :class=\"{active: $store.state.rightConfigActiveName === '资源配置'}\" @click=\"rightConfigActiveNameChange('资源配置')\">资源配置</a>\r\n    <a :class=\"{active: $store.state.rightConfigActiveName === '调度配置'}\" @click=\"rightConfigActiveNameChange('调度配置')\">调度配置</a>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport { mapMutations } from 'vuex'\r\nexport default {\r\n  name: 'rightConfigBar',\r\n  methods: {\r\n    ...mapMutations(['rightConfigActiveNameChange'])\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.right-config-bar {\r\n  transition: all .5s cubic-bezier(.4,0,.2,1) 0s;\r\n  position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: 32px;\r\n    bottom: 0;\r\n    z-index: 3;\r\n    // background: #f2f5fc;\r\n    // border-left: 1px solid #e2e2e2;\r\n    border-right: 1px solid #DCDFE6;\r\n    // padding: 8px 0 0;\r\n    user-select: none;\r\n    a {\r\n      display: block;\r\n      margin-top: 8px;\r\n      padding: 15px 0;\r\n      text-indent: 5px;\r\n      letter-spacing: 5px;\r\n      width: 32px;\r\n      line-height: 32px;\r\n      writing-mode: vertical-lr;\r\n      text-align: center;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      background: #FFFFFF;\r\n      border-radius: 4px 0px 0px 4px;\r\n      border: 1px solid #DCDFE6;\r\n      cursor: pointer;\r\n      &.active {\r\n        color: #276EB7;\r\n        text-decoration: none;\r\n        background: #E9F0F7;\r\n        border: 1px solid #A9C5E2;\r\n      }\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-bar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./right-config-bar.vue?vue&type=template&id=bee2de5a&scoped=true&\"\nimport script from \"./right-config-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./right-config-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./right-config-bar.vue?vue&type=style&index=0&id=bee2de5a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bee2de5a\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"right-config-panel\",style:({right: _vm.$store.state.rightConfigActiveName === '资源配置' ? 0 : ''})},[_c('div',{staticClass:\"right-config-form\"},[_c('div',{staticClass:\"workflow-resource-config\"},[_c('h2',[_vm._v(\"资源配置\")]),(_vm.resourceList.length)?_c('div',{staticClass:\"form-group\"},[_c('el-form',{ref:\"resourceForm\",attrs:{\"model\":_vm.resourceForm,\"label-width\":\"100px\",\"label-position\":\"top\",\"size\":\"small\"}},[_c('el-form-item',{staticStyle:{\"margin-bottom\":\"25px\"},attrs:{\"label\":\"运行资源\",\"prop\":\"resourceCode\",\"rules\":[\n              { required: true, message: '请选择运行资源', trigger: 'blur,change' } ]}},[_c('template',{slot:\"label\"},[_vm._v(\" 运行资源 \"),_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"dark\",\"content\":\"任务运行所依赖的资源环境\",\"placement\":\"right\"}},[_c('em',{staticClass:\"el-icon-question\"})])],1),_c('el-select',{attrs:{\"placeholder\":\"请选择\"},on:{\"change\":_vm.handleResourceChange},model:{value:(_vm.resourceForm.resourceCode),callback:function ($$v) {_vm.$set(_vm.resourceForm, \"resourceCode\", $$v)},expression:\"resourceForm.resourceCode\"}},_vm._l((_vm.resourceList),function(item){return _c('el-option',{key:item.resourceCode,attrs:{\"label\":item.name,\"value\":item.id}})}),1)],2)],1),(!_vm.configData.length)?_c('div',{staticStyle:{\"text-align\":\"center\",\"color\":\"#999\",\"padding-bottom\":\"50px\"}},[_vm._v(\"暂无配置项\")]):_vm._e(),_c('el-form',{ref:\"configForm\",attrs:{\"model\":_vm.configForm,\"label-width\":\"100px\",\"label-position\":\"top\",\"size\":\"small\"}},[_vm._l((_vm.configData),function(item){return _c('el-form-item',{key:item.itemKey,attrs:{\"label\":item.itemKey,\"prop\":item.itemKey,\"rules\":[\n              { validator: _vm.validateNumber, trigger: 'blur' }\n            ]}},[_c('template',{slot:\"label\"},[_vm._v(\" \"+_vm._s(item.itemKey)+\" \"),_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"dark\",\"content\":item.itemDesc,\"placement\":\"right\"}},[_c('em',{staticClass:\"el-icon-question\"})])],1),_c('el-input',{model:{value:(_vm.configForm[item.itemKey]),callback:function ($$v) {_vm.$set(_vm.configForm, item.itemKey, $$v)},expression:\"configForm[item.itemKey]\"}},[_c('template',{slot:\"append\"},[_vm._v(_vm._s(item.itemUnit))])],2)],2)}),_c('el-form-item',{staticStyle:{\"text-align\":\"center\",\"padding-top\":\"15px\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"disabled\":!_vm.configData.length},on:{\"click\":_vm.submitForm}},[_vm._v(\"保存\")])],1)],2)],1):_c('div',[_vm._v(\"请先到AI引擎管理平台的资源列表注册任务调度需要的资源\")])])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import feach from '../libs/feach'\r\n\r\nconst baseUrl = window.applicationServerPath\r\n// const dataOsUrl = window.dataos_urlDaasMeta\r\n// const jobUrl = window.executorgover\r\n\r\n\r\n// dataos可运行资源查询\r\nexport function dataosResource(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/common/resources',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 可运行资源查询\r\nexport function getResourceList(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/resource/dataos',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n//清空资源配置\r\nexport function clearConfig(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}/resource/config/delete`,\r\n    method: 'post',\r\n    // params\r\n  })\r\n}\r\n\r\n// 当前流程资源配置查询\r\nexport function getConfig(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}/execute_resources`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n// 保存资源配置\r\nexport function updateConfig(data, workflowId) {\r\n  return feach({\r\n    // url: baseUrl + '/api/v2/workflow/execute/addConfig',\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/execute_resources`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 资源配置项查询\r\nexport function resourceConfig(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/common/workflow/execute/config/item/${params.workflowCategory}`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n// 调度配置\r\n// 新增调度\r\nexport function scheduleAdd(data, workflowId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/schedules`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新调度\r\nexport function scheduleUpdate(data, workflowId, scheduleId) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${workflowId}/schedules/${scheduleId}/update`,\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 查询调度信息\r\nexport function scheduleInfo(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}/schedules`,\r\n    method: 'get',\r\n    // params\r\n  })\r\n}\r\n\r\n// 清空调度信息\r\nexport function scheduleClear(params) {\r\n  return feach({\r\n    url: baseUrl + `/api/v2/workflows/${params.workflowId}/schedules/${params.scheduleId}/delete`,\r\n    method: 'post',\r\n    // params\r\n  })\r\n}\r\n\r\n// 校验cron表达式\r\nexport function cronValid(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/common/cron/valid',\r\n    method: 'post',\r\n    params\r\n  })\r\n}\r\n\r\n// 解析cron表达式\r\nexport function commonCron(params) {\r\n  return feach({\r\n    url: baseUrl + '/api/v2/common/cron',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n", "<!-- 资源配置 -->\r\n<template>\r\n  <section class=\"right-config-panel\" :style=\"{right: $store.state.rightConfigActiveName === '资源配置' ? 0 : ''}\">\r\n    <div class=\"right-config-form\">\r\n      <div class=\"workflow-resource-config\">\r\n        <h2>资源配置</h2>\r\n        <div class=\"form-group\" v-if=\"resourceList.length\">\r\n          <el-form :model=\"resourceForm\" ref=\"resourceForm\" label-width=\"100px\" label-position=\"top\" size=\"small\">\r\n            <el-form-item label=\"运行资源\" prop=\"resourceCode\" style=\"margin-bottom: 25px\"\r\n              :rules=\"[\r\n                { required: true, message: '请选择运行资源', trigger: 'blur,change' },\r\n              ]\"\r\n            >\r\n              <template slot=\"label\">\r\n                运行资源\r\n                <el-tooltip class=\"item\" effect=\"dark\" content=\"任务运行所依赖的资源环境\" placement=\"right\">\r\n                  <em class=\"el-icon-question\"></em>\r\n                </el-tooltip>\r\n              </template>\r\n              <el-select v-model=\"resourceForm.resourceCode\" @change=\"handleResourceChange\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in resourceList\" :label=\"item.name\" :value=\"item.id\" :key=\"item.resourceCode\"></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-form>\r\n\r\n          <div style=\"text-align: center; color: #999; padding-bottom: 50px;\" v-if=\"!configData.length\">暂无配置项</div>\r\n          <el-form :model=\"configForm\" ref=\"configForm\" label-width=\"100px\" label-position=\"top\" size=\"small\">\r\n            \r\n            <el-form-item v-for=\"(item) in configData\" :label=\"item.itemKey\" :prop=\"item.itemKey\"\r\n              :rules=\"[\r\n                { validator: validateNumber, trigger: 'blur' }\r\n              ]\"\r\n              :key=\"item.itemKey\"\r\n            >\r\n              <template slot=\"label\">\r\n                {{item.itemKey}}\r\n                <el-tooltip class=\"item\" effect=\"dark\" :content=\"item.itemDesc\" placement=\"right\">\r\n                  <em class=\"el-icon-question\"></em>\r\n                </el-tooltip>\r\n              </template>\r\n              <el-input v-model=\"configForm[item.itemKey]\">\r\n                <template slot=\"append\">{{item.itemUnit}}</template>\r\n              </el-input>\r\n            </el-form-item>\r\n\r\n            <el-form-item style=\"text-align: center; padding-top: 15px\">\r\n              <!-- <el-button @click=\"resetForm\">清空</el-button> -->\r\n              <el-button type=\"primary\" :disabled=\"!configData.length\" @click=\"submitForm\">保存</el-button>\r\n            </el-form-item>\r\n            \r\n          </el-form>\r\n          \r\n        </div>\r\n        <div v-else>请先到AI引擎管理平台的资源列表注册任务调度需要的资源</div>\r\n      </div>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\n\r\nimport { dataosResource, getConfig, resourceConfig, updateConfig, clearConfig } from '@/api/workflowConfig'\r\nexport default {\r\n  name: 'rightConfigResource',\r\n  data() {\r\n    return {\r\n      workflowId: '',\r\n      savedConfig: {},\r\n      resourceForm: {\r\n        resourceCode: '', // 资源code\r\n      },\r\n      configForm: {\r\n        //\r\n      },\r\n      resourceList: [ // 运行资源列表\r\n        {\r\n          \"createTime\": \"cupidatat tempor deserunt do pariatur\",\r\n          \"resourceCode\": \"1\",\r\n          \"resourceType\": \"mysql\",\r\n          \"resourceName\": \"mysql\"\r\n        },\r\n        {\r\n          \"createTime\": \"laboris\",\r\n          \"resourceCode\": \"2\",\r\n          \"resourceType\": \"spark\",\r\n          \"resourceName\": \"spark\"\r\n        }\r\n      ],\r\n      allConfigData: [], // 所有配置项数据\r\n      configData: [\r\n        // {\r\n        //   code: 'mysql',   \r\n        //   key: 'aaaaaaa', // 配置项名称\r\n        //   value: 1, // 配置项值\r\n        //   desc: '描述1', // 配置项描述\r\n        //   unit: 'G', // 配置项单位\r\n        // },\r\n        // {\r\n        //   code: 'mysql', \r\n        //   key: 'bbbbbbb', // 配置项名称\r\n        //   value: 2, // 配置项值\r\n        //   desc: '描述2', // 配置项描述\r\n        //   unit: '个', // 配置项单位\r\n        // }\r\n      ], // 配置项数据\r\n      //configList: [], // 当前选中资源对应的配置项列表\r\n    }\r\n  },\r\n  watch: {\r\n    configData(newValue) {\r\n      let configForm = {}\r\n      newValue.forEach((item) => {\r\n        configForm[item.itemKey] = item['itemValue']\r\n      })\r\n      this.configForm = configForm\r\n    },\r\n    '$store.state.rightConfigActiveName'(val) {\r\n      if (val === '资源配置') {\r\n        // this.getScheduleInfo()\r\n        this.getDataosResource()\r\n      }\r\n      // this.currentTime = new Date().getTime()\r\n    }\r\n  },\r\n  created() {\r\n    // this.workflowId = this.$route.query.workflowId\r\n    // this.category =  this.$route.query.category\r\n    this.workflowId = window.GetQueryValue('workflowId')\r\n    this.category = window.GetQueryValue('category')\r\n    this.getDataosResource()\r\n    this.getSavedConfig()\r\n  },\r\n  methods: {\r\n    async getDataosResource() {\r\n      const data = {\r\n        resourceType: this.category\r\n      }\r\n      const res = await dataosResource(data);\r\n      if (res.code == 200) {\r\n        this.resourceList = res.result\r\n      }\r\n      this.getSavedConfig()\r\n    },\r\n    async getSavedConfig() {\r\n      const res = await getConfig({ // 获取上次保存的配置项\r\n        workflowId: this.workflowId\r\n      })\r\n      if (res.code == 200 && res.result.resourceId) { // 如果有保存的值\r\n        // this.savedConfig = res.result\r\n        this.resourceForm.resourceCode = res.result.resourceId\r\n        const configData = res.result.workflowExecuteConfigBaseVOs\r\n        configData.forEach(x => {\r\n          x.itemDesc = x.confDesc\r\n          x.itemUnit = x.unit\r\n          x.itemValue = x.value\r\n        })\r\n\r\n        this.configData = configData\r\n      }\r\n    },\r\n    async handleResourceChange() { // 获取运行资源下的配置项\r\n      // if(this.savedConfig.resourceCode === val) {\r\n      //   this.configData = JSON.parse(this.savedConfig.resourceConfig)\r\n      //   return\r\n      // }\r\n      const res = await resourceConfig({workflowCategory: this.category});\r\n      if (res.code == 200) {\r\n        this.configData = res.result\r\n        this.$refs['configForm'].resetFields();\r\n        this.configForm = {}\r\n      }\r\n    },\r\n    async resetForm() {\r\n      this.$refs['configForm'].resetFields();\r\n      const res = await clearConfig({workflowId: this.workflowId});\r\n      if (res.code == 200) {\r\n        // this.savedConfig = {}\r\n        this.configForm = {}\r\n        this.$message({\r\n          message: '清空成功',\r\n          type: 'success'\r\n        });\r\n      }\r\n    },\r\n    submitForm() {\r\n      \r\n      this.$refs['resourceForm'].validate((valid) => {\r\n        if (valid) {\r\n          this.$refs['configForm'].validate((valid2) => {\r\n            if (valid2) {\r\n              const configData = JSON.parse(JSON.stringify(this.configData))\r\n              configData.forEach((item) => {\r\n                item.value = this.configForm[item.itemKey]\r\n                item.unit = item.itemUnit\r\n                item.confDesc = item.itemDesc\r\n                delete item.itemUnit\r\n                delete item.itemDesc\r\n                delete item.itemValue\r\n              })\r\n             \r\n              const data = {\r\n                // workflowId: this.workflowId, // 流程id\r\n                resourceId: this.resourceForm.resourceCode, // dataos的资源code\r\n                // type: this.category, // 资源类型：mysql，spark\r\n                executeConfigParams: configData // 资源配置\r\n              }\r\n              updateConfig(data, this.workflowId).then((res) => {\r\n                if (res.code == 200) {\r\n                  this.getSavedConfig()\r\n                  this.$message({\r\n                    message: '保存成功',\r\n                    type: 'success'\r\n                  });\r\n                }\r\n              })\r\n            } else {\r\n              console.log('error submit!!');\r\n              return false;\r\n            }\r\n          });\r\n        } else {\r\n          console.log('error submit!!');\r\n          return false;\r\n        }\r\n      });\r\n      \r\n    },\r\n\r\n    validateNumber (rule, value, callback) {\r\n      if (!(/(^[1-9]\\d*$)/.test(value))) {\r\n        callback(new Error('配置项必须为正整数'));\r\n      } else {\r\n        callback();\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.workflow-resource-config {\r\n  width: 100%;\r\n  padding: 0 24px 12px;\r\n  h2 {\r\n    height: 60px;\r\n    line-height: 60px;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    // border-bottom: 1px solid #e2e2e2;\r\n  }\r\n  .form-group {\r\n    .el-form-item--small .el-form-item__label {\r\n      line-height: 32px;\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #606266;\r\n    }\r\n    .el-form--label-top .el-form-item__label {\r\n      padding: 0 0 0;\r\n    }\r\n    .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {\r\n      margin-bottom: 9px;\r\n    }\r\n    .el-select {\r\n      display: initial;\r\n    }\r\n    .el-input-group__append {\r\n      padding: 0 10px;\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      background-color: transparent;\r\n      border: none;\r\n      color: #303133;\r\n    }\r\n    .el-input-group--append .el-input__inner, .el-input-group__prepend {\r\n      border-top-right-radius: 4px;\r\n      border-bottom-right-radius: 4px;\r\n    }\r\n    .el-button--primary {\r\n      border-color: #276EB7;\r\n      background-color: #276EB7;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-resource.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-resource.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./right-config-resource.vue?vue&type=template&id=25042372&\"\nimport script from \"./right-config-resource.vue?vue&type=script&lang=js&\"\nexport * from \"./right-config-resource.vue?vue&type=script&lang=js&\"\nimport style0 from \"./right-config-resource.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"right-config-panel\",style:({right: _vm.$store.state.rightConfigActiveName === '调度配置' ? 0 : ''})},[_c('div',{staticClass:\"right-config-form\"},[_c('p',{staticClass:\"configTitle\"},[_vm._v(\"调度配置\")]),_c('p',{staticClass:\"configWay\"},[_vm._v(\"调度方式\")]),_c('el-radio-group',{on:{\"change\":_vm.dispatchWayChange},model:{value:(_vm.dispatchWay),callback:function ($$v) {_vm.dispatchWay=$$v},expression:\"dispatchWay\"}},[_c('el-radio',{attrs:{\"label\":\"SINGLE\"}},[_vm._v(\"单次调度\")]),_c('el-radio',{attrs:{\"label\":\"ROUND\"}},[_vm._v(\"周期调度\")]),_c('el-radio',{attrs:{\"label\":\"CRON\"}},[_vm._v(\"cron调度\")])],1),_c('p',{staticClass:\"configWay\",staticStyle:{\"margin\":\"14px 0 10px 0\"}},[_vm._v(\"调度属性\")]),_c('el-form',{ref:\"ruleForm\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.form,\"rules\":_vm.rules,\"label-position\":\"top\",\"label-width\":\"100px\"}},[(_vm.dispatchWay === 'SINGLE')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"执行时间\",\"prop\":\"executeTime\"}},[_c('el-date-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"default-value\":new Date().getTime() + 360 * 1000,\"picker-options\":_vm.pickerBeginDateBefore,\"type\":\"datetime\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择开始时间\"},on:{\"change\":function($event){_vm.currentTime = new Date().getTime()}},model:{value:(_vm.form.executeTime),callback:function ($$v) {_vm.$set(_vm.form, \"executeTime\", $$v)},expression:\"form.executeTime\"}})],1)],1):_vm._e(),(_vm.dispatchWay === 'ROUND')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"生效时间\",\"prop\":\"effectStartTime\",\"rules\":[{ required: true, validator: _vm.effectStartTimeCheck, trigger: 'blur' }]}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"开始\")]),_c('el-date-picker',{staticClass:\"dateClass\",staticStyle:{\"width\":\"260px\"},attrs:{\"picker-options\":_vm.pickerBeginDateBefore1,\"type\":\"datetime\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择开始时间\"},on:{\"change\":function($event){_vm.currentTime = new Date().getTime()},\"focus\":_vm.dateFocus,\"blur\":_vm.dateBlur},model:{value:(_vm.form.effectStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"effectStartTime\", $$v)},expression:\"form.effectStartTime\"}}),_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"截止\")]),_c('el-date-picker',{staticClass:\"dateClass\",staticStyle:{\"width\":\"260px\",\"margin-top\":\"10px\"},attrs:{\"picker-options\":_vm.pickerBeginDateBefore1,\"type\":\"datetime\",\"disabled\":_vm.continuous,\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择截止时间\"},on:{\"change\":function($event){_vm.currentTime = new Date().getTime()},\"focus\":_vm.dateFocus,\"blur\":_vm.dateBlur},model:{value:(_vm.form.effectEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"effectEndTime\", $$v)},expression:\"form.effectEndTime\"}})],1),_c('el-checkbox',{on:{\"change\":_vm.continuousChange},model:{value:(_vm.continuous),callback:function ($$v) {_vm.continuous=$$v},expression:\"continuous\"}},[_vm._v(\"持续生效\")]),_c('el-form-item',{attrs:{\"label\":\"调度周期\",\"prop\":\"dispatchCycle\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":\"请选择调度周期\"},on:{\"change\":_vm.dispatchCycleChange},model:{value:(_vm.form.dispatchCycle),callback:function ($$v) {_vm.$set(_vm.form, \"dispatchCycle\", $$v)},expression:\"form.dispatchCycle\"}},_vm._l((_vm.cycleOption),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1),(_vm.form.dispatchCycle === 'minute')?_c('div',[_c('el-form-item',{staticClass:\"itemAline\",attrs:{\"label\":\"范围\",\"prop\":\"scopeStartTime\",\"rules\":[{ required: true, validator: _vm.scopeStartTimeCheck, trigger: 'blur' }]}},[_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"起始时间\"},model:{value:(_vm.form.scopeStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"scopeStartTime\", $$v)},expression:\"form.scopeStartTime\"}},_vm._l((_vm.hoursOption),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1),_vm._v(\" ~ \"),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.scopeEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"scopeEndTime\", $$v)},expression:\"form.scopeEndTime\"}},_vm._l((_vm.hoursOption),function(item){return _c('el-option',{key:item.value,attrs:{\"disabled\":parseInt(item.value) < parseInt(_vm.form.scopeStartTime),\"label\":item.label,\"value\":item.value}})}),1)],1),_c('el-form-item',{attrs:{\"label\":\"间隔时间\",\"prop\":\"intervalStartTime\",\"rules\":[{ required: true, validator: _vm.intervalStartTimeCheck, trigger: 'change' }]}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"从\")]),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"起始时间\"},model:{value:(_vm.form.intervalStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalStartTime\", $$v)},expression:\"form.intervalStartTime\"}},_vm._l((_vm.minOption),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1),_vm._v(\" ~ \"),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.intervalEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalEndTime\", $$v)},expression:\"form.intervalEndTime\"}},_vm._l((_vm.minOption),function(item){return _c('el-option',{key:item.value,attrs:{\"disabled\":parseInt(item.value) <= parseInt(_vm.form.intervalStartTime),\"label\":item.label,\"value\":item.value}})}),1),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"间隔\")]),_c('el-select',{staticStyle:{\"width\":\"260px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.spacer),callback:function ($$v) {_vm.$set(_vm.form, \"spacer\", $$v)},expression:\"form.spacer\"}},_vm._l((_vm.spacerOptionMin),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"具体时间(单位秒)\",\"prop\":\"specificTime\"}},[_c('el-time-picker',{staticClass:\"ss\",staticStyle:{\"width\":\"100%\"},attrs:{\"value-format\":\"ss\",\"format\":\"ss\",\"placeholder\":\"请选择具体时间\"},model:{value:(_vm.form.specificTime),callback:function ($$v) {_vm.$set(_vm.form, \"specificTime\", $$v)},expression:\"form.specificTime\"}})],1)],1):_vm._e(),(_vm.form.dispatchCycle === 'hour')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"间隔时间\",\"prop\":\"intervalStartTime\",\"rules\":[{ required: true, validator: _vm.intervalStartTimeCheck, trigger: 'change' }]}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"从\")]),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"起始时间\"},model:{value:(_vm.form.intervalStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalStartTime\", $$v)},expression:\"form.intervalStartTime\"}},_vm._l((_vm.hoursOption),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1),_vm._v(\" ~ \"),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.intervalEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalEndTime\", $$v)},expression:\"form.intervalEndTime\"}},_vm._l((_vm.hoursOption),function(item){return _c('el-option',{key:item.value,attrs:{\"disabled\":parseInt(item.value) <= parseInt(_vm.form.intervalStartTime),\"label\":item.label,\"value\":item.value}})}),1),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"间隔\")]),_c('el-select',{staticStyle:{\"width\":\"260px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.spacer),callback:function ($$v) {_vm.$set(_vm.form, \"spacer\", $$v)},expression:\"form.spacer\"}},_vm._l((_vm.spacerOptionHour),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"具体时间(单位分秒)\",\"prop\":\"specificTime\"}},[_c('el-time-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"value-format\":\"mm:ss\",\"format\":\"mm:ss\",\"placeholder\":\"请选择具体时间\"},model:{value:(_vm.form.specificTime),callback:function ($$v) {_vm.$set(_vm.form, \"specificTime\", $$v)},expression:\"form.specificTime\"}})],1)],1):_vm._e(),(_vm.form.dispatchCycle === 'day')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"间隔时间\",\"prop\":\"intervalStartTime\",\"rules\":[{ required: true, validator: _vm.intervalStartTimeCheck, trigger: 'change' }]}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"从\")]),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"起始时间\"},model:{value:(_vm.form.intervalStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalStartTime\", $$v)},expression:\"form.intervalStartTime\"}},_vm._l((_vm.spacerOptionDay),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1),_vm._v(\" ~ \"),_c('el-select',{staticStyle:{\"width\":\"121px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.intervalEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"intervalEndTime\", $$v)},expression:\"form.intervalEndTime\"}},_vm._l((_vm.spacerOptionDay),function(item){return _c('el-option',{key:item.value,attrs:{\"disabled\":parseInt(item.value) <= parseInt(_vm.form.intervalStartTime),\"label\":item.label,\"value\":item.value}})}),1),_c('div',{staticStyle:{\"margin-top\":\"15px\"}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"间隔\")]),_c('el-select',{staticStyle:{\"width\":\"260px\"},attrs:{\"placeholder\":\"结束时间\"},model:{value:(_vm.form.spacer),callback:function ($$v) {_vm.$set(_vm.form, \"spacer\", $$v)},expression:\"form.spacer\"}},_vm._l((_vm.intervalOptionDay),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1)],1),_c('el-form-item',{attrs:{\"label\":\"具体时间(单位时分秒)\",\"prop\":\"specificTime\"}},[_c('el-time-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"value-format\":\"HH:mm:ss\",\"format\":\"HH:mm:ss\",\"placeholder\":\"请选择具体时间\"},model:{value:(_vm.form.specificTime),callback:function ($$v) {_vm.$set(_vm.form, \"specificTime\", $$v)},expression:\"form.specificTime\"}})],1)],1):_vm._e(),(_vm.form.dispatchCycle === 'week')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"选择周期\",\"prop\":\"weekTime\"}},[_c('el-dropdown',{staticStyle:{\"width\":\"100%\"},attrs:{\"trigger\":\"click\"}},[_c('span',{staticClass:\"el-dropdown-link\"},[_c('el-input',{attrs:{\"placeholder\":\"请选择周期、支持多选\",\"readonly\":\"\",\"suffix-icon\":\"el-icon-arrow-down\"},model:{value:(_vm.form.weekTime),callback:function ($$v) {_vm.$set(_vm.form, \"weekTime\", $$v)},expression:\"form.weekTime\"}})],1),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\"},slot:\"dropdown\"},[_c('div',{staticClass:\"dropdownBox\"},[_c('div',{staticStyle:{\"height\":\"150px\",\"overflow\":\"auto\"}},[_c('div',{staticClass:\"dropdownTitle\"},[_vm._v(\"每周\")]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.spacerOptionWeek),function(item){return _c('div',{key:item.value,staticClass:\"chooseStyle\",class:_vm.currentClass(item)},[_c('div',{staticClass:\"labelStyle\",on:{\"click\":function($event){return _vm.weekClick(item)}}},[_vm._v(_vm._s(item.label))])])}),0)])])])],1)],1),_c('el-form-item',{attrs:{\"label\":\"具体时间(单位时分秒)\",\"prop\":\"specificTime\"}},[_c('el-time-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"value-format\":\"HH:mm:ss\",\"format\":\"HH:mm:ss\",\"placeholder\":\"请选择具体时间\"},model:{value:(_vm.form.specificTime),callback:function ($$v) {_vm.$set(_vm.form, \"specificTime\", $$v)},expression:\"form.specificTime\"}})],1)],1):_vm._e(),(_vm.form.dispatchCycle === 'month')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"选择周期\",\"prop\":\"dayTime\"}},[_c('el-dropdown',{staticStyle:{\"width\":\"100%\"},attrs:{\"trigger\":\"click\"}},[_c('span',{staticClass:\"el-dropdown-link\"},[_c('el-input',{attrs:{\"placeholder\":\"请选择周期、支持多选\",\"readonly\":\"\",\"suffix-icon\":\"el-icon-arrow-down\"},model:{value:(_vm.form.dayTime),callback:function ($$v) {_vm.$set(_vm.form, \"dayTime\", $$v)},expression:\"form.dayTime\"}})],1),_c('el-dropdown-menu',{attrs:{\"slot\":\"dropdown\"},slot:\"dropdown\"},[_c('div',{staticClass:\"dropdownBox\"},[_c('div',{staticStyle:{\"height\":\"260px\",\"overflow\":\"auto\"}},[_c('div',{staticClass:\"dropdownTitle\"},[_vm._v(\"每月\")]),_c('div',{staticStyle:{\"display\":\"flex\",\"flex-wrap\":\"wrap\"}},_vm._l((_vm.optionDay),function(item){return _c('div',{key:item.value,staticClass:\"daychooseStyle\",class:_vm.currentClassDay(item)},[_c('div',{staticClass:\"daylabelStyle\",on:{\"click\":function($event){return _vm.dayClick(item)}}},[_vm._v(_vm._s(item.value))])])}),0)])])])],1)],1),_c('el-form-item',{attrs:{\"label\":\"具体时间(单位时分秒)\",\"prop\":\"specificTime\"}},[_c('el-time-picker',{staticStyle:{\"width\":\"100%\"},attrs:{\"value-format\":\"HH:mm:ss\",\"format\":\"HH:mm:ss\",\"placeholder\":\"请选择具体时间\"},model:{value:(_vm.form.specificTime),callback:function ($$v) {_vm.$set(_vm.form, \"specificTime\", $$v)},expression:\"form.specificTime\"}})],1)],1):_vm._e()],1):_vm._e(),(_vm.dispatchWay === 'CRON')?_c('div',[_c('el-form-item',{attrs:{\"label\":\"生效时间\",\"prop\":\"effectStartTime\",\"rules\":[{ required: true, validator: _vm.effectStartTimeCheck, trigger: 'blur' }]}},[_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"开始\")]),_c('el-date-picker',{staticClass:\"dateClass\",staticStyle:{\"width\":\"260px\"},attrs:{\"picker-options\":_vm.pickerBeginDateBefore1,\"type\":\"datetime\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择开始时间\"},on:{\"change\":function($event){_vm.currentTime = new Date().getTime()}},model:{value:(_vm.form.effectStartTime),callback:function ($$v) {_vm.$set(_vm.form, \"effectStartTime\", $$v)},expression:\"form.effectStartTime\"}}),_c('span',{staticStyle:{\"display\":\"inline-block\",\"width\":\"43px\"}},[_vm._v(\"截止\")]),_c('el-date-picker',{staticClass:\"dateClass\",staticStyle:{\"width\":\"260px\",\"margin-top\":\"10px\"},attrs:{\"picker-options\":_vm.pickerBeginDateBefore1,\"type\":\"datetime\",\"disabled\":_vm.continuous,\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"placeholder\":\"选择结束时间\"},on:{\"change\":function($event){_vm.currentTime = new Date().getTime()}},model:{value:(_vm.form.effectEndTime),callback:function ($$v) {_vm.$set(_vm.form, \"effectEndTime\", $$v)},expression:\"form.effectEndTime\"}})],1),_c('el-checkbox',{on:{\"change\":_vm.continuousChange},model:{value:(_vm.continuous),callback:function ($$v) {_vm.continuous=$$v},expression:\"continuous\"}},[_vm._v(\"持续生效\")]),_c('el-form-item',{attrs:{\"label\":\"cron表达式\",\"rules\":[{ required: true, validator: _vm.cronCheck, trigger: 'blur' }],\"prop\":\"cron\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入cron表达式\"},model:{value:(_vm.form.cron),callback:function ($$v) {_vm.$set(_vm.form, \"cron\", $$v)},expression:\"form.cron\"}})],1),_c('el-form-item',{attrs:{\"label\":\"测试\"}},[_c('div',{staticStyle:{\"display\":\"flex\",\"justify-content\":\"space-between\"}},[_c('el-input-number',{attrs:{\"controls-position\":\"right\",\"min\":1,\"precision\":0,\"max\":200},model:{value:(_vm.testNum),callback:function ($$v) {_vm.testNum=$$v},expression:\"testNum\"}}),_c('el-button',{attrs:{\"type\":\"primary\",\"plain\":\"\"},on:{\"click\":_vm.executeCron}},[_vm._v(\"执行\")])],1)]),_c('el-form-item',{attrs:{\"label\":\"最近运行时间\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"height\":\"240\",\"data\":_vm.elapsedTableData,\"header-cell-style\":{'background-color': '#F5F7FA','color': '#909399'}}},[_c('el-table-column',{attrs:{\"label\":\"序号\",\"type\":\"index\",\"width\":\"100\"}}),_c('el-table-column',{attrs:{\"prop\":\"executeTime\",\"label\":\"执行时间\"}})],1)],1)],1):_vm._e(),_c('el-form-item',{staticStyle:{\"text-align\":\"center\",\"padding-top\":\"5px\"}},[_c('el-button',{attrs:{\"plain\":\"\"},on:{\"click\":function($event){return _vm.empty('ruleForm')}}},[_vm._v(\"清空\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.submitForm('ruleForm')}}},[_vm._v(\"保存\")])],1)],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "export const hoursOption = [\r\n    {value: '0', label: '0时'},\r\n    {value: '1', label: '1时'},\r\n    {value: '2', label: '2时'},\r\n    {value: '3', label: '3时'},\r\n    {value: '4', label: '4时'},\r\n    {value: '5', label: '5时'},\r\n    {value: '6', label: '6时'},\r\n    {value: '7', label: '7时'},\r\n    {value: '8', label: '8时'},\r\n    {value: '9', label: '9时'},\r\n    {value: '10', label: '10时'},\r\n    {value: '11', label: '11时'},\r\n    {value: '12', label: '12时'},\r\n    {value: '13', label: '13时'},\r\n    {value: '14', label: '14时'},\r\n    {value: '15', label: '15时'},\r\n    {value: '16', label: '16时'},\r\n    {value: '17', label: '17时'},\r\n    {value: '18', label: '18时'},\r\n    {value: '19', label: '19时'},\r\n    {value: '20', label: '20时'},\r\n    {value: '21', label: '21时'},\r\n    {value: '22', label: '22时'},\r\n    {value: '23', label: '23时'}\r\n]\r\nexport const cycleOption = [\r\n    {value: 'minute', label: '分钟'},\r\n    {value: 'hour', label: '小时'},\r\n    {value: 'day', label: '天'},\r\n    {value: 'week', label: '周'},\r\n    {value: 'month', label: '月'}\r\n]\r\nexport const minOption = [\r\n    {value: '0', label: '0分'},\r\n    {value: '1', label: '1分'},\r\n    {value: '2', label: '2分'},\r\n    {value: '3', label: '3分'},\r\n    {value: '4', label: '4分'},\r\n    {value: '5', label: '5分'},\r\n    {value: '6', label: '6分'},\r\n    {value: '7', label: '7分'},\r\n    {value: '8', label: '8分'},\r\n    {value: '9', label: '9分'},\r\n    {value: '10', label: '10分'},\r\n    {value: '11', label: '11分'},\r\n    {value: '12', label: '12分'},\r\n    {value: '13', label: '13分'},\r\n    {value: '14', label: '14分'},\r\n    {value: '15', label: '15分'},\r\n    {value: '16', label: '16分'},\r\n    {value: '17', label: '17分'},\r\n    {value: '18', label: '18分'},\r\n    {value: '19', label: '19分'},\r\n    {value: '20', label: '20分'},\r\n    {value: '21', label: '21分'},\r\n    {value: '22', label: '22分'},\r\n    {value: '23', label: '23分'},\r\n    {value: '24', label: '24分'},\r\n    {value: '25', label: '25分'},\r\n    {value: '26', label: '26分'},\r\n    {value: '27', label: '27分'},\r\n    {value: '28', label: '28分'},\r\n    {value: '29', label: '29分'},\r\n    {value: '30', label: '30分'},\r\n    {value: '31', label: '31分'},\r\n    {value: '32', label: '32分'},\r\n    {value: '33', label: '33分'},\r\n    {value: '34', label: '34分'},\r\n    {value: '35', label: '35分'},\r\n    {value: '36', label: '36分'},\r\n    {value: '37', label: '37分'},\r\n    {value: '38', label: '38分'},\r\n    {value: '39', label: '39分'},\r\n    {value: '40', label: '40分'},\r\n    {value: '41', label: '41分'},\r\n    {value: '42', label: '42分'},\r\n    {value: '43', label: '43分'},\r\n    {value: '44', label: '44分'},\r\n    {value: '45', label: '45分'},\r\n    {value: '46', label: '46分'},\r\n    {value: '47', label: '47分'},\r\n    {value: '48', label: '48分'},\r\n    {value: '49', label: '49分'},\r\n    {value: '50', label: '50分'},\r\n    {value: '51', label: '51分'},\r\n    {value: '52', label: '52分'},\r\n    {value: '53', label: '53分'},\r\n    {value: '54', label: '54分'},\r\n    {value: '55', label: '55分'},\r\n    {value: '56', label: '56分'},\r\n    {value: '57', label: '57分'},\r\n    {value: '58', label: '58分'},\r\n    {value: '59', label: '59分'}\r\n]\r\nexport const spacerOptionMin = [\r\n    {value: '5', label: '5分钟'},\r\n    {value: '10', label: '10分钟'},\r\n    {value: '15', label: '15分钟'},\r\n    {value: '20', label: '20分钟'},\r\n    {value: '25', label: '25分钟'},\r\n    {value: '30', label: '30分钟'}\r\n]\r\nexport const spacerOptionHour = [\r\n    {value: '1', label: '1小时'},\r\n    {value: '2', label: '2小时'},\r\n    {value: '3', label: '3小时'},\r\n    {value: '4', label: '4小时'},\r\n    {value: '5', label: '5小时'},\r\n    {value: '6', label: '6小时'},\r\n    {value: '7', label: '7小时'},\r\n    {value: '8', label: '8小时'},\r\n    {value: '9', label: '9小时'},\r\n    {value: '10', label: '10小时'},\r\n    {value: '11', label: '11小时'},\r\n    {value: '12', label: '12小时'},\r\n    {value: '13', label: '13小时'},\r\n    {value: '14', label: '14小时'},\r\n    {value: '15', label: '15小时'},\r\n    {value: '16', label: '16小时'},\r\n    {value: '17', label: '17小时'},\r\n    {value: '18', label: '18小时'},\r\n    {value: '19', label: '19小时'},\r\n    {value: '20', label: '20小时'},\r\n    {value: '21', label: '21小时'},\r\n    {value: '22', label: '22小时'},\r\n    {value: '23', label: '23小时'},\r\n    // {value: '24', label: '24小时'}\r\n]\r\nexport const spacerOptionDay = [\r\n    {value: '1', label: '1日'},\r\n    {value: '2', label: '2日'},\r\n    {value: '3', label: '3日'},\r\n    {value: '4', label: '4日'},\r\n    {value: '5', label: '5日'},\r\n    {value: '6', label: '6日'},\r\n    {value: '7', label: '7日'},\r\n    {value: '8', label: '8日'},\r\n    {value: '9', label: '9日'},\r\n    {value: '10', label: '10日'},\r\n    {value: '11', label: '11日'},\r\n    {value: '12', label: '12日'},\r\n    {value: '13', label: '13日'},\r\n    {value: '14', label: '14日'},\r\n    {value: '15', label: '15日'},\r\n    {value: '16', label: '16日'},\r\n    {value: '17', label: '17日'},\r\n    {value: '18', label: '18日'},\r\n    {value: '19', label: '19日'},\r\n    {value: '20', label: '20日'},\r\n    {value: '21', label: '21日'},\r\n    {value: '22', label: '22日'},\r\n    {value: '23', label: '23日'},\r\n    {value: '24', label: '24日'},\r\n    {value: '25', label: '25日'},\r\n    {value: '26', label: '26日'},\r\n    {value: '27', label: '27日'},\r\n    {value: '28', label: '28日'},\r\n    {value: '29', label: '29日'},\r\n    {value: '30', label: '30日'},\r\n    {value: '31', label: '31日'},\r\n]\r\n\r\nexport const intervalOptionDay = [\r\n    {value: '1', label: '1天'},\r\n    {value: '2', label: '2天'},\r\n    {value: '3', label: '3天'},\r\n    {value: '4', label: '4天'},\r\n    {value: '5', label: '5天'},\r\n    {value: '6', label: '6天'},\r\n    {value: '7', label: '7天'},\r\n    {value: '8', label: '8天'},\r\n    {value: '9', label: '9天'},\r\n    {value: '10', label: '10天'},\r\n    {value: '11', label: '11天'},\r\n    {value: '12', label: '12天'},\r\n    {value: '13', label: '13天'},\r\n    {value: '14', label: '14天'},\r\n    {value: '15', label: '15天'},\r\n    {value: '16', label: '16天'},\r\n    {value: '17', label: '17天'},\r\n    {value: '18', label: '18天'},\r\n    {value: '19', label: '19天'},\r\n    {value: '20', label: '20天'},\r\n    {value: '21', label: '21天'},\r\n    {value: '22', label: '22天'},\r\n    {value: '23', label: '23天'},\r\n    {value: '24', label: '24天'},\r\n    {value: '25', label: '25天'},\r\n    {value: '26', label: '26天'},\r\n    {value: '27', label: '27天'},\r\n    {value: '28', label: '28天'},\r\n    {value: '29', label: '29天'},\r\n    {value: '30', label: '30天'},\r\n]\r\nexport const secOption = [\r\n    {value: '0', label: '0秒'},\r\n    {value: '1', label: '1秒'},\r\n    {value: '2', label: '2秒'},\r\n    {value: '3', label: '3秒'},\r\n    {value: '4', label: '4秒'},\r\n    {value: '5', label: '5秒'},\r\n    {value: '6', label: '6秒'},\r\n    {value: '7', label: '7秒'},\r\n    {value: '8', label: '8秒'},\r\n    {value: '9', label: '9秒'},\r\n    {value: '10', label: '10秒'},\r\n    {value: '11', label: '11秒'},\r\n    {value: '12', label: '12秒'},\r\n    {value: '13', label: '13秒'},\r\n    {value: '14', label: '14秒'},\r\n    {value: '15', label: '15秒'},\r\n    {value: '16', label: '16秒'},\r\n    {value: '17', label: '17秒'},\r\n    {value: '18', label: '18秒'},\r\n    {value: '19', label: '19秒'},\r\n    {value: '20', label: '20秒'},\r\n    {value: '21', label: '21秒'},\r\n    {value: '22', label: '22秒'},\r\n    {value: '23', label: '23秒'},\r\n    {value: '24', label: '24秒'},\r\n    {value: '25', label: '25秒'},\r\n    {value: '26', label: '26秒'},\r\n    {value: '27', label: '27秒'},\r\n    {value: '28', label: '28秒'},\r\n    {value: '29', label: '29秒'},\r\n    {value: '30', label: '30秒'},\r\n    {value: '31', label: '31秒'},\r\n    {value: '32', label: '32秒'},\r\n    {value: '33', label: '33秒'},\r\n    {value: '34', label: '34秒'},\r\n    {value: '35', label: '35秒'},\r\n    {value: '36', label: '36秒'},\r\n    {value: '37', label: '37秒'},\r\n    {value: '38', label: '38秒'},\r\n    {value: '39', label: '39秒'},\r\n    {value: '40', label: '40秒'},\r\n    {value: '41', label: '41秒'},\r\n    {value: '42', label: '42秒'},\r\n    {value: '43', label: '43秒'},\r\n    {value: '44', label: '44秒'},\r\n    {value: '45', label: '45秒'},\r\n    {value: '46', label: '46秒'},\r\n    {value: '47', label: '47秒'},\r\n    {value: '48', label: '48秒'},\r\n    {value: '49', label: '49秒'},\r\n    {value: '50', label: '50秒'},\r\n    {value: '51', label: '51秒'},\r\n    {value: '52', label: '52秒'},\r\n    {value: '53', label: '53秒'},\r\n    {value: '54', label: '54秒'},\r\n    {value: '55', label: '55秒'},\r\n    {value: '56', label: '56秒'},\r\n    {value: '57', label: '57秒'},\r\n    {value: '58', label: '58秒'},\r\n    {value: '59', label: '59秒'}\r\n]\r\nexport const spacerOptionWeek = [\r\n    {value: 1, label: '星期一'},\r\n    {value: 2, label: '星期二'},\r\n    {value: 3, label: '星期三'},\r\n    {value: 4, label: '星期四'},\r\n    {value: 5, label: '星期五'},\r\n    {value: 6, label: '星期六'},\r\n    {value: 7, label: '星期日'},\r\n]\r\nexport const optionDay = [\r\n    {value: 1, label: '1号'},\r\n    {value: 2, label: '2号'},\r\n    {value: 3, label: '3号'},\r\n    {value: 4, label: '4号'},\r\n    {value: 5, label: '5号'},\r\n    {value: 6, label: '6号'},\r\n    {value: 7, label: '7号'},\r\n    {value: 8, label: '8号'},\r\n    {value: 9, label: '9号'},\r\n    {value: 10, label: '10号'},\r\n    {value: 11, label: '11号'},\r\n    {value: 12, label: '12号'},\r\n    {value: 13, label: '13号'},\r\n    {value: 14, label: '14号'},\r\n    {value: 15, label: '15号'},\r\n    {value: 16, label: '16号'},\r\n    {value: 17, label: '17号'},\r\n    {value: 18, label: '18号'},\r\n    {value: 19, label: '19号'},\r\n    {value: 20, label: '20号'},\r\n    {value: 21, label: '21号'},\r\n    {value: 22, label: '22号'},\r\n    {value: 23, label: '23号'},\r\n    {value: 24, label: '24号'},\r\n    {value: 25, label: '25号'},\r\n    {value: 26, label: '26号'},\r\n    {value: 27, label: '27号'},\r\n    {value: 28, label: '28号'},\r\n    {value: 29, label: '29号'},\r\n    {value: 30, label: '30号'},\r\n    {value: 31, label: '31号'},\r\n]", "var timeValidator = (rule, value, callback) => {\r\n  // debugger\r\n    if (!value) {\r\n        return callback(new Error('请选择执行时间'))\r\n    }\r\n    if (new Date(value).getTime() - 300 * 1000 < Date.now()) {\r\n        callback(new Error(\"请选择正确的执行时间(调度任务五分钟后才可执行)\"));\r\n    } else {\r\n        callback();\r\n    }\r\n}\r\nvar specificTimeCheck = (rule, value, callback) => {\r\n    if (!value) {\r\n      return callback(new Error('请选择具体时间'))\r\n    } else {\r\n      callback()\r\n    }\r\n  }\r\nexport default {\r\n  // 通用\r\n  executeTime: [\r\n    { required: true, validator: timeValidator, trigger: 'blur' }\r\n  ],\r\n  specificTime: [\r\n    { required: true, validator: specificTimeCheck, trigger: 'blur' }\r\n  ],\r\n  weekTime: [\r\n    { required: true, message: '请选择时间', trigger: 'change' }\r\n  ],\r\n  dispatchCycle: [\r\n    { required: true, message: '请选择调度周期', trigger: 'change' }\r\n  ]\r\n//   codeName: [\r\n//     { required: true, validator: codeName<PERSON>heck, trigger: 'blur' },\r\n//     { min: 2, max: 60, message: '长度在2~60之间', trigger: 'blur' }\r\n//   ]\r\n}\r\n  ", "<!-- 调度配置 -->\r\n<template>\r\n  <section class=\"right-config-panel\" :style=\"{right: $store.state.rightConfigActiveName === '调度配置' ? 0 : ''}\">\r\n    <div class=\"right-config-form\">\r\n      <p class=\"configTitle\">调度配置</p>\r\n      <p class=\"configWay\">调度方式</p>\r\n      <el-radio-group v-model=\"dispatchWay\" @change=\"dispatchWayChange\">\r\n        <el-radio label=\"SINGLE\">单次调度</el-radio>\r\n        <el-radio label=\"ROUND\">周期调度</el-radio>\r\n        <el-radio label=\"CRON\">cron调度</el-radio>\r\n        <!-- <el-radio label=\"DEPENDENT\">依赖调度</el-radio> -->\r\n      </el-radio-group>\r\n      <p class=\"configWay\" style=\"margin: 14px 0 10px 0\">调度属性</p>\r\n      <el-form :model=\"form\" :rules=\"rules\" ref=\"ruleForm\" label-position=\"top\" label-width=\"100px\" class=\"demo-ruleForm\">\r\n        <div v-if=\"dispatchWay === 'SINGLE'\">\r\n          <el-form-item label=\"执行时间\" prop=\"executeTime\">\r\n            <el-date-picker style=\"width:100%\" :default-value=\"new Date().getTime() + 360 * 1000\" @change=\"currentTime = new Date().getTime()\" v-model=\"form.executeTime\" :picker-options=\"pickerBeginDateBefore\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"选择开始时间\"></el-date-picker>\r\n          </el-form-item>\r\n        </div>\r\n        <div v-if=\"dispatchWay === 'ROUND'\">\r\n          <el-form-item label=\"生效时间\" prop=\"effectStartTime\" :rules=\"[{ required: true, validator: effectStartTimeCheck, trigger: 'blur' }]\">\r\n            <span style=\"display: inline-block; width:43px\">开始</span>\r\n            <el-date-picker class=\"dateClass\" style=\"width:260px\" @change=\"currentTime = new Date().getTime()\" v-model=\"form.effectStartTime\" @focus=\"dateFocus\" @blur=\"dateBlur\" :picker-options=\"pickerBeginDateBefore1\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"选择开始时间\"></el-date-picker>\r\n            <span style=\"display: inline-block; width:43px\">截止</span>\r\n            <el-date-picker class=\"dateClass\" style=\"width:260px;margin-top: 10px\" @change=\"currentTime = new Date().getTime()\" v-model=\"form.effectEndTime\" @focus=\"dateFocus\" @blur=\"dateBlur\" :picker-options=\"pickerBeginDateBefore1\" type=\"datetime\" :disabled=\"continuous\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"选择截止时间\"></el-date-picker>\r\n          </el-form-item>\r\n          <el-checkbox v-model=\"continuous\" @change=\"continuousChange\">持续生效</el-checkbox>\r\n          <el-form-item label=\"调度周期\" prop=\"dispatchCycle\">\r\n            <el-select v-model=\"form.dispatchCycle\" style=\"width:100%\" @change=\"dispatchCycleChange\" placeholder=\"请选择调度周期\">\r\n              <el-option\r\n                v-for=\"item in cycleOption\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\">\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <div v-if=\"form.dispatchCycle === 'minute'\">\r\n            <el-form-item label=\"范围\" prop=\"scopeStartTime\" class=\"itemAline\" :rules=\"[{ required: true, validator: scopeStartTimeCheck, trigger: 'blur' }]\">\r\n              <!-- <span style=\"display: inline-block; width:35px\"></span> -->\r\n              <el-select v-model=\"form.scopeStartTime\" style=\"width:121px\" placeholder=\"起始时间\">\r\n                <el-option\r\n                  v-for=\"item in hoursOption\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              ~\r\n              <el-select v-model=\"form.scopeEndTime\" style=\"width:121px\" placeholder=\"结束时间\">\r\n                <el-option\r\n                  v-for=\"item in hoursOption\"\r\n                  :disabled=\"parseInt(item.value) < parseInt(form.scopeStartTime)\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"间隔时间\" prop=\"intervalStartTime\" :rules=\"[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]\">\r\n              <span style=\"display: inline-block; width:43px\">从</span>\r\n              <el-select v-model=\"form.intervalStartTime\" style=\"width:121px\" placeholder=\"起始时间\">\r\n                <el-option\r\n                  v-for=\"item in minOption\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              ~\r\n              <el-select v-model=\"form.intervalEndTime\" style=\"width:121px\" placeholder=\"结束时间\">\r\n                <el-option\r\n                  v-for=\"item in minOption\"\r\n                  :disabled=\"parseInt(item.value) <= parseInt(form.intervalStartTime)\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              <div style=\"margin-top: 15px\">\r\n                <span style=\"display: inline-block; width:43px\">间隔</span>\r\n                <el-select v-model=\"form.spacer\" style=\"width:260px\" placeholder=\"结束时间\">\r\n                  <el-option\r\n                    v-for=\"item in spacerOptionMin\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"具体时间(单位秒)\" prop=\"specificTime\">\r\n              <el-time-picker\r\n                v-model=\"form.specificTime\"\r\n                style=\"width:100%\"\r\n                class=\"ss\"\r\n                value-format=\"ss\"\r\n                format=\"ss\"\r\n                placeholder=\"请选择具体时间\">\r\n              </el-time-picker>\r\n            </el-form-item>\r\n          </div>\r\n          <div v-if=\"form.dispatchCycle === 'hour'\">\r\n            <el-form-item label=\"间隔时间\" prop=\"intervalStartTime\" :rules=\"[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]\">\r\n              <span style=\"display: inline-block; width:43px\">从</span>\r\n              <el-select v-model=\"form.intervalStartTime\" style=\"width:121px\" placeholder=\"起始时间\">\r\n                <el-option\r\n                  v-for=\"item in hoursOption\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              ~\r\n              <el-select v-model=\"form.intervalEndTime\" style=\"width:121px\" placeholder=\"结束时间\">\r\n                <el-option\r\n                  v-for=\"item in hoursOption\"\r\n                  :disabled=\"parseInt(item.value) <= parseInt(form.intervalStartTime)\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              <div style=\"margin-top: 15px\">\r\n                <span style=\"display: inline-block; width:43px\">间隔</span>\r\n                <el-select v-model=\"form.spacer\" style=\"width:260px\" placeholder=\"结束时间\">\r\n                  <el-option\r\n                    v-for=\"item in spacerOptionHour\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"具体时间(单位分秒)\" prop=\"specificTime\">\r\n              <el-time-picker\r\n                v-model=\"form.specificTime\"\r\n                style=\"width:100%\"\r\n                value-format=\"mm:ss\"\r\n                format=\"mm:ss\"\r\n                placeholder=\"请选择具体时间\">\r\n              </el-time-picker>\r\n            </el-form-item>\r\n          </div>\r\n          <div v-if=\"form.dispatchCycle === 'day'\">\r\n            <el-form-item label=\"间隔时间\" prop=\"intervalStartTime\" :rules=\"[{ required: true, validator: intervalStartTimeCheck, trigger: 'change' }]\">\r\n              <span style=\"display: inline-block; width:43px\">从</span>\r\n              <el-select v-model=\"form.intervalStartTime\" style=\"width:121px\" placeholder=\"起始时间\">\r\n                <el-option\r\n                  v-for=\"item in spacerOptionDay\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              ~\r\n              <el-select v-model=\"form.intervalEndTime\" style=\"width:121px\" placeholder=\"结束时间\">\r\n                <el-option\r\n                  v-for=\"item in spacerOptionDay\"\r\n                  :disabled=\"parseInt(item.value) <= parseInt(form.intervalStartTime)\"\r\n                  :key=\"item.value\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\">\r\n                </el-option>\r\n              </el-select>\r\n              <div style=\"margin-top: 15px\">\r\n                <span style=\"display: inline-block; width:43px\">间隔</span>\r\n                <el-select v-model=\"form.spacer\" style=\"width:260px\" placeholder=\"结束时间\">\r\n                  <el-option\r\n                    v-for=\"item in intervalOptionDay\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\">\r\n                  </el-option>\r\n                </el-select>\r\n              </div>\r\n            </el-form-item>\r\n            <!-- 这里开始 -->\r\n            <el-form-item label=\"具体时间(单位时分秒)\" prop=\"specificTime\">\r\n              <el-time-picker\r\n                style=\"width:100%\"\r\n                value-format=\"HH:mm:ss\"\r\n                format=\"HH:mm:ss\"\r\n                v-model=\"form.specificTime\"\r\n                placeholder=\"请选择具体时间\">\r\n              </el-time-picker>\r\n            </el-form-item>\r\n          </div>\r\n          <div v-if=\"form.dispatchCycle === 'week'\">\r\n            <el-form-item label=\"选择周期\" prop=\"weekTime\">\r\n              <el-dropdown trigger=\"click\" style=\"width: 100%\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <el-input \r\n                    placeholder=\"请选择周期、支持多选\" \r\n                    v-model=\"form.weekTime\"\r\n                    readonly\r\n                    suffix-icon=\"el-icon-arrow-down\"\r\n                  />\r\n                </span>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <div class=\"dropdownBox\">\r\n                    <div style=\"height: 150px;overflow: auto;\">\r\n                      <div class=\"dropdownTitle\">每周</div>\r\n                      <div style=\"display: flex;flex-wrap: wrap;\">\r\n                        <div v-for=\"item in spacerOptionWeek\" class=\"chooseStyle\" :class=\"currentClass(item)\" :key=\"item.value\">\r\n                          <div class=\"labelStyle\" @click=\"weekClick(item)\">{{ item.label }}</div>\r\n                        </div>\r\n                      </div>\r\n                      \r\n                    </div>\r\n                  </div>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </el-form-item>\r\n            <el-form-item label=\"具体时间(单位时分秒)\" prop=\"specificTime\">\r\n              <el-time-picker\r\n                style=\"width:100%\"\r\n                value-format=\"HH:mm:ss\"\r\n                format=\"HH:mm:ss\"\r\n                v-model=\"form.specificTime\"\r\n                placeholder=\"请选择具体时间\">\r\n              </el-time-picker>\r\n            </el-form-item>\r\n          </div>\r\n          <div v-if=\"form.dispatchCycle === 'month'\">\r\n            <el-form-item label=\"选择周期\" prop=\"dayTime\">\r\n              <el-dropdown trigger=\"click\" style=\"width: 100%\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <el-input \r\n                    placeholder=\"请选择周期、支持多选\" \r\n                    v-model=\"form.dayTime\"\r\n                    readonly\r\n                    suffix-icon=\"el-icon-arrow-down\"\r\n                  />\r\n                </span>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <div class=\"dropdownBox\">\r\n                    <div style=\"height: 260px;overflow: auto;\">\r\n                      <div class=\"dropdownTitle\">每月</div>\r\n                      <div style=\"display: flex;flex-wrap: wrap;\">\r\n                        <div v-for=\"item in optionDay\" class=\"daychooseStyle\" :class=\"currentClassDay(item)\" :key=\"item.value\">\r\n                          <div class=\"daylabelStyle\" @click=\"dayClick(item)\">{{ item.value }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </el-form-item>\r\n            <el-form-item label=\"具体时间(单位时分秒)\" prop=\"specificTime\">\r\n              <el-time-picker\r\n                style=\"width:100%\"\r\n                value-format=\"HH:mm:ss\"\r\n                format=\"HH:mm:ss\"\r\n                v-model=\"form.specificTime\"\r\n                placeholder=\"请选择具体时间\">\r\n              </el-time-picker>\r\n            </el-form-item>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"dispatchWay === 'CRON'\">\r\n          <el-form-item label=\"生效时间\" prop=\"effectStartTime\" :rules=\"[{ required: true, validator: effectStartTimeCheck, trigger: 'blur' }]\">\r\n            <span style=\"display: inline-block; width:43px\">开始</span>\r\n            <el-date-picker class=\"dateClass\" style=\"width:260px\" @change=\"currentTime = new Date().getTime()\" v-model=\"form.effectStartTime\" :picker-options=\"pickerBeginDateBefore1\" type=\"datetime\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"选择开始时间\"></el-date-picker>\r\n            <span style=\"display: inline-block; width:43px\">截止</span>\r\n            <el-date-picker class=\"dateClass\" style=\"width:260px;margin-top: 10px\" @change=\"currentTime = new Date().getTime()\" v-model=\"form.effectEndTime\" :picker-options=\"pickerBeginDateBefore1\" type=\"datetime\" :disabled=\"continuous\" value-format=\"yyyy-MM-dd HH:mm:ss\" placeholder=\"选择结束时间\"></el-date-picker>\r\n          </el-form-item>\r\n          <el-checkbox v-model=\"continuous\" @change=\"continuousChange\">持续生效</el-checkbox>\r\n          <el-form-item label=\"cron表达式\" :rules=\"[{ required: true, validator: cronCheck, trigger: 'blur' }]\" prop=\"cron\">\r\n            <el-input v-model=\"form.cron\" placeholder=\"请输入cron表达式\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"测试\">\r\n            <div style=\"display:flex; justify-content: space-between;\">\r\n              <el-input-number v-model=\"testNum\" controls-position=\"right\" :min=\"1\" :precision=\"0\" :max=\"200\"></el-input-number>\r\n              <el-button type=\"primary\" plain @click=\"executeCron\">执行</el-button>\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item label=\"最近运行时间\">\r\n            <el-table\r\n              height=\"240\"\r\n              :data=\"elapsedTableData\"\r\n              style=\"width: 100%\"\r\n              :header-cell-style=\"{'background-color': '#F5F7FA','color': '#909399'}\"\r\n            >\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"executeTime\"\r\n                label=\"执行时间\">\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-form-item>\r\n        </div>\r\n        <el-form-item style=\"text-align:center;padding-top: 5px;\">\r\n          <el-button plain @click=\"empty('ruleForm')\">清空</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">保存</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport { scheduleAdd, scheduleInfo, cronValid, scheduleClear, commonCron, scheduleUpdate } from '@/api/workflowConfig'\r\nimport {hoursOption, cycleOption, minOption, spacerOptionMin, spacerOptionHour, spacerOptionDay, spacerOptionWeek, optionDay, secOption, intervalOptionDay} from \"../../libs/common.js\";\r\nimport rules from '../../libs/validDate.js'\r\nimport { bus } from '@/libs/bus'\r\nexport default {\r\n  name: 'rightConfigDispatch',\r\n  data() {\r\n    return {\r\n      rules,\r\n      isAdd: false,\r\n      scheduleId: '',\r\n      lastSaveData: '',\r\n      workflowId: '',\r\n      dayOfWeeks: [],\r\n      showWeeks: [],\r\n      dayOfMonths: [],\r\n      showMonths: [],\r\n      testNum: 10,\r\n      elapsedTableData: [],\r\n      dispatchWay: 'SINGLE',\r\n      form: {\r\n        executeTime: '', // 执行时间\r\n        effectStartTime: new Date(), // 生效开始时间\r\n        effectEndTime: '', // 生效结束时间\r\n        dispatchCycle: '', // 调度周期\r\n        scopeStartTime: '0', // 范围开始时间\r\n        scopeEndTime: '23', // 范围结束时间\r\n        intervalStartTime: '0', // 间隔开始时间\r\n        intervalEndTime: '59', // 间隔结束时间\r\n        spacer: '30', // 间隔区间\r\n        specificTime: '0', // 具体时间\r\n        weekTime: '', // 周选择时间\r\n        dayTime: '', // 月选择时间\r\n        cron: ''\r\n      },\r\n      hoursKey: 1,\r\n      hoursOption,\r\n      cycleOption,\r\n      minOption,\r\n      spacerOptionMin,\r\n      spacerOptionHour,\r\n      secOption,\r\n      spacerOptionDay,\r\n      intervalOptionDay,\r\n      spacerOptionWeek,\r\n      optionDay,\r\n      continuous: true,\r\n      currentTime: new Date().getTime()\r\n    }\r\n  },\r\n  watch: {\r\n    '$store.state.rightConfigActiveName'(val) {\r\n      if (val === '调度配置') {\r\n        this.getScheduleInfo()\r\n      }\r\n      // this.currentTime = new Date().getTime()\r\n    }\r\n  },\r\n  computed:{\r\n    pickerBeginDateBefore1() {\r\n      //选择的日期\r\n      let curDate\r\n      if (this.form.effectStartTime) {\r\n        curDate = this.$moment(this.form.effectStartTime).format(\"YYYY-MM-DD\");\r\n      } else {\r\n        curDate = this.$moment(this.currentTime).format(\"YYYY-MM-DD\");\r\n      }\r\n      //最小日期\r\n      let minDate = this.$moment(Date.now()).format(\"YYYY-MM-DD\");\r\n      // 如果选择的日期为最小日期，则设置为最小日期的时分秒\r\n      let str = \"\";\r\n      if(curDate == minDate){\r\n        str = this.$moment(Date.now()).format(\"HH:mm:ss\");\r\n      }else{\r\n        str = \"00:00:00\"\r\n      }\r\n      return {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < Date.now() - 8.64e7;\r\n        },\r\n        selectableRange: str + \" - 23:59:59\"\r\n      }\r\n    },\r\n    pickerBeginDateBefore(){\r\n      //选择的日期\r\n      let curDate\r\n      if (this.form.executeTime) {\r\n        curDate = this.$moment(this.form.executeTime).format(\"YYYY-MM-DD\");\r\n      } else {\r\n        curDate = this.$moment(this.currentTime).format(\"YYYY-MM-DD\");\r\n      }\r\n      //最小日期\r\n      let minDate = this.$moment(Date.now()).format(\"YYYY-MM-DD\");\r\n      // 如果选择的日期为最小日期，则设置为最小日期的时分秒\r\n      let str = \"\";\r\n      if(curDate == minDate){\r\n        str = this.$moment(Date.now() + 360 * 1000).format(\"HH:mm:ss\");\r\n      }else{\r\n        str = \"00:00:00\"\r\n      }\r\n      return {\r\n        disabledDate: (time) => {\r\n          return time.getTime() < Date.now() - 8.64e7;\r\n        },\r\n        selectableRange: str + \" - 23:59:59\"\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    // this.workflowId = this.$route.query.workflowId\r\n    this.workflowId = window.GetQueryValue('workflowId')\r\n    // this.category = window.GetQueryValue('category')\r\n    this.getScheduleInfo()\r\n  },\r\n  mounted() {\r\n    // this.getScheduleInfo()\r\n  },\r\n  methods: {\r\n    getScheduleInfo() {\r\n      scheduleInfo({workflowId: this.workflowId}).then((res) => {\r\n        if (res.code === 200) {\r\n          if (res.result[0]) {\r\n            this.scheduleId = res.result[0].scheduleId\r\n            bus.$emit('scheduleId', this.scheduleId)\r\n            this.isAdd = false\r\n            this.lastSaveData = res.result[0]\r\n            this.dispatchWay = res.result[0].strategyType\r\n            if (this.dispatchWay === 'ROUND') {\r\n              this.form.dispatchCycle = res.result[0].period.toLowerCase()\r\n              // this.continuous = res.result.continuedFlag === 'CONTINUED' ? true : false\r\n              this.continuous = res.result[0].endTime ? false : true\r\n              this.form.effectStartTime = res.result[0].startTime\r\n              this.form.effectEndTime = res.result[0].endTime ? res.result[0].endTime : ''\r\n              switch(this.form.dispatchCycle) {\r\n                case 'minute':\r\n                  this.form.scopeStartTime = res.result[0].hourRange.split(',')[0]\r\n                  this.form.scopeEndTime = res.result[0].hourRange.split(',')[1]\r\n                  this.form.intervalStartTime = res.result[0].minuteRange.split(',')[0]\r\n                  this.form.intervalEndTime = res.result[0].minuteRange.split(',')[1]\r\n                  this.form.spacer = res.result[0].timeInterval + ''\r\n                  this.form.specificTime = res.result[0].second + ''\r\n                break\r\n                case 'hour':\r\n                  this.form.intervalStartTime = res.result[0].minuteRange.split(',')[0]\r\n                  this.form.intervalEndTime = res.result[0].minuteRange.split(',')[1]\r\n                  this.form.spacer = res.result[0].timeInterval + ''\r\n                  this.form.specificTime = res.result[0].minute + ':' + res.result[0].second\r\n                break\r\n                case 'day':\r\n                  this.form.intervalStartTime = res.result[0].dayRange.split(',')[0]\r\n                  this.form.intervalEndTime = res.result[0].dayRange.split(',')[1]\r\n                  this.form.spacer = res.result[0].timeInterval + ''\r\n                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second\r\n                break\r\n                case 'week':\r\n                  this.dayOfWeeks = res.result[0].dayOfWeeks\r\n                  this.dayOfWeeks.forEach(x => {\r\n                    if (x == '1') {\r\n                      this.showWeeks.push('星期一')\r\n                    } else if (x == '2') {\r\n                      this.showWeeks.push('星期二')\r\n                    } else if (x == '3') {\r\n                      this.showWeeks.push('星期三')\r\n                    } else if (x == '4') {\r\n                      this.showWeeks.push('星期四')\r\n                    } else if (x == '5') {\r\n                      this.showWeeks.push('星期五')\r\n                    } else if (x == '6') {\r\n                      this.showWeeks.push('星期六')\r\n                    } else if (x == '7') {\r\n                      this.showWeeks.push('星期日')\r\n                    }\r\n                  })\r\n                  this.form.weekTime = this.showWeeks.toString()\r\n                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second\r\n                break\r\n                case 'month':\r\n                  this.dayOfMonths = res.result[0].dayOfMonths\r\n                  this.form.specificTime = res.result[0].hour + ':' + res.result[0].minute + ':' + res.result[0].second\r\n                  this.dayOfMonths.forEach(x => {\r\n                    this.showMonths.push(x + '号')\r\n                  })\r\n                  this.form.dayTime = this.showMonths.toString()\r\n                break\r\n              }\r\n            } else if (this.dispatchWay === 'SINGLE') {\r\n              this.form.executeTime = res.result[0].startTime\r\n            } else if (this.dispatchWay === 'CRON') {\r\n              this.continuous = res.result[0].endTime ? false : true\r\n              this.form.effectStartTime = res.result[0].startTime\r\n              this.form.effectEndTime = res.result[0].endTime ? res.result[0].endTime : ''\r\n              this.form.cron = res.result[0].cronExp\r\n            }\r\n          } else {\r\n            this.isAdd = true\r\n          }\r\n        }\r\n      })\r\n    },\r\n    executeCron() {\r\n      // this.$refs.ruleForm.\r\n      this.$refs.ruleForm.validateField(\"cron\", errMsg => {\r\n        if (!errMsg) {\r\n          commonCron({cronExp: this.form.cron, testSize: this.testNum}).then((res) => {\r\n            if (res.code === 200) {\r\n              this.elapsedTableData = []\r\n              res.result.forEach(x => {\r\n                this.elapsedTableData.push({executeTime: x})\r\n              })\r\n            }\r\n          })\r\n        }\r\n      });\r\n    },\r\n    async cronCheck(rule, value, callback) {\r\n      if (!value) {\r\n        return callback(new Error('请输入cron表达式'))\r\n      }\r\n      if (value.trim() === '') {\r\n        callback(new Error('请输入cron表达式'))\r\n      }\r\n      const res = await cronValid({cronExp: value})\r\n      if (res.result === true) {\r\n        callback()\r\n      } else {\r\n        callback(new Error('请输入正确的cron表达式'))\r\n      }\r\n    },\r\n    effectStartTimeCheck(rule, value, callback){\r\n      if (!value) {\r\n        return callback(new Error('请选择开始时间'))\r\n      }\r\n      if (!this.continuous) {\r\n        if (!this.form.effectEndTime) {\r\n          return callback(new Error('请选择截止时间'))\r\n        } else {\r\n          if (new Date(this.form.effectEndTime).getTime() <= new Date(value).getTime()) {\r\n            return callback(new Error('截止时间不能小于或等于开始时间'))\r\n          } else {\r\n            callback()\r\n          }\r\n        }\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    dispatchWayChange(val) {\r\n      this.$refs.ruleForm.clearValidate()\r\n      if (this.lastSaveData) {\r\n        if (val === this.lastSaveData.strategyType) {\r\n          if (val === 'ROUND') {\r\n            this.form.dispatchCycle = this.lastSaveData.scheduleConfig.period.toLowerCase()\r\n            this.continuous = this.lastSaveData.scheduleConfig.endTime ? false : true\r\n            this.form.effectStartTime = this.lastSaveData.scheduleConfig.startTime\r\n            this.form.effectEndTime = this.lastSaveData.scheduleConfig.endTime ? this.lastSaveData.scheduleConfig.endTime : ''\r\n            switch(this.form.dispatchCycle) {\r\n              case 'minute':\r\n                this.form.scopeStartTime = this.lastSaveData.scheduleConfig.hourRange.split(',')[0]\r\n                this.form.scopeEndTime = this.lastSaveData.scheduleConfig.hourRange.split(',')[1]\r\n                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[0]\r\n                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[1]\r\n                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval\r\n                this.form.specificTime = this.lastSaveData.scheduleConfig.second + ''\r\n              break\r\n              case 'hour':\r\n                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[0]\r\n                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.minuteRange.split(',')[1]\r\n                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval\r\n                this.form.specificTime = this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second\r\n              break\r\n              case 'day':\r\n                this.form.intervalStartTime = this.lastSaveData.scheduleConfig.dayRange.split(',')[0]\r\n                this.form.intervalEndTime = this.lastSaveData.scheduleConfig.dayRange.split(',')[1]\r\n                this.form.spacer = this.lastSaveData.scheduleConfig.timeInterval\r\n                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second\r\n              break\r\n              case 'week':\r\n                this.dayOfWeeks = this.lastSaveData.scheduleConfig.dayOfWeeks\r\n                this.dayOfWeeks.forEach(x => {\r\n                  if (x == '1') {\r\n                    this.showWeeks.push('星期一')\r\n                  } else if (x == '2') {\r\n                    this.showWeeks.push('星期二')\r\n                  } else if (x == '3') {\r\n                    this.showWeeks.push('星期三')\r\n                  } else if (x == '4') {\r\n                    this.showWeeks.push('星期四')\r\n                  } else if (x == '5') {\r\n                    this.showWeeks.push('星期五')\r\n                  } else if (x == '6') {\r\n                    this.showWeeks.push('星期六')\r\n                  } else if (x == '7') {\r\n                    this.showWeeks.push('星期日')\r\n                  }\r\n                })\r\n                this.form.weekTime = this.showWeeks.toString()\r\n                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second\r\n              break\r\n              case 'month':\r\n                this.dayOfMonths = this.lastSaveData.scheduleConfig.dayOfMonths\r\n                this.form.specificTime = this.lastSaveData.scheduleConfig.hour + ':' + this.lastSaveData.scheduleConfig.minute + ':' + this.lastSaveData.scheduleConfig.second\r\n                this.dayOfMonths.forEach(x => {\r\n                  this.showMonths.push(x + '号')\r\n                })\r\n                this.form.dayTime = this.showMonths.toString()\r\n              break\r\n            }\r\n          } else if (val === 'SINGLE') {\r\n            document.getElementsByTagName('body')[0].className = ''\r\n            this.form.executeTime = new Date(this.lastSaveData.scheduleConfig.startTime)\r\n          } else if (val === 'CRON') {\r\n            document.getElementsByTagName('body')[0].className = ''\r\n            // this.continuous = this.lastSaveData.continuedFlag === 'CONTINUED' ? true : false\r\n            this.continuous = this.lastSaveData.scheduleConfig.endTime ? false : true\r\n            this.form.effectStartTime = this.lastSaveData.scheduleConfig.startTime\r\n            this.form.effectEndTime = this.lastSaveData.scheduleConfig.endTime ? this.lastSaveData.scheduleConfig.endTime : ''\r\n            this.form.cron = this.lastSaveData.scheduleConfig.cronExp\r\n          }\r\n        } else {\r\n          document.getElementsByTagName('body')[0].className = ''\r\n          if (val === 'SINGLE') {\r\n            this.form.executeTime = ''\r\n          } else if (val === 'ROUND') {\r\n            this.form.dispatchCycle = ''\r\n            this.continuous = true\r\n            this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\");\r\n            this.form.effectEndTime = ''\r\n          } else if (val === 'CRON') {\r\n            this.continuous = true\r\n            this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\");\r\n            this.form.effectEndTime = ''\r\n            this.form.cron = ''\r\n            this.testNum = 10\r\n            this.elapsedTableData = []\r\n          }\r\n        }\r\n      } else {\r\n        document.getElementsByTagName('body')[0].className = ''\r\n        if (val === 'SINGLE') {\r\n          this.form.executeTime = ''\r\n        } else if (val === 'ROUND') {\r\n          this.form.dispatchCycle = ''\r\n          this.continuous = true\r\n          this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\");\r\n          this.form.effectEndTime = ''\r\n        } else if (val === 'CRON') {\r\n          this.continuous = true\r\n          this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\");\r\n          this.form.effectEndTime = ''\r\n          this.form.cron = ''\r\n          this.testNum = 10\r\n          this.elapsedTableData = []\r\n        }\r\n      }\r\n    },\r\n    dateFocus() {\r\n      this.bodyClass = JSON.parse(JSON.stringify(document.getElementsByTagName('body')[0].className))\r\n      document.getElementsByTagName('body')[0].className = ''\r\n    },\r\n    dateFocus1() {\r\n      document.getElementsByTagName('body')[0].className = ''\r\n    },\r\n    dateBlur() {\r\n      document.getElementsByTagName('body')[0].className = this.bodyClass\r\n    },\r\n    scopeStartTimeCheck(rule, value, callback){\r\n      if (!value) {\r\n        return callback(new Error('请选择范围起始时间'))\r\n      }\r\n      if (!this.form.scopeEndTime) {\r\n        return callback(new Error('请选择范围结束时间'))\r\n      }\r\n      if (parseInt(this.form.scopeEndTime) < parseInt(value)) {\r\n        return callback(new Error('范围结束时间必须大于等于起始时间'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    intervalStartTimeCheck(rule, value, callback){\r\n      if (!value) {\r\n        return callback(new Error('请选择间隔起始时间'))\r\n      }\r\n      if (!this.form.intervalEndTime) {\r\n        return callback(new Error('请选择间隔结束时间'))\r\n      }\r\n      if (!this.form.spacer) {\r\n        return callback(new Error('请选择间隔时间段'))\r\n      }\r\n      if (parseInt(this.form.intervalEndTime) <= parseInt(value)) {\r\n        return callback(new Error('间隔结束时间必须大于起始时间'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    dispatchCycleChange(val) {\r\n      this.$refs.ruleForm.clearValidate()\r\n      if (val === 'minute') {\r\n        this.form.spacer = '30'\r\n      } else if (val === 'hour' || val === 'day') {\r\n        this.form.spacer = '1'\r\n      } else {\r\n        this.form.spacer = ''\r\n      }\r\n      if (val === 'day' || val === 'week' || val === 'month') {\r\n        document.getElementsByTagName('body')[0].className = ''\r\n        this.form.specificTime = '00:00:00'\r\n      } else if (val === 'hour') {\r\n        document.getElementsByTagName('body')[0].className = 'mmss'\r\n        this.form.specificTime = '00:00'\r\n      } else {\r\n        document.getElementsByTagName('body')[0].className = 'ss'\r\n        this.form.specificTime = '00'\r\n      }\r\n      if (val === 'minute') {\r\n        this.form.intervalStartTime = '0'\r\n        this.form.intervalEndTime = '59'\r\n      } else if (val === 'hour') {\r\n        this.form.intervalStartTime = '0'\r\n        this.form.intervalEndTime = '23'\r\n      } else if (val === 'day') {\r\n        this.form.intervalStartTime = '1'\r\n        this.form.intervalEndTime = '31'\r\n      }\r\n    },\r\n    continuousChange(val) {\r\n      if (val) {\r\n        this.form.effectEndTime = ''\r\n      }\r\n    },\r\n    currentClass(item) {\r\n      return [this.dayOfWeeks.indexOf(item.value) > -1 ? 'ccc' : '']\r\n    },\r\n    currentClassDay(item) {\r\n      return [this.dayOfMonths.indexOf(item.value) > -1 ? 'ccc' : '']\r\n    },\r\n    weekClick(item) {\r\n      var index = this.dayOfWeeks.indexOf(item.value)\r\n      if (index < 0) {\r\n        this.showWeeks.push(item.label)\r\n        this.dayOfWeeks.push(item.value)\r\n      } else {\r\n        this.showWeeks.splice(index, 1)\r\n        this.dayOfWeeks.splice(index, 1)\r\n      }\r\n      this.form.weekTime = this.showWeeks.toString()\r\n    },\r\n    dayClick(item) {\r\n      var index = this.dayOfMonths.indexOf(item.value)\r\n      if (index < 0) {\r\n        this.showMonths.push(item.label)\r\n        this.dayOfMonths.push(item.value)\r\n      } else {\r\n        this.showMonths.splice(index, 1)\r\n        this.dayOfMonths.splice(index, 1)\r\n      }\r\n      this.form.dayTime = this.showMonths.toString()\r\n    },\r\n    empty(formName) {\r\n      if (!this.scheduleId) {\r\n        this.$refs[formName].resetFields()\r\n        this.$message({\r\n          type: 'success',\r\n          message: '清空成功'\r\n        })\r\n        return \r\n      }\r\n      scheduleClear({workflowId: this.workflowId, scheduleId: this.scheduleId}).then((res) => {\r\n        if (res.code === 200) {\r\n          this.isAdd = true\r\n          this.$refs[formName].clearValidate()\r\n          if (this.dispatchWay === 'SINGLE') {\r\n            this.form.executeTime = ''\r\n          } else if (this.dispatchWay === 'ROUND') {\r\n            this.form.dispatchCycle = ''\r\n            this.continuous = true\r\n            this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\")\r\n            this.form.effectEndTime = ''\r\n            setTimeout(() => {\r\n              this.$refs[formName].clearValidate()\r\n            }, 0);\r\n          } else if (this.dispatchWay === 'CRON') {\r\n            this.continuous = true\r\n            this.form.effectStartTime = this.$moment(Date.now()).format(\"YYYY-MM-DD HH:mm:ss\");\r\n            this.form.effectEndTime = ''\r\n            this.form.cron = ''\r\n            this.testNum = 10\r\n            this.elapsedTableData = []\r\n          }\r\n          this.lastSaveData = ''\r\n          this.$message({\r\n            type: 'success',\r\n            message: '清空成功'\r\n          })\r\n        }\r\n      })\r\n     \r\n    },\r\n    submitForm(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          const param = this.getParam()\r\n          if (this.isAdd) {\r\n            scheduleAdd(param, this.workflowId).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message({\r\n                  type: 'success',\r\n                  message: '保存成功'\r\n                })\r\n                this.scheduleId = res.result\r\n                bus.$emit('scheduleId', this.scheduleId)\r\n                this.isAdd = false\r\n                this.lastSaveData = param\r\n              }\r\n              \r\n            })\r\n          } else {\r\n            scheduleUpdate(param, this.workflowId, this.scheduleId).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message({\r\n                  type: 'success',\r\n                  message: '保存成功'\r\n                })\r\n                this.isAdd = false\r\n                this.lastSaveData = param\r\n              }\r\n            })\r\n          }\r\n          \r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    getParam() {\r\n      var obj = {}\r\n      // obj.executeWay = this.dispatchWay\r\n      // obj.workflowId = this.workflowId\r\n      // obj.scheduleConfig = {}\r\n      obj.strategyType = this.dispatchWay\r\n      // if (this.dispatchWay !== 'ROUND') {\r\n      //   obj.continuedFlag = 'INIT'\r\n      // } else {\r\n      //   if (this.continuous) {\r\n      //     obj.continuedFlag = 'CONTINUED'\r\n      //   } else {\r\n      //     obj.continuedFlag = 'INIT'\r\n      //   }\r\n      // }\r\n      if (this.dispatchWay === 'SINGLE') {\r\n        obj.startTime = this.form.executeTime\r\n      } else if (this.dispatchWay === 'ROUND') {\r\n        obj.startTime = this.form.effectStartTime\r\n        obj.endTime = this.form.effectEndTime\r\n        obj.period = this.form.dispatchCycle.toUpperCase()\r\n        switch(this.form.dispatchCycle) {\r\n          case 'minute':\r\n            obj.hourRange = this.form.scopeStartTime + ',' + this.form.scopeEndTime\r\n            obj.minuteRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime\r\n            obj.timeInterval = this.form.spacer\r\n            obj.second = this.form.specificTime\r\n          break\r\n          case 'hour':\r\n            obj.minuteRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime\r\n            obj.timeInterval = this.form.spacer\r\n            obj.minute = this.form.specificTime.split(':')[0]\r\n            obj.second = this.form.specificTime.split(':')[1]\r\n          break\r\n          case 'day':\r\n            obj.dayRange = this.form.intervalStartTime + ',' + this.form.intervalEndTime\r\n            obj.timeInterval = this.form.spacer\r\n            obj.hour = this.form.specificTime.split(':')[0]\r\n            obj.minute = this.form.specificTime.split(':')[1]\r\n            obj.second = this.form.specificTime.split(':')[2]\r\n          break\r\n          case 'week':\r\n            obj.dayOfWeeks = this.dayOfWeeks\r\n            obj.hour = this.form.specificTime.split(':')[0]\r\n            obj.minute = this.form.specificTime.split(':')[1]\r\n            obj.second = this.form.specificTime.split(':')[2]\r\n          break\r\n          case 'month':\r\n            obj.dayOfMonths = this.dayOfMonths\r\n            obj.hour = this.form.specificTime.split(':')[0]\r\n            obj.minute = this.form.specificTime.split(':')[1]\r\n            obj.second = this.form.specificTime.split(':')[2]\r\n          break\r\n        }\r\n      } else if (this.dispatchWay === 'CRON') {\r\n        obj.startTime = this.form.effectStartTime\r\n        obj.endTime = this.form.effectEndTime\r\n        // obj.period = this.form.dispatchCycle.toUpperCase()\r\n        obj.cronExp = this.form.cron\r\n      }\r\n      return obj\r\n    }\r\n  }\r\n  \r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// ::v-deep .ss{\r\n//   .el-time-spinner.has-seconds .el-time-spinner__wrapper{\r\n//     background: aquamarine;\r\n//   }\r\n//   .el-time-spinner:first-child{\r\n//     display: none;\r\n//   }\r\n//   .el-time-spinner:nth-child(2) {\r\n//     width: 50%;\r\n//   }\r\n// }\r\n::v-deep .right-config-form{\r\n  padding:16px 24px;\r\n  .el-radio{\r\n    // display: block;\r\n    margin-bottom: 16px;\r\n  }\r\n  .el-radio__input.is-checked .el-radio__inner{\r\n    border-color: #276EB7;\r\n    background: #276EB7;\r\n  }\r\n  .el-radio__input.is-checked+.el-radio__label{\r\n    color: #276EB7;\r\n  }\r\n  .el-button--primary{\r\n    background-color: #276EB7;\r\n    border-color: #276EB7;\r\n  }\r\n  .el-button--primary.is-plain{\r\n    color: #409EFF;\r\n    background: #ecf5ff;\r\n    border-color: #b3d8ff;\r\n  }\r\n  .el-form--label-top .el-form-item__label{\r\n    padding: 0;\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 15px;\r\n  }\r\n  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{\r\n    background-color: #276EB7;\r\n    border-color: #276EB7;\r\n  }\r\n  .el-checkbox__input.is-checked+.el-checkbox__label{\r\n    color: #276EB7;\r\n  }\r\n  .dateClass{\r\n    // .el-input__inner{\r\n    //   padding: 0 0 0 20px !important\r\n    // }\r\n    // .el-input__prefix{\r\n    //   left: 0;\r\n    // }\r\n    // .el-input__suffix{\r\n    //   right: -4px\r\n    // }\r\n  }\r\n  .itemAline{\r\n    display: flex;\r\n    .el-form-item__label{\r\n      width: 43px;\r\n    }\r\n  }\r\n}\r\n.configTitle{\r\n  font-size: 16px;\r\n  font-family: PingFangSC-Medium, PingFang SC;\r\n  font-weight: 800;\r\n  color: #303133;\r\n  margin-bottom: 24px;\r\n}\r\n.configWay{\r\n  font-size: 16px;\r\n  font-family: PingFangSC-Regular, PingFang SC;\r\n  font-weight: 400;\r\n  color: #303133;\r\n  margin-bottom: 20px;\r\n}\r\n.dropdownBox{\r\n    width: 300px;\r\n    padding: 10px;\r\n  }\r\n.dropdownTitle{\r\n  text-align: center;\r\n  font-size: 16px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #DCDFE6;\r\n}\r\n.chooseStyle{\r\n  margin: 8px 25px;\r\n}\r\n.labelStyle{\r\n  cursor: pointer;\r\n}\r\n.daychooseStyle{\r\n  margin: 8px 12px;\r\n}\r\n.daylabelStyle{\r\n  width: 20px;\r\n  cursor: pointer;\r\n}\r\n.ccc{\r\n  color:#1570C4;\r\n}\r\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-dispatch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-dispatch.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./right-config-dispatch.vue?vue&type=template&id=1400914e&scoped=true&\"\nimport script from \"./right-config-dispatch.vue?vue&type=script&lang=js&\"\nexport * from \"./right-config-dispatch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./right-config-dispatch.vue?vue&type=style&index=0&id=1400914e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1400914e\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"log-bar\",style:({ transform: _vm.transformHeight, width: _vm.logBarWidth })},[(_vm.getShowDragLine)?_c('span',{staticClass:\"log-header drag-line\",on:{\"mousemove\":_vm.drag,\"mousedown\":_vm.dragstart,\"mouseup\":_vm.dragend}},[_c('span',{staticClass:\"title\",on:{\"click\":_vm.clicklogBar}},[_vm._v(\"运行日志\"),_c('img',{attrs:{\"src\":_vm.logIcon,\"alt\":\"\"}}),(_vm.getErrComponentNum !== 0)?_c('span',{staticClass:\"err-tag\"},[_c('i',{staticClass:\"el-icon-error c-error\"}),_vm._v(_vm._s(_vm.getErrComponentNum))]):_vm._e()])]):_c('span',{staticClass:\"log-header\"},[_c('span',{staticClass:\"title\",on:{\"click\":_vm.clicklogBar}},[_vm._v(\"运行日志\"),_c('img',{attrs:{\"src\":_vm.logIcon,\"alt\":\"\"}}),(_vm.getErrComponentNum !== 0)?_c('span',{staticClass:\"err-tag\"},[_c('i',{staticClass:\"el-icon-error c-error\"}),_vm._v(_vm._s(_vm.getErrComponentNum))]):_vm._e()])]),(_vm.showLogContainer)?_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"logBarContainer\",staticClass:\"logbar-container\"},[_c('el-tabs',{attrs:{\"type\":\"card\"},model:{value:(_vm.activeName),callback:function ($$v) {_vm.activeName=$$v},expression:\"activeName\"}},[_c('el-tab-pane',{attrs:{\"lazy\":\"\",\"label\":\"流程日志\",\"name\":\"processLog\"}},[_c('ProcessLog',{ref:\"processLogRef\",attrs:{\"taskId\":_vm.taskId}})],1),_c('el-tab-pane',{attrs:{\"lazy\":\"\",\"label\":\"组件日志\",\"name\":\"componentLog\"}},[_c('ComponentLog',{ref:\"componentLogRef\",attrs:{\"taskId\":_vm.taskId}})],1)],1)],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('ul',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"processContainerRef\",staticClass:\"process-container\"},[(_vm.$store.state.processLogs.length !== 0)?_c('div',_vm._l((_vm.$store.state.processLogs),function(item){return _c('li',{key:item.uuId},[_vm._v(_vm._s(item.ouput))])}),0):_c('el-empty',{staticClass:\"empty\",attrs:{\"image-size\":40,\"description\":\"暂无流程日志信息\"}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div')}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!-- 日志基类 -->\r\n<template>\r\n  <div></div>\r\n</template>\r\n<script >\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n    };\r\n  },\r\n  methods: {\r\n    // 滚动到底部\r\n    scrollToPosition(el) {\r\n      el.scrollTo({\r\n        top: el.scrollHeight,\r\n        behavior: 'smooth',\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang='scss'>\r\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-base.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-base.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./log-base.vue?vue&type=template&id=6cfaaa74&\"\nimport script from \"./log-base.vue?vue&type=script&lang=js&\"\nexport * from \"./log-base.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "<!-- 流程日志 -->\r\n<template>\r\n  <ul class=\"process-container\" v-loading=\"loading\" ref=\"processContainerRef\">\r\n    <div v-if=\"$store.state.processLogs.length !== 0\">\r\n      <li v-for=\"item in $store.state.processLogs\" :key=\"item.uuId\">{{item.ouput}}</li>\r\n    </div>\r\n    <el-empty class=\"empty\" v-else :image-size=\"40\" description=\"暂无流程日志信息\"></el-empty>\r\n  </ul>\r\n</template>\r\n<script >\r\nimport LogBase from './log-base.vue';\r\nexport default {\r\n  data() {\r\n    return {\r\n      logs: [],\r\n    };\r\n  },\r\n  extends: LogBase,\r\n  watch: {\r\n    '$store.state.processLogs'(val) {\r\n      if(!val || val.length === 0) return\r\n      this.$nextTick(() => {\r\n        this.scrollToPosition(this.$refs.processContainerRef);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n  },\r\n};\r\n</script>\r\n<style lang='scss' scoped>\r\n.process-container {\r\n  height: 100%;\r\n  overflow: auto;\r\n  border-radius: 1px;\r\n  border: 1px solid #dcdfe6;\r\n  font-size: 12px;\r\n  font-weight: 400;\r\n  color: #1a1b1d;\r\n  padding: 16px;\r\n  li {\r\n    line-height: 17px;\r\n    white-space: pre;\r\n  }\r\n  .empty {\r\n    height: 100%;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./process-log.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./process-log.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./process-log.vue?vue&type=template&id=7d3ec378&scoped=true&\"\nimport script from \"./process-log.vue?vue&type=script&lang=js&\"\nexport * from \"./process-log.vue?vue&type=script&lang=js&\"\nimport style0 from \"./process-log.vue?vue&type=style&index=0&id=7d3ec378&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7d3ec378\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"components-logs\"},[(_vm.$store.state.componentLogs.moduleExecuteLogDTOList.length !== 0)?_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"componentsTableRef\",staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.$store.state.componentLogs.moduleExecuteLogDTOList,\"border\":\"\",\"size\":\"small\",\"height\":\"100%\"}},[_c('el-table-column',{attrs:{\"prop\":\"moduleName\",\"label\":\"节点名称\",\"min-width\":\"120px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"executeState\",\"label\":\"运行状态\",\"min-width\":\"80px\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{class:['dot', (\"b-\" + (_vm.componentMapping[scope.row.executeState].icon))]}),_c('span',[_vm._v(_vm._s(_vm.componentMapping[scope.row.executeState].label))])]}}],null,false,2712548496)}),_c('el-table-column',{attrs:{\"prop\":\"exeStartTime\",\"label\":\"开始时间\",\"min-width\":\"112px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"exeEndTime\",\"label\":\"结束时间\",\"min-width\":\"112px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"exeResult\",\"label\":\"操作结果\",\"min-width\":\"169px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"address\",\"label\":\"操作\",\"min-width\":\"82px\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('LogDetails',{attrs:{\"row\":scope.row}}),_c('span',{staticClass:\"opt-text\",staticStyle:{\"margin-left\":\"12px\"},on:{\"click\":function($event){return _vm.locationComponent(scope.row)}}},[_vm._v(\"定位\")])]}}],null,false,1465766631)})],1):_c('el-empty',{staticClass:\"empty\",attrs:{\"image-size\":40,\"description\":\"暂无组件日志信息\"}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',[_c('span',{staticClass:\"opt-text\",on:{\"click\":function($event){_vm.dialogVisible = true}}},[_vm._v(\"详情\")]),_c('el-dialog',{attrs:{\"title\":\"日志详情\",\"visible\":_vm.dialogVisible,\"width\":\"53%\",\"destroy-on-close\":\"\",\"close-on-click-modal\":false,\"append-to-body\":\"\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('el-descriptions',{attrs:{\"column\":1,\"labelStyle\":{ minWidth: '66px' }}},[_c('el-descriptions-item',{attrs:{\"label\":\"节点名称\"}},[_vm._v(_vm._s(_vm.row.moduleName))]),_c('el-descriptions-item',{attrs:{\"label\":\"运行状态\"}},[_vm._v(_vm._s(_vm.componentMapping[_vm.row.executeState].label)+\" \")]),_c('el-descriptions-item',{attrs:{\"label\":\"开始时间\"}},[_vm._v(_vm._s(_vm.row.exeStartTime))]),_c('el-descriptions-item',{attrs:{\"label\":\"结束时间\"}},[_vm._v(_vm._s(_vm.row.exeEndTime))]),_c('el-descriptions-item',{attrs:{\"label\":\"任务耗时\"}},[_vm._v(\" \"+_vm._s(_vm.getTaskConsumeTime(_vm.row.timeConsuming)))]),_c('el-descriptions-item',{attrs:{\"label\":\"日志详情\"}},[_c('div',{staticClass:\"log-details\"},[_vm._v(_vm._s(_vm.row.exeResult))])])],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "// 公共方法\r\nexport function getConsumeTime(ms) {\r\n  if (!ms || ms < 0) return ''\r\n  // 计算有多少小时\r\n  const h = Math.trunc(ms / (60 * 60 * 1000))\r\n  // 计算有多少分钟\r\n  const mms = ms - h * 60 * 60 * 1000\r\n  const m = Math.ceil(mms / (60 * 1000))\r\n  return ((h === 0 ? '' : (h + '小时')) + m + '分钟')\r\n}", "<!-- 日志详情dialog -->\r\n<template>\r\n  <span>\r\n    <span class=\"opt-text\" @click='dialogVisible = true'>详情</span>\r\n    <el-dialog title='日志详情' :visible.sync='dialogVisible' width='53%' destroy-on-close :close-on-click-modal='false'\r\n      append-to-body>\r\n      <el-descriptions :column=\"1\" :labelStyle=\"{ minWidth: '66px' }\">\r\n        <el-descriptions-item label=\"节点名称\">{{ row.moduleName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"运行状态\">{{ componentMapping[row.executeState].label }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"开始时间\">{{ row.exeStartTime }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"结束时间\">{{ row.exeEndTime }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"任务耗时\"> {{ getTaskConsumeTime(row.timeConsuming) }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"日志详情\">\r\n          <div class=\"log-details\">{{ row.exeResult }}</div>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </el-dialog>\r\n  </span>\r\n</template>\r\n<script>\r\nimport { componentMapping } from '@/libs/types.js'\r\nimport { getConsumeTime } from '@/libs/utils.js'\r\nexport default {\r\n  props: {\r\n    row: {\r\n      required: true,\r\n      type: Object,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      componentMapping\r\n    };\r\n  },\r\n  computed: {\r\n    getTaskConsumeTime() {\r\n      return (ms) => {\r\n        return getConsumeTime(ms)\r\n      }\r\n    }\r\n  },\r\n  components: {},\r\n  methods: {\r\n    cancel() {\r\n      this.dialogVisible = false;\r\n    },\r\n    submit() {\r\n      this.dialogVisible = false;\r\n    },\r\n    openDialog() {\r\n      this.dialogVisible = true;\r\n      this.nextTick(() => { });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang='scss' scoped>\r\n::v-deep .el-descriptions-item__label {\r\n  min-width: 60px;\r\n}\r\n\r\n.log-details {\r\n  max-height: 350px;\r\n  overflow: auto;\r\n  padding: 16px;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9e9eb;\r\n  white-space: pre;\r\n  width: calc(53vw - 138px);\r\n}\r\n\r\n::v-deep .el-descriptions__body {\r\n  padding: 12px;\r\n}\r\n\r\n::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {\r\n  padding-bottom: 24px !important;\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-details.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./log-details.vue?vue&type=template&id=8004cd4a&scoped=true&\"\nimport script from \"./log-details.vue?vue&type=script&lang=js&\"\nexport * from \"./log-details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./log-details.vue?vue&type=style&index=0&id=8004cd4a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8004cd4a\",\n  null\n  \n)\n\nexport default component.exports", "<!-- 组件日志 -->\r\n<template>\r\n  <div class=\"components-logs\">\r\n    <el-table ref=\"componentsTableRef\" v-if=\"$store.state.componentLogs.moduleExecuteLogDTOList.length !== 0\"\r\n      :data=\"$store.state.componentLogs.moduleExecuteLogDTOList\" style=\"width: 100%\" border size=\"small\"\r\n      v-loading=\"loading\" height=\"100%\">\r\n      <el-table-column prop=\"moduleName\" label=\"节点名称\" min-width=\"120px\" show-overflow-tooltip>\r\n      </el-table-column>\r\n      <el-table-column prop=\"executeState\" label=\"运行状态\" min-width=\"80px\">\r\n        <template slot-scope=\"scope\">\r\n          <span :class=\"['dot', `b-${componentMapping[scope.row.executeState].icon}`]\"></span>\r\n          <span>{{ componentMapping[scope.row.executeState].label }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"exeStartTime\" label=\"开始时间\" min-width=\"112px\" show-overflow-tooltip>\r\n      </el-table-column>\r\n      <el-table-column prop=\"exeEndTime\" label=\"结束时间\" min-width=\"112px\" show-overflow-tooltip>\r\n      </el-table-column>\r\n      <el-table-column prop=\"exeResult\" label=\"操作结果\" min-width=\"169px\" show-overflow-tooltip>\r\n      </el-table-column>\r\n      <el-table-column prop=\"address\" label=\"操作\" min-width=\"82px\">\r\n        <template slot-scope=\"scope\">\r\n          <LogDetails :row=\"scope.row\" />\r\n          <span class=\"opt-text\" @click=\"locationComponent(scope.row)\" style=\"margin-left: 12px\">定位</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-empty class=\"empty\" v-else :image-size=\"40\" description=\"暂无组件日志信息\">\r\n    </el-empty>\r\n  </div>\r\n</template>\r\n<script >\r\nimport LogDetails from './log-details.vue';\r\nimport LogBase from './log-base.vue';\r\nimport { bus } from '@/libs/bus';\r\nimport { componentMapping } from '@/libs/types.js'\r\nexport default {\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      logDetaisVisibility: false,\r\n      componentMapping,\r\n    };\r\n  },\r\n  extends: LogBase,\r\n  components: {\r\n    LogDetails,\r\n  },\r\n  methods: {\r\n    clickDetails() {\r\n      this.logDetaisVisibility = true;\r\n    },\r\n    // 定位\r\n    locationComponent(row) {\r\n      if (!row.moduleId) return this.$message.error('组件ID不能为空')\r\n      bus.$emit('highlight', row.moduleId);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang='scss' scoped>\r\n.components-logs {\r\n  height: 100%;\r\n\r\n  .dot {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    display: inline-block;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .empty {\r\n    height: 100%;\r\n    border: 1px #dcdfe6 solid;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./component-log.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./component-log.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./component-log.vue?vue&type=template&id=5631bc13&scoped=true&\"\nimport script from \"./component-log.vue?vue&type=script&lang=js&\"\nexport * from \"./component-log.vue?vue&type=script&lang=js&\"\nimport style0 from \"./component-log.vue?vue&type=style&index=0&id=5631bc13&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5631bc13\",\n  null\n  \n)\n\nexport default component.exports", "<!-- 日志栏组件 -->\r\n<template>\r\n  <section class=\"log-bar\" :style=\"{ transform: transformHeight, width: logBarWidth }\">\r\n    <!-- <div class=\"drag-line\" v-if=\"getShowDragLine\" draggable=\"true\" @mousemove=\"drag\"\r\n      @mousedown=\"dragstart\" @mouseup=\"dragend\"></div> -->\r\n    <span v-if=\"getShowDragLine\" class=\"log-header drag-line\" @mousemove=\"drag\" @mousedown=\"dragstart\"\r\n      @mouseup=\"dragend\">\r\n      <span class=\"title\" @click=\"clicklogBar\">运行日志<img :src=\"logIcon\" alt=\"\" />\r\n        <span class=\"err-tag\" v-if=\"getErrComponentNum !== 0\">\r\n          <i class=\"el-icon-error c-error\"></i>{{ getErrComponentNum }}</span>\r\n      </span>\r\n    </span>\r\n    <span v-else class=\"log-header\">\r\n      <span class=\"title\" @click=\"clicklogBar\">运行日志<img :src=\"logIcon\" alt=\"\" />\r\n        <span class=\"err-tag\" v-if=\"getErrComponentNum !== 0\">\r\n          <i class=\"el-icon-error c-error\"></i>{{ getErrComponentNum }}</span>\r\n      </span>\r\n    </span>\r\n    <div v-if=\"showLogContainer\" class=\"logbar-container\" ref=\"logBarContainer\" v-loading=\"loading\">\r\n      <el-tabs v-model=\"activeName\" type=\"card\">\r\n        <el-tab-pane lazy label=\"流程日志\" name=\"processLog\">\r\n          <ProcessLog :taskId=\"taskId\" ref=\"processLogRef\" />\r\n        </el-tab-pane>\r\n        <el-tab-pane lazy label=\"组件日志\" name=\"componentLog\">\r\n          <ComponentLog :taskId=\"taskId\" ref=\"componentLogRef\" />\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </div>\r\n  </section>\r\n</template>\r\n<script >\r\nimport ProcessLog from './process-log.vue';\r\nimport ComponentLog from './component-log.vue';\r\nimport { componentMapping } from '@/libs/types.js';\r\nexport default {\r\n  data() {\r\n    return {\r\n      move: false,\r\n      logIcon: require('@/assets/images/log.png'),\r\n      transformHeight: 'translateY(0px)',\r\n      activeName: 'processLog',\r\n      taskId: '',\r\n      loading: false,\r\n      // 拖拽前el-tabs__content高度\r\n      oldHeight: 200,\r\n      // 日志容器显隐\r\n      showLogContainer: false,\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算logBar宽度\r\n    logBarWidth() {\r\n      return this.$store.state.rightConfigActiveName === ''\r\n        ? 'calc(100vw - 231px)'\r\n        : 'calc(100vw - 594px)';\r\n    },\r\n    // DragLine显隐\r\n    getShowDragLine() {\r\n      if (this.showLogContainer && this.transformHeight === 'translateY(0px)') {\r\n        document.body.addEventListener('mouseup', this.mouseupClose)\r\n      } else {\r\n        document.body.removeEventListener('mouseup', this.mouseupClose)\r\n      }\r\n      return (\r\n        this.showLogContainer && this.transformHeight === 'translateY(0px)'\r\n      );\r\n    },\r\n    getErrComponentNum() {\r\n      const componentLogs = this.$store.state.componentLogs.moduleExecuteLogDTOList;\r\n      if (!componentLogs || componentLogs.length === 0) return 0;\r\n      const errComps = componentLogs.filter(item => {\r\n        return item.executeState === componentMapping.FAIL.value\r\n      });\r\n      return errComps.length;\r\n    },\r\n  },\r\n  components: {\r\n    ProcessLog,\r\n    ComponentLog,\r\n  },\r\n  methods: {\r\n    mouseupClose() {\r\n      const tabsContent = document.querySelector('.log-bar .el-tabs__content');\r\n      this.oldHeight = tabsContent.clientHeight;\r\n      this.move = false\r\n    },\r\n    async clicklogBar() {\r\n      if (!this.showLogContainer) {\r\n        this.showLogContainer = true;\r\n        this.$store.commit('setLogBarIsOpen', true);\r\n      } else {\r\n        // 关闭\r\n        if (this.transformHeight === 'translateY(0px)') {\r\n          this.$store.commit('setLogBarIsOpen', false);\r\n          this.transformHeight = `translateY(${this.$refs.logBarContainer.clientHeight}px)`;\r\n        } else {\r\n          // 打开\r\n          this.$store.commit('setLogBarIsOpen', true);\r\n          this.transformHeight = 'translateY(0px)';\r\n        }\r\n      }\r\n    },\r\n    drag(e) {\r\n      e.preventDefault();\r\n      if (e.clientY === 0 || !this.move) return;\r\n      // e.dataTransfer.dropEffect = 'move';\r\n      const dragsVal = e.clientY - this.dragstartVal;\r\n      const tabsContent = document.querySelector('.log-bar .el-tabs__content');\r\n      tabsContent.style.height = this.oldHeight - dragsVal + 'px';\r\n    },\r\n    dragstart(e) {\r\n      this.move = true\r\n      this.dragstartVal = e.clientY;\r\n    },\r\n    dragend() {\r\n      this.move = false\r\n      const tabsContent = document.querySelector('.log-bar .el-tabs__content');\r\n      this.oldHeight = tabsContent.clientHeight;\r\n      // this.oldHeight = tabsContent.style.height.slice(0, tabsContent.style.height.length - 2);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang='scss' scoped>\r\n.log-bar {\r\n  position: absolute;\r\n  bottom: 0px;\r\n  z-index: 2;\r\n  left: 232px;\r\n  transition: 0.5s;\r\n  background: #fff;\r\n\r\n  .drag-line {\r\n\r\n    // width: 100%;\r\n    // height: 5px;\r\n    // transition: 0s 0.3s;\r\n    // background: transparent;\r\n    &:hover {\r\n      // background: #409eff;\r\n      // border-bottom: 3px #409eff solid;\r\n      cursor: row-resize;\r\n    }\r\n  }\r\n\r\n  .log-header {\r\n    -webkit-user-select: none;\r\n    font-size: 14px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #303133;\r\n    line-height: 22px;\r\n    display: flex;\r\n    align-items: center;\r\n    background: #f5f7fa;\r\n    padding: 5px 16px;\r\n\r\n    // user-select: none;\r\n    .title {\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      img {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-left: 8px;\r\n      }\r\n\r\n      .err-tag {\r\n        margin-left: 8px;\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .logbar-container {\r\n    padding: 12px 16px;\r\n  }\r\n\r\n  ::v-deep .el-tabs__content {\r\n    height: 200px;\r\n    min-height: 100px;\r\n    max-height: 60vh;\r\n  }\r\n\r\n  ::v-deep .el-tabs__item {\r\n    height: 36px;\r\n    line-height: 36px;\r\n    padding: 0px 16px;\r\n  }\r\n\r\n  .el-tab-pane {\r\n    height: 100%;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-bar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./log-bar.vue?vue&type=template&id=ba0de2ca&scoped=true&\"\nimport script from \"./log-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./log-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./log-bar.vue?vue&type=style&index=0&id=ba0de2ca&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ba0de2ca\",\n  null\n  \n)\n\nexport default component.exports", "<template>\r\n  <div class=\"home\">\r\n    <!-- <div class=\"task-tab-header\">\r\n      <em class=\"el-icon-back arrow-left\"></em>\r\n      <span class=\"task-name\"> 任务名称</span>\r\n    </div> -->\r\n     <menuBar />\r\n     \r\n    <div id=\"canvas-wrapper\" class=\"flex-wrapper\">\r\n      <nodeSelector />\r\n      <canvasToolbox />\r\n      <graphCanvas />\r\n\r\n      <rightConfigBar />\r\n      <rightConfigResource />\r\n      <rightConfigDispatch />\r\n      <logBar/>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n import menuBar from '@/components/menuBar.vue';\r\n import nodeSelector from '@/components/nodeSelector.vue';\r\n import canvasToolbox from '@/components/canvasToolbox.vue';\r\n import graphCanvas from '@/components/graphCanvas.vue';\r\n import rightConfigBar from '@/components/workflowConfig/right-config-bar.vue';\r\n import rightConfigResource from '@/components/workflowConfig/right-config-resource.vue';\r\n import rightConfigDispatch from '@/components/workflowConfig/right-config-dispatch.vue';\r\n import logBar from '@/components/workflowConfig/log-bar/log-bar.vue'\r\nexport default {\r\n  name: 'Home',\r\n  components: {\r\n    menuBar,\r\n    nodeSelector,\r\n    canvasToolbox,\r\n    graphCanvas,\r\n    rightConfigBar,\r\n    rightConfigResource,\r\n    rightConfigDispatch,\r\n    logBar\r\n  },\r\n  \r\n  mounted() {\r\n    document.querySelector('#canvas-wrapper').addEventListener('contextmenu', this.canvasPreventDefault)\r\n  },\r\n  methods: {\r\n    canvasPreventDefault(event) {\r\n      event.preventDefault();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    document.querySelector('#canvas-wrapper').removeEventListener('contextmenu', this.canvasPreventDefault)\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\r\n  .home {\r\n    height: 100%;\r\n  }\r\n  .task-tab-header {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 34px;\r\n    line-height: 34px;\r\n    white-space: nowrap;\r\n    background-color: #f2f5fb;\r\n    border-bottom: 1px solid #dee1e5;\r\n    .arrow-left {\r\n      cursor: pointer;\r\n      margin: 0 5px;\r\n    }\r\n  }\r\n\r\n  .flex-wrapper {\r\n    display: flex;\r\n    position: relative;\r\n    height: calc(100vh - 50px);\r\n    overflow: hidden;\r\n  }\r\n\r\n  .right-config-panel {\r\n    position: absolute;\r\n    top: 0;\r\n    right: -376px;\r\n    bottom: 0;\r\n    z-index: 2;\r\n    width: 360px;\r\n    background: #fff;\r\n    // box-shadow: -6px 10px 40px 0 rgb(0 0 0 / 10%);\r\n    transition: all .5s cubic-bezier(.4,0,.2,1) 0s;\r\n  }\r\n  .right-config-form {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 1;\r\n    overflow: auto;\r\n  }\r\n\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Home.vue?vue&type=template&id=185a6570&\"\nimport script from \"./Home.vue?vue&type=script&lang=js&\"\nexport * from \"./Home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Home.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"text-align\":\"center\",\"padding\":\"100px 0\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-circle-plus\"},on:{\"click\":function($event){_vm.dialogVisible = true}}},[_vm._v(\"新建流程\")]),_c('el-dialog',{attrs:{\"append-to-body\":true,\"close-on-click-modal\":false,\"modal-append-to-body\":false,\"visible\":_vm.dialogVisible,\"width\":\"200px\"},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('div',{staticClass:\"header-title\",attrs:{\"slot\":\"title\"},slot:\"title\"},[_c('span',{staticClass:\"tit\"},[_vm._v(\"新建流程\")])]),_c('div',[_c('el-form',{ref:\"ruleForm\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.ruleForm,\"rules\":_vm.rules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"流程名称\",\"prop\":\"name\"}},[_c('el-input',{model:{value:(_vm.ruleForm.name),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"name\", $$v)},expression:\"ruleForm.name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"流程标签\",\"prop\":\"category\"}},[_c('el-select',{attrs:{\"placeholder\":\"请选择\"},model:{value:(_vm.ruleForm.category),callback:function ($$v) {_vm.$set(_vm.ruleForm, \"category\", $$v)},expression:\"ruleForm.category\"}},[_c('el-option',{attrs:{\"label\":\"mysql\",\"value\":\"MYSQL\"}}),_c('el-option',{attrs:{\"label\":\"spark\",\"value\":\"SPARK\"}})],1)],1)],1)],1),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"info\"},on:{\"click\":_vm.resetForm}},[_vm._v(\"取消\")]),_c('el-button',{directives:[{name:\"popover\",rawName:\"v-popover:popover\",arg:\"popover\"}],attrs:{\"type\":\"primary\"},on:{\"click\":_vm.submitForm}},[_vm._v(\"确认\")])],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div style=\"text-align: center; padding: 100px 0;\">\r\n    <el-button type=\"primary\" icon=\"el-icon-circle-plus\" @click=\"dialogVisible = true\">新建流程</el-button>\r\n    <el-dialog :append-to-body=\"true\" :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :visible.sync=\"dialogVisible\" width=\"200px\">\r\n      <div class=\"header-title\" slot=\"title\">\r\n        <span class=\"tit\">新建流程</span>\r\n      </div>\r\n      <div>\r\n\r\n        <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\" class=\"demo-ruleForm\">\r\n          <el-form-item label=\"流程名称\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"流程标签\" prop=\"category\">\r\n            <el-select v-model=\"ruleForm.category\" placeholder=\"请选择\">\r\n              <el-option label=\"mysql\" value=\"MYSQL\"></el-option>\r\n              <el-option label=\"spark\" value=\"SPARK\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n      </div>\r\n      <span class=\"dialog-footer\" slot=\"footer\">\r\n        <el-button @click=\"resetForm\" type=\"info\">取消</el-button>\r\n        <el-button @click=\"submitForm\" type=\"primary\" v-popover:popover>确认</el-button>\r\n\r\n      </span>\r\n\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { workflowAdd } from '@/api/https.js'\r\nexport default {\r\n  name: 'workflowAdd',\r\n  data() {\r\n    return {\r\n      // workflowId: window.dataOsToken,\r\n      dialogVisible: false,\r\n      ruleForm: {\r\n        name: '',\r\n        category: ''\r\n      },\r\n      rules: {\r\n        name: [\r\n          { required: true, message: '请输入流程名称', trigger: 'blur' }\r\n        ],\r\n        category: [\r\n          { required: true, message: '请选择流程标签', trigger: 'change' }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      this.$refs['ruleForm'].validate(async (valid) => {\r\n        if (valid) {\r\n          const params = this.ruleForm\r\n          const res = await workflowAdd(params)\r\n          if (res.code == 200) {\r\n            const { category, name, workflowId } = res.result\r\n            this.$router.push({\r\n              path: '/home',\r\n              query: {\r\n                accountToken: window.dataOsToken || '1ea958ae55a94c1787f0f67e9248b83e000000',\r\n                workflowId,\r\n                category,\r\n                name\r\n              }\r\n            })\r\n          }\r\n        }\r\n      });\r\n    },\r\n    resetForm() {\r\n      this.dialogVisible = false\r\n      this.$refs['ruleForm'].resetFields();\r\n    }\r\n  }\r\n}\r\n</script>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./workflowAdd.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./workflowAdd.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./workflowAdd.vue?vue&type=template&id=4dd373e2&\"\nimport script from \"./workflowAdd.vue?vue&type=script&lang=js&\"\nexport * from \"./workflowAdd.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\nimport Home from '../views/Home.vue'\r\nimport workflowAdd from '../views/workflowAdd.vue'\r\n\r\nVue.use(VueRouter)\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    redirect: self == top ? '/workflowAdd' : '/home'\r\n  },\r\n  {\r\n    path: '/home',\r\n    name: 'Home',\r\n    component: Home\r\n  },\r\n  {\r\n    path: '/workflowAdd',\r\n    name: 'workflowAdd',\r\n    component: workflowAdd\r\n  }\r\n]\r\n\r\nconst router = new VueRouter({\r\n  basePath: '/workflowAdd',\r\n  routes\r\n})\r\n\r\nexport default router\r\n", "import Vue from 'vue'\r\nimport Vuex from 'vuex'\r\n\r\nVue.use(Vuex)\r\n\r\nexport default new Vuex.Store({\r\n  state: {\r\n    nodeSelectorHide: false,\r\n    rightConfigActiveName: '',\r\n    taskId: '',\r\n    // 组件日志信息\r\n    componentLogs: [],\r\n    // 流程日志信息\r\n    processLogs: [],\r\n    // 日志栏是否打开\r\n    logBarIsOpen: false\r\n  },\r\n  mutations: {\r\n    nodeSelectorHideToggle(state, val) {\r\n      state.nodeSelectorHide = val\r\n    },\r\n    rightConfigActiveNameChange(state, val) {\r\n      if (state.rightConfigActiveName === val) {\r\n        state.rightConfigActiveName = ''\r\n      }\r\n      else {\r\n        state.rightConfigActiveName = val\r\n      }\r\n    },\r\n    setTaskId(state, val) {\r\n      state.taskId = val\r\n    },\r\n    setComponentLogs(state, val) {\r\n      state.componentLogs = val\r\n    },\r\n    setProcessLogs(state, val) {\r\n      state.processLogs = val\r\n    },\r\n    setLogBarIsOpen(state, val) {\r\n      state.logBarIsOpen = val\r\n    },\r\n  },\r\n  actions: {\r\n  },\r\n  modules: {\r\n  }\r\n})\r\n", "import Vue from 'vue'\r\n/*\r\n  按钮防抖动指令\r\n*/\r\nVue.directive('debounce', {\r\n  inserted: function (el, binding) {\r\n    let [fn, event = \"click\", time = 300] = binding.value\r\n    let timer\r\n    el.addEventListener(event, () => {\r\n      timer && clearTimeout(timer)\r\n      timer = setTimeout(() => fn(), time)\r\n    })\r\n  }\r\n})\r\n\r\nVue.directive('throttle', {\r\n  inserted: function (el, binding) {\r\n    let [fn, event = \"click\", time = 300] = binding.value\r\n    let timer, timer_end;\r\n    el.addEventListener(event, () => {\r\n      if (timer) {\r\n        clearTimeout(timer_end);\r\n        return timer_end = setTimeout(() => fn(), time);\r\n      }\r\n      fn();\r\n      timer = setTimeout(() => timer = null, time)\r\n    })\r\n  }\r\n})", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('svg',{staticClass:\"svg-icon\",attrs:{\"aria-hidden\":\"true\"}},[_c('use',{attrs:{\"xlink:href\":(\"#icon-\" + _vm.name),\"fill\":_vm.color}})])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <svg class=\"svg-icon\" aria-hidden=\"true\">\r\n    <use :xlink:href=\"`#icon-${name}`\" :fill=\"color\" />\r\n  </svg>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'SvgIcon',\r\n  props: {\r\n    name: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.svg-icon {\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: -0.15em;\r\n  fill: currentColor;\r\n  overflow: hidden;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SvgIcon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--13-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SvgIcon.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SvgIcon.vue?vue&type=template&id=1c395d35&scoped=true&\"\nimport script from \"./SvgIcon.vue?vue&type=script&lang=js&\"\nexport * from \"./SvgIcon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./SvgIcon.vue?vue&type=style&index=0&id=1c395d35&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1c395d35\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport '@/assets/style/base.scss'\r\nimport '@/assets/style/common.scss'\r\nimport ElementUI from 'element-ui';\r\nimport 'element-ui/lib/theme-chalk/index.css';\r\nimport moment from 'moment'//导入文件\r\nimport '@/libs/development.js'\r\nimport '@/libs/directive.js'\r\nimport SvgIcon from '@/components/SvgIcon'// svg组件\r\n\r\n// 全局注册SvgIcon组件\r\nVue.component('svg-icon', SvgIcon)\r\n// 载入所有svg icon\r\nconst requireContext = require.context('./assets/icons', false, /\\.svg$/)\r\nrequireContext.keys().forEach(requireContext)\r\n\r\nVue.config.productionTip = false\r\nVue.use(ElementUI);\r\nVue.prototype.$moment = moment;//赋值使用\r\nmoment.locale('zh-cn');//需要汉化\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAYxJREFUWEftlz9Lw0AYxp/30oprEQqCbm4VNMm1Qxf/DKJObjo5+gEc/QCC38FJEHR0qjqUboptUh2Ki7gUcVEcFdvkkf5Ri0tJSlOH3Jg73t/vnhyXvAIAlpXdJPw9QDIkjdazYQ0R8QDWBGrfdcun0oL79E8AvAmkAqA5LHi3boKgBpBSorbEtOw7EtOGQsZxnOchw9vlbdue9HzURFAX09JNEMVqtbISBfybYZr6EoJlmTdtCqRQrVbWIxWwdIHkaizwTxIQqRM8j/IMgLIGcKqdQKTgP7CugJSSCdmOUqTR5BHAxX9yBuJ7IE4gTiBkAmY2uyCejLvuzUXQO8Qc9GvY+Y3jMUkFhZ1bxzkMIjGQwC8cLx0o00ElQgv0wpUklgzDU03PL5GYCCIRSiCfz6ffPz6fSLy24K57fd/av5nLzbHhFwGmkgk1Uy6XH/u9jlACJMXSelc4dua6Vw+9EK31rOdxQykcOI7TGIpAv6JB5kMlEATQb20s8JPAyBuTkbdmI29OR92efwFXgH3MWZMAfwAAAABJRU5ErkJggg==\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./process-log.vue?vue&type=style&index=0&id=7d3ec378&lang=scss&scoped=true&\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shape.vue?vue&type=style&index=0&id=2227f8da&lang=scss&scoped=true&\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAC7UlEQVRYR+2XvU9TURjGn5c29WOC8hWdiaiLbkajMTDooInukADB9pKwWGAzapDECagLSU8rBBL+AE100EExGA2bLiqya/hogQGjpPSYt7fX3p577kdDExg8W9v3nud3nvfjnhIOeNEB66NqABmLnUIodBtSXgNwFkBj6RBZAF9A9Bp7e88ok/ke5HCBAWQsdhV1dWMArgTZGMAiCoX7lMm884r3BZC9vUcRiTwBURyo2jEJKdPY3b1Ls7O/dSCeALK/P4pw+AWAiwFP7Rb2Efn8TZqezqkBrgAyHj8OojcALuxT3Hp8CVJ2Ujr9y76fO8DAQAZS3qmRuLkN0VNKpWK+AKWCe+vIeUsL0NUFzMwA29t6tvp6oK8PmJ8H1tfVGIlCocNemFoHpGEsArhc8TSLJxJAQwOwugokk8DWVqUAi3NMayuwuWnGrK2pEO9JiH+d5ACQhnEOwCfH8UZGgLa28tcqBIsPDQEMaq2VFWBiQufUeRLiczErDo8MYxTAA8dT0agp0NRU/olPNzlpflbFNzZMcXbCuR6REA/dALjyO7QJZvuHhyshWIiXHcxbnKMXSIiihs6BHwBO6CsMZg2oEPZgLjx2RX9yK/InCXHSDeAPgIgrAP/AEFwTjdZroBSdzQLj437iHLxLQhw5tAD+KeCCa27Wm1SDFHgXoSrOtvOyp8MfwrMI9W3IeVfFLSGrDe2ueEEQjVEqVWz14IOIJ1x7e9l2VUAHuLxsTkPnch9EHKsdxdzn7AAPJB5AvLHaanaIXM5sR2tOlCG8R3ERwLz9OF9GDNHdDczNubcaQ/T0mC8jp7gEUSelUgsWj/vr2DAEAL4F1XKlSQjDvuHhvZAUUzE42Ih8/mUNbkVLCIdv0NRUqWfLHvhfShOJY9jZSe7rUgoMqVcx3xpQE18szFDoMaS8FKgoiD4AuGcvON1zvg44QOLx0yC6BeA6gDPKH5OvAF5ByueUTn8LAlo1QJBNq4n5D3DgDvwFB3UqMKe88voAAAAASUVORK5CYII=\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./graphCanvas.vue?vue&type=style&index=0&lang=scss&\"", "var map = {\n\t\"./auto_layout.svg\": \"21d1\",\n\t\"./funnel.svg\": \"c929\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"a244\";", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./menuBar.vue?vue&type=style&index=0&lang=scss&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-dispatch.vue?vue&type=style&index=0&id=1400914e&lang=scss&scoped=true&\"", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./canvasToolbox.vue?vue&type=style&index=0&lang=scss&\"", "import SpriteSymbol from \"../../../node_modules/svg-baker-runtime/browser-symbol.js\";\nimport sprite from \"../../../node_modules/svg-sprite-loader/runtime/browser-sprite.build.js\";\nvar symbol = new SpriteSymbol({\n  \"id\": \"icon-funnel\",\n  \"use\": \"icon-funnel-usage\",\n  \"viewBox\": \"0 0 1024 1024\",\n  \"content\": \"<symbol class=\\\"icon\\\" viewBox=\\\"0 0 1024 1024\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" id=\\\"icon-funnel\\\"><path d=\\\"M221.78789744 920.38454701c-15.71774359 0-28.4991453-12.78249572-28.49914531-28.4991453s12.78140171-28.50023931 28.49914531-28.50023932h28.99145299V732.11186325c0-88.83309402 74.38659829-147.01948718 128.70345299-189.51111112 14.95521367-11.70160684 33.45723077-26.17545299 42.02119658-35.78201708l4.02160684-4.51500856-4.20102564-4.34652991c-8.86700854-9.1580171-27.59111111-22.61770941-42.6425983-33.43425641-54.56300854-39.20957265-129.26905983-92.90393162-129.26905982-178.6945641V160.61483761h-27.63049573a28.53305983 28.53305983 0 0 1-28.50023932-28.4991453 28.53305983 28.53305983 0 0 1 28.4991453-28.50023932h580.44170941a28.53305983 28.53305983 0 0 1 28.50023931 28.4991453 28.52758974 28.52758974 0 0 1-28.4991453 28.50023932h-27.63158974v125.22447863c0 85.80157265-74.71699146 139.49046154-129.26358975 178.6945641-15.07336752 10.83295726-33.79309402 24.28717949-42.6371282 33.43425641l-4.20649573 4.34762394 4.02707693 4.51391452c8.55302564 9.60109402 27.03316239 24.0574359 41.88663248 35.67589744 54.44594872 42.58680342 128.8259829 100.76225641 128.8259829 189.61723077v131.27329914h29.0045812a28.53305983 28.53305983 0 0 1 28.50461539 28.49476923 28.33176069 28.33176069 0 0 1-8.33969231 20.15835898 28.31535043 28.31535043 0 0 1-20.15398291 8.34735043h-580.45264957z m84.62550427-634.54523077c0 56.56287179 58.51131624 98.6114188 105.52888888 132.40451282 39.70844444 28.53962393 71.06516239 51.07418803 71.0651624 83.89251282 0 31.85449572-30.18064957 55.45900854-68.38700855 85.35302564-47.61162393 37.23815385-106.8351453 83.56758974-106.83514529 144.62249573v131.26782906h408.44034188V732.11186325c0-61.0494359-59.22352136-107.38434188-106.81217095-144.60499146-38.22386325-29.91152136-68.40451282-53.52150428-68.40451282-85.37709402 0-32.81176069 31.35671795-55.34632479 71.05422223-83.87938461 47.02851282-33.79418803 105.53982906-75.84820513 105.53982905-132.41107692V160.61483761H306.41449572v125.22447863z\\\" p-id=\\\"14891\\\" /><path d=\\\"M412.28820513 765.81538462c-13.6 0-24.66461538-11.06461538-24.66461539-24.66564103s11.06461538-24.66461538 24.66461539-24.66461538h199.42871795c13.6 0 24.66461538 11.06461538 24.66461538 24.66461538s-11.06461538 24.66666667-24.66461538 24.66666667H412.28717949z\\\" p-id=\\\"14892\\\" /></symbol>\"\n});\nvar result = sprite.add(symbol);\nexport default symbol", "export * from \"-!../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SvgIcon.vue?vue&type=style&index=0&id=1c395d35&scoped=true&lang=css&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./right-config-resource.vue?vue&type=style&index=0&lang=scss&\"", "var map = {\n\t\"./icon-default.png\": \"eed1\",\n\t\"./icon-inOperation.png\": \"ff38\",\n\t\"./icon-noConfigure.png\": \"efd7\",\n\t\"./icon-runFailed.png\": \"8870\",\n\t\"./icon-runSuccessfully.png\": \"fb93\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"d88e\";", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OTVGRDQ5ODM3MTZEMTFFQUI3ODVDQ0YyNTU3MzQ1RTQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OTVGRDQ5ODQ3MTZEMTFFQUI3ODVDQ0YyNTU3MzQ1RTQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo5NUZENDk4MTcxNkQxMUVBQjc4NUNDRjI1NTczNDVFNCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo5NUZENDk4MjcxNkQxMUVBQjc4NUNDRjI1NTczNDVFNCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Ph6uBBAAAAEzSURBVHjaYvy/kwEbcADiACA2B2J1IGYC4ttAfAqINwPxDrhKt/9gihHNICkgngLEgQz4AcigAiC+CTOICUlSF4ivE2EICHgA8TUgtoEJwAwShDqbD4um00B8FIs4SO9hhl2M8iAOC1RwBRBz4LDdHoi/A/F/HPKbgFgfFEYWQMZxPN7gB+JPeAwCAT+Q8+IIhAcnEWGWADLIioFyYAAySJUKBokzoSUBbOAfEQYxggy5Q0DRayIMeg2K/hNArINH0Uog/krAoIsgFy0noMgaiF0IqFkGy2tH8cQeI5TGlY7uAPObKixlhwHxExwK10FTNi4QiJ77bYF4PxAzkxDtPkDXbEXP/YehJcBRIgw4C8SgrLUVJsCCpuA6tGgIgRZshtAyCmThMyC+BC3YlqCbDBBgAAyqPa++NflmAAAAAElFTkSuQmCC\"", "module.exports = \"data:image/png;base64,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\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACHElEQVRYR+2Wu04bQRSG/2OS0GClS1IHGkqEXdFEu467MDINghegQLxChF8hcpMHQLFSJGsrFciTKk0QLpMC6BEdokLAnmhYsL2XueyA5CZb2d4z5//meHe/JUz5oCnn41EAHDUaICYSgwPfjXgDMIPQDw7vgldlnQjsA+EPEIXrIO4mobRBYnD/uRyGFwB/Xn6ONy//Api/jzvF2cUibR1dl4uH3zXAUbgN4k4mbIeEzP5m5Sk9Ae6tVIHZYwCvM93PgasFEr8urakTBeUBovAjiNuakF0SUneucEkpAP7WfIWZmxMAVQ3AJW6fLdDa/rnrFMoBRMEnEHaSW2+QzuiHD987JGRS43A4A/D3YB4V/AHwwgJwjRiL1JKnDvnudwH3gz0wNkdN9RNQJV9IyHGtgcRpAtxrLAHxkXriOAIwKqjRBzm0TcERINgH8D7VzDwBVXpAQjYfDZAIJ87Lxg6gBta0ico4gUQ44W+Aa7mdOAFgiFVZM4nKDJASTgbBDcAqKi1AgXDSBM4AMIpKD1AsnDGEO4BaoxVVIQB/fTeH2Yp65GaFY7uodee1oioGMAvHF6JNQu5mF+cAHIST9Cj3F6gVhaLKA0wKx7TX8gCqW05UKQD+0XiL21i9aiXCeXqAnKjSAFnhPD2A6tglITceWo8ACoVjm4LfeUbMdWr9VHIb2417BcLxC3BZNRKVkw1dOvrW/AeY+gT+AVBCxyE8E+moAAAAAElFTkSuQmCC\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADXklEQVRYR+2XTUhUURTH/3dGnIwInDEkyZlWUW2SGUtGx49RUUstTaWtlKl9CCbZl1iU0LagiPwINBNT36iVBrVQw6diOGKbipah4yjUIiIbcbxxR32N47yPUcEW3d1799zz/91zz7nvPIItHmSL9REwwKkJyz63m+QANA3AQQC65U18A/ARIG/VatrdFsV/UbI5xQAF4/GJlNIaAPFKHAMYJIRUdxgH30nZywIU9idt+7lz4T6AYiDgiFEAdTt+BJU3Wgd++wORBCgYNmupJqgHoGaFuxYxIyPEtZDVETvy3ddAFCB7zLRdQ0L6AMRsTFxYPeqic8mvou2/vP2JAuSPx9WDkqJNEl9yQ2gDZxw6KwuwnHD96zhzOV5KCLF6J6bfCOTbLYMALHLe5OYJCAr3lGFm3oHXs9yKOc+ZeKGS1gDkjlsOqSkm5JzLzTPx05HlyNh1EhQUTZMP0Tvb7lnmJojqMvIfPKfi6yhvPO42oeSmnIB0bRMU6SuQFpYjmPXMtqNp8oHnmRJ6x2YcuuUXoMBu6aOAdb0AbOfF+kqkhmULLl7OtKJ56tFflwQDnJH3aKyJQL7d4gCw2xfAHGpFeHAEumdaRNkIVCg1XEGyLlOwYfYtU49910xzJj5CDMAFINh7hSU0FWV7q6EiKrQ66tDpbF4DwebO6a8hSXdUmGN2zN7PmOdMvEYxABNP0LJvz9Jom34CbrpReGbiFww3kKBNF97ZnE147mgQi5YkwJojYAIXDVWI94JgAAyEzTFAFiUxQD8U4kcgloRM6LzhOhK1GYK/LmczwjURiA1NEd61OurR6XwqncNSSShVhgyiVH8VVt0xvwItjlp0O5/JFxAhNZxx0FPqAV9ELNNL9JVICctaJcTKjJWbkiF5ETEHclfxUq1fRmrYcY8eu+V6ZtuUaDMb6auYWSj5GDGIM5GX4HB99b7n5SAoFpHMHeYHVgxFP8d5dkstWeqCNm1QoM5m4ku8Hf67DQmjzB09olMHBfduQlc06l6Yz+yKec8651VDtiktGDaHUI363kaaUhedq/BtxWRzwJd0OTHvAohVmBTDWESVd8L5WycbAd9FeWPm/USlPkEo0ilwwPvHhACfKMEbuuh+YYse+awENGAAJU4DsfkPsOUR+APhwjUwx18RGwAAAABJRU5ErkJggg==\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--9-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--1-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./log-details.vue?vue&type=style&index=0&id=8004cd4a&lang=scss&scoped=true&\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAB20lEQVRYR92XzS4DURTHz+m0k9amEgtr4jnKAglewArTCNGxsLCSaKeVWFlYaIVEBysvgAQL+hoS1hZENyrTTq/c0cZkOvejrWZU1+ec/++eM+ejCAH/MGB96F8APWue0uzlM9pSN1nsOAOprPlOhQsZbfD/Aei7Z0NV67NybKx+sF4nk4EV42jABiV6Yiy/seK0lICKk6p9SxDLdt2aY0GIAKi4ElIvkZA4RpSp/Nbiqx9ECwB1DKN6DQAJgnDPguAB/IjDBACUasSaYT3E9yNsQFwBwDgLggXg9hWJ04wwu8AT6KFGrFn3K/wARD5SJXAb8QJ6AToR52agCcIKrOfMDWcQpbX9TsWlAKiRS2CMEJI4NJJP7kytGcVRRCwRhEde57RdAm85VCU2fLC98OwXaH3nfMSyKy+82dEVQDfjlufrdEEqa14AwDTXEGEzn9aKPBs9ZyYJgT0B7E0ho803bf4GQK/SKxNXeh3TTgjsIwy0DdsdRLwF1nYbBjqKReO1p8tIJN6YHb43oYyvuxSsg8S5BQCgZQ03nUUHSRhVYQzfZfR9ktXvAKDsvQHc5DInWQMijpHQpPRJRkV+8yiNqNEYS1x6Hfu1jygDMlOwvwEC/2smm2KR3RdJr5ow1fSUzAAAAABJRU5ErkJggg==\""], "sourceRoot": ""}