<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
  <script>
    function GetQueryValue(queryName) {
      var reg = new RegExp("(^|&)" + queryName + "=([^&]*)(&|$)", "i");
      var r = window.location.href.split('?')[1]?.match(reg);
      if (r != null) {
        return decodeURI(r[2]);
      } else {
        return null;
      }
    }
    window.dataOsToken = GetQueryValue("accountToken");
    window.accountId = GetQueryValue("accountId");
    window.applicationCode = GetQueryValue("applicationCode");

  </script>
  <script src="./lib/env.js"></script>
</body>

</html>